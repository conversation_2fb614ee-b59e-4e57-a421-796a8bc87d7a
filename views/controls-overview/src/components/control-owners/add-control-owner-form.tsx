import { sharedControlOwnersController } from '@controllers/controls';
import { sharedUsersInfiniteController } from '@controllers/users';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getFullName } from '@helpers/formatters';
import { Form, type FormValues } from '@ui/forms';

export const AddControlOwnerForm = observer(
    ({
        onSubmit,
        formRef,
    }: {
        onSubmit: (values: FormValues) => void;
        formRef: React.RefObject<HTMLFormElement>;
    }): React.JSX.Element => {
        const { options, hasNextPage, isFetching, isLoading, onFetchUsers } =
            sharedUsersInfiniteController;
        const { controlOwners } = sharedControlOwnersController;

        return (
            <Stack direction="column" gap="6x" data-id="aDJtorjV">
                <Text>
                    <Trans>
                        The control owner ensures that the control is kept up to
                        date and accurate. They also receive notifications about
                        the control.
                    </Trans>
                </Text>
                <Form
                    hasExternalSubmitButton
                    ref={formRef}
                    formId="add-control-owner-form"
                    data-id="add-control-owner-form"
                    schema={{
                        owners: {
                            type: 'combobox',
                            label: t`Control owners`,
                            loaderLabel: t`Loading results`,
                            removeAllSelectedItemsLabel: t`Clear all`,
                            getSearchEmptyState: () => t`No users found`,
                            isMultiSelect: true,
                            options,
                            hasMore: hasNextPage,
                            isLoading: isFetching && isLoading,
                            onFetchOptions: onFetchUsers,
                            isOptional: true,
                            initialValue: controlOwners.map((owner) => ({
                                id: owner.id.toString(),
                                label: getFullName(
                                    owner.firstName,
                                    owner.lastName,
                                ),
                                value: owner.id.toString(),
                            })),
                        },
                    }}
                    onSubmit={onSubmit}
                />
            </Stack>
        );
    },
);
