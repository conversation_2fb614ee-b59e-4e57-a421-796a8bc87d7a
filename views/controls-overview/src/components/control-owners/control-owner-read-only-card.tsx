import { isEmpty } from 'lodash-es';
import { sharedControlOwnersController } from '@controllers/controls';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { AvatarStack } from '@cosmos-lab/components/avatar-stack';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getFullName, getInitials } from '@helpers/formatters';

export const ControlOwnerReadOnlyCard = observer((): React.JSX.Element => {
    const { controlOwners } = sharedControlOwnersController;

    const MAX_NUMBER_OWNERS = 10;

    if (isEmpty(controlOwners)) {
        return (
            <Metadata
                label={t`Unassigned`}
                data-id="unassigned-tag"
                type="tag"
                colorScheme="neutral"
            />
        );
    }

    return (
        <Stack
            direction="column"
            gap="2x"
            data-testid="renderContent"
            data-id="z_jQH_T_"
        >
            <AvatarStack
                maxVisibleItems={MAX_NUMBER_OWNERS}
                data-testid="AssetsOwnerCellComponent"
                data-id="kg8sm-9C"
                avatarData={controlOwners.map((owner) => ({
                    fallbackText: getInitials(
                        getFullName(owner.firstName, owner.lastName),
                    ),
                    primaryLabel: getFullName(owner.firstName, owner.lastName),
                    imgSrc: owner.avatarUrl ?? undefined,
                }))}
            />
        </Stack>
    );
});
