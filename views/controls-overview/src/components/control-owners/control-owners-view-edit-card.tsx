import { ViewEditCardComponent } from '@components/view-edit-card';
import { sharedControlOwnersMutationController } from '@controllers/controls';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useFormSubmit } from '@ui/forms';
import { sharedControlOwnersCardModel } from '../../models/control-owners-card.model';
import { AddControlOwnerForm } from './add-control-owner-form';
import { ControlOwnerReadOnlyCard } from './control-owner-read-only-card';

export const ControlOwnersViewEditCard = observer((): React.JSX.Element => {
    const { formRef, triggerSubmit } = useFormSubmit();
    const { handleOnSubmitOwners } = sharedControlOwnersCardModel;
    const { isUpdating, hasError } = sharedControlOwnersMutationController;
    const { hasWriteControlPermission } = sharedFeatureAccessModel;

    return (
        <ViewEditCardComponent
            title={t`Owners`}
            data-testid="ControlsOwnersCard"
            editButtonLabel={t`Assign`}
            data-id="control-owners-card"
            isMutationPending={isUpdating}
            hasMutationError={hasError}
            size="md"
            readOnlyComponent={<ControlOwnerReadOnlyCard />}
            editComponent={
                hasWriteControlPermission ? (
                    <AddControlOwnerForm
                        formRef={formRef}
                        onSubmit={handleOnSubmitOwners}
                    />
                ) : null
            }
            onSave={triggerSubmit}
        />
    );
});
