import { isEmpty } from 'lodash-es';
import { REVIEW_APPROVAL_STATUS, StatsBlock } from '@components/controls';
import { sharedControlApprovalsController } from '@controllers/controls';
import { Button } from '@cosmos/components/button';
import {
    criticalBackgroundStrongInitial,
    successBackgroundModerate,
} from '@cosmos/constants/tokens';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { controlReviewCardModel } from '../control-review-approval/models/control-review-card.model';

export const ControlApprovalsReadinessCard = observer((): React.JSX.Element => {
    const { hasWriteControlPermission } = sharedFeatureAccessModel;
    const { isLoading, controlApprovals, currentControlApproval } =
        sharedControlApprovalsController;
    const controlReviewers = controlApprovals
        .map((approval) => approval.lastReview?.reviewer)
        .filter(Boolean);
    const isApprovalsEmpty = isEmpty(controlReviewers);

    const emptyStateProps = {
        title: t`Require specific people to approve a control before it's ready.`,
        ...(hasWriteControlPermission && {
            leftAction: (
                <Button
                    label={t`Set up approvals`}
                    level="secondary"
                    onClick={action(() => {
                        controlReviewCardModel.handleEditClick();
                    })}
                />
            ),
        }),
    };
    const isApprovalsReady =
        currentControlApproval?.approvalStatus ===
        REVIEW_APPROVAL_STATUS.COMPLETED;
    const statusLabel = isApprovalsReady ? t`Ready` : t`Not ready`;
    const iconName = isApprovalsReady ? 'CheckCircle' : 'NotReady';
    const iconColor = isApprovalsReady ? 'success' : 'critical';
    const legendValues = isApprovalsEmpty
        ? []
        : [
              {
                  label: isApprovalsReady ? t`Approved` : t`Needs approval`,
                  value: 1,
                  color: isApprovalsReady
                      ? successBackgroundModerate
                      : criticalBackgroundStrongInitial,
              },
          ];

    return (
        <StatsBlock
            data-id="approvals-stats-block"
            data-testid="ApprovalsStatsBlock"
            title={t`Approvals`}
            isLoading={isLoading}
            iconName={iconName}
            iconColor={iconColor}
            statusLabel={statusLabel}
            legendValues={legendValues}
            emptyStateProps={emptyStateProps}
        />
    );
});
