import { useEffect } from 'react';
import { Box } from '@cosmos/components/box';
import { Card } from '@cosmos/components/card';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useFormSubmit } from '@ui/forms';
import { ControlApprovalsEditComponent } from './components/control-approvals-edit.component';
import { ControlApprovalsReadComponent } from './components/control-approvals-read.component';
import { controlReviewCardModel } from './models/control-review-card.model';

export const ControlsOverviewReviewCardComponent = observer(
    (): React.JSX.Element => {
        const { formRef, triggerSubmit } = useFormSubmit();
        const { isEditing, actions } = controlReviewCardModel;

        useEffect(() => {
            controlReviewCardModel.triggerSubmit = triggerSubmit;
        }, [triggerSubmit]);

        return (
            <Box gridRow="1" gridColumn="2" width="100%" data-id="mMACPn9w">
                <Card
                    title={t`Review and approval`}
                    isEditMode={isEditing}
                    actions={actions}
                    body={
                        isEditing ? (
                            <ControlApprovalsEditComponent formRef={formRef} />
                        ) : (
                            <ControlApprovalsReadComponent />
                        )
                    }
                />
            </Box>
        );
    },
);
