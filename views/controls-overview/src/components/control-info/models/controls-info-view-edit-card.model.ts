import {
    sharedControlCustomFieldsController,
    sharedControlDetailsController,
    sharedControlsUpdateMutationController,
} from '@controllers/controls';
import { makeAutoObservable } from '@globals/mobx';
import { sharedCustomFieldsManager } from '@models/custom-fields';
import type { FormValues } from '@ui/forms';

class ControlsInfoViewEditCardModel {
    constructor() {
        makeAutoObservable(this);
    }

    handleOnSubmit = (values: FormValues, onScrollTo?: () => void) => {
        const { controlDetails } = sharedControlDetailsController;
        const { controlCustomFields } = sharedControlCustomFieldsController;
        const customFieldsData = controlCustomFields[0]?.customFields ?? [];

        if (!controlDetails) {
            return;
        }

        const customFieldsValues =
            sharedCustomFieldsManager.extractCustomFieldsFromFormValues(
                values,
                customFieldsData,
            );

        sharedControlsUpdateMutationController.updateControl(
            controlDetails.id,
            {
                name: values.name as string,
                code: values.code as string,
                description: values.description as string,
                question: values.question ? (values.question as string) : null,
                activity: values.activity ? (values.activity as string) : null,
            },
            customFieldsValues,
            onScrollTo,
        );
    };
}

export const sharedControlsInfoViewEditCardModel =
    new ControlsInfoViewEditCardModel();
