import { isNil } from 'lodash-es';
import {
    sharedControlCustomFieldsController,
    sharedControlDetailsController,
} from '@controllers/controls';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Stack } from '@cosmos/components/stack';
import { observer } from '@globals/mobx';
import { sharedCustomFieldsManager } from '@models/custom-fields';
import { CardLoadingSkeleton } from './card-loading-skeleton.component';

export const ControlsInfoReadOnlyCard = observer((): React.JSX.Element => {
    const { controlCustomFields } = sharedControlCustomFieldsController;

    const { controlDetails, isLoading: isControlDetailsLoading } =
        sharedControlDetailsController;
    const { name, code, description, question, activity } =
        controlDetails ?? {};
    const customFieldsData = controlCustomFields[0]?.customFields ?? [];

    return (
        <Stack gap="6x" direction="column" data-id="okD8NNTB">
            {isControlDetailsLoading ? (
                <CardLoadingSkeleton />
            ) : (
                <>
                    <KeyValuePair label="Name" type="TEXT" value={name} />
                    <KeyValuePair label="Code" type="TEXT" value={code} />
                    <KeyValuePair
                        label="Description"
                        type="TEXT"
                        value={description}
                    />
                    {!isNil(question) && (
                        <KeyValuePair
                            label="Question"
                            type="TEXT"
                            value={question}
                        />
                    )}
                    {!isNil(activity) && (
                        <KeyValuePair
                            label="Activities"
                            type="TEXT"
                            value={activity}
                        />
                    )}
                    {sharedCustomFieldsManager.renderReadOnlyCustomFields(
                        customFieldsData,
                    )}
                </>
            )}
        </Stack>
    );
});
