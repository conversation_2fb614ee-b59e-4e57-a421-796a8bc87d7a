import { Box } from '@cosmos/components/box';
import { Grid } from '@cosmos/components/grid';
import { Stack } from '@cosmos/components/stack';
import { observer } from '@globals/mobx';
import { ControlsInfoViewEditCard } from '../components/control-info/components/controls-info-view-edit-card.component';
import { ControlLinkedWorkspaceCardComponent } from '../components/control-linked-workspace/control-linked-workspace-card.component';
import { ControlMetricsGridComponent } from '../components/control-metrics/control-metrics-grid-container.component';
import { ControlOwnersViewEditCard } from '../components/control-owners/control-owners-view-edit-card';
import { ControlsOverviewReviewCardComponent } from '../components/control-review-approval/control-review-card.component';

export const ControlsOverviewView = observer((): React.JSX.Element => {
    return (
        <Stack
            data-testid="ControlsOverviewView"
            data-id="b7swgWwc"
            direction="column"
            gap="4x"
        >
            <ControlMetricsGridComponent />
            <Grid gap="4x" columns="repeat(2, 1fr)" rows="2">
                <Box gridRowStart="1" gridColumn="1">
                    <ControlsInfoViewEditCard />
                </Box>
                <Stack
                    gap="4x"
                    direction="column"
                    align="center"
                    width={'100%'}
                >
                    <Box gridRow="1" gridColumn="2" width={'100%'}>
                        <ControlOwnersViewEditCard />
                    </Box>
                    <ControlsOverviewReviewCardComponent />
                    <Box gridRow="2" gridColumn="2" width={'100%'}>
                        <ControlLinkedWorkspaceCardComponent />
                    </Box>
                </Stack>
            </Grid>
        </Stack>
    );
});
