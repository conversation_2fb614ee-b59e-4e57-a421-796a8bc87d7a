import { isEmpty } from 'lodash-es';
import { sharedControlOwnersMutationController } from '@controllers/controls';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { makeAutoObservable } from '@globals/mobx';
import type { FormValues } from '@ui/forms';

class ControlOwnersCardModel {
    constructor() {
        makeAutoObservable(this);
    }

    handleOnSubmitOwners = (values: FormValues) => {
        const typedValues = values as {
            owners: ListBoxItemData[];
        };

        const ownerIds = isEmpty(typedValues.owners)
            ? []
            : typedValues.owners.map((owner) => Number(owner.id));

        sharedControlOwnersMutationController.updateControlOwners(ownerIds);
    };
}

export const sharedControlOwnersCardModel = new ControlOwnersCardModel();
