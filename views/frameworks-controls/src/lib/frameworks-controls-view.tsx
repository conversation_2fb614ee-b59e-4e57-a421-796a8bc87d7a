import { useCallback, useMemo, useRef } from 'react';
import { AppDatatable } from '@components/app-datatable';
import { ControlPanel } from '@components/controls';
import {
    sharedControlDetailsOrchestratorController,
    sharedControlsController,
} from '@controllers/controls';
import { panelController } from '@controllers/panel';
import { sharedRequirementDetailsController } from '@controllers/requirements';
import type {
    DatatableRef,
    FetchDataResponseParams,
} from '@cosmos/components/datatable';
import type { ControlListResponseDto } from '@globals/api-sdk/types';
import { action, observer } from '@globals/mobx';
import { sharedFrameworksModel } from '../../../../models/frameworks/src';
import { sharedFrameworksControlsActionsModel } from '../models/frameworks-controls-actions.model';
import { sharedFrameworksControlsDataTableModel } from '../models/frameworks-controls-data-table.model';

export const FrameworksControlsView = observer((): React.JSX.Element => {
    const datatableRef = useRef<DatatableRef>(null);

    const { controls, total, isLoading, loadPage } = sharedControlsController;
    const { requirement, isRequirementLoading, isRequirementFetching } =
        sharedRequirementDetailsController;

    const { handleRowSelection } = sharedFrameworksModel;

    const { columns, tableId, emptyStateProps } =
        sharedFrameworksControlsDataTableModel;

    const { tableActions } = sharedFrameworksControlsActionsModel;

    const handleFetchData = useCallback(
        (param: FetchDataResponseParams) => {
            if (!requirement) {
                return;
            }

            loadPage(param, {
                requirementId: requirement.id,
                isArchived: false,
            });
        },
        [loadPage, requirement],
    );
    const handleRowClick = useCallback(
        ({ row }: { row: ControlListResponseDto }) => {
            action(() => {
                sharedControlDetailsOrchestratorController.load(row.id);
                panelController.openPanel({
                    id: 'evidence-control-panel',
                    content: () => (
                        <ControlPanel
                            controlSource="FRAMEWORK_REQUIREMENTS"
                            data-id="fsnIlFZ5"
                        />
                    ),
                });
            })();
        },
        [],
    );

    const bulkActionDropdownItems = useMemo(() => {
        return sharedFrameworksModel.bulkActions(datatableRef);
    }, []);

    return (
        <AppDatatable
            isFullPageTable
            isRowSelectionEnabled
            tableId={tableId}
            total={total}
            data={controls}
            columns={columns}
            getRowId={(row) => String(row.id)}
            data-testid="FrameworksControlsView"
            imperativeHandleRef={datatableRef}
            data-id="flg7uN2t"
            emptyStateProps={emptyStateProps}
            tableActions={tableActions}
            bulkActionDropdownItems={bulkActionDropdownItems}
            isLoading={
                isLoading || isRequirementLoading || isRequirementFetching
            }
            onFetchData={handleFetchData}
            onRowClick={handleRowClick}
            onRowSelection={handleRowSelection}
        />
    );
});
