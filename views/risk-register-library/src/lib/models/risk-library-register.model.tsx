import { sharedRiskLibraryRegisterByIdsController } from '@controllers/risk';
import type {
    BulkAction,
    DatatableRef,
    DatatableRowSelectionState,
} from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';

class RiskLibraryRegisterModel {
    selectedRiskIds: string[] = [];
    isAllRowsSelected = false;
    dataTableRef: React.RefObject<DatatableRef> | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    handleRowSelection = (
        currentRowSelectionState: DatatableRowSelectionState,
    ): void => {
        const { selectedRows, isAllRowsSelected } = currentRowSelectionState;
        const selectedIds = Object.keys(selectedRows);

        this.selectedRiskIds = selectedIds.filter(Boolean);
        this.isAllRowsSelected = isAllRowsSelected;
    };

    setDataTableRef = (ref: React.RefObject<DatatableRef> | null): void => {
        this.dataTableRef = ref;
    };

    get bulkActions(): BulkAction[] {
        const { registerRiskByIds } = sharedRiskLibraryRegisterByIdsController;

        const actions: BulkAction[] = [
            {
                actionType: 'button',
                id: 'add-register-button',
                typeProps: {
                    label: t`Add to register`,
                    level: 'tertiary',
                    onClick: () => {
                        registerRiskByIds('COPY_BY_IDS', this.selectedRiskIds);
                        this.dataTableRef?.current?.resetRowSelection();
                    },
                },
            },
        ];

        return actions;
    }
}

export const sharedRiskLibraryRegisterModel = new RiskLibraryRegisterModel();
