import { isEmpty, noop } from 'lodash-es';
import { useRef } from 'react';
import { AppDatatable } from '@components/app-datatable';
import { sharedRiskLibraryController } from '@controllers/risk';
import type { DatatableRef } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedRiskLibraryFiltersModel } from '@models/risk-library';
import { getRiskColumns } from './constants/risk-register-library.constants';
import { sharedRiskLibraryRegisterModel } from './models/risk-library-register.model';

export const RiskRegisterLibraryView = observer((): React.JSX.Element => {
    const { risks, isLoading, loadRiskLibrary, total } =
        sharedRiskLibraryController;
    const { bulkActions, handleRowSelection, setDataTableRef } =
        sharedRiskLibraryRegisterModel;
    const datatableRef = useRef<DatatableRef>(null);

    setDataTableRef(datatableRef);

    const { filters } = sharedRiskLibraryFiltersModel;

    return (
        <AppDatatable
            isFullPageTable
            getRowId={(row) => row.riskId}
            isLoading={isLoading}
            tableId="risk-register-library-datatable"
            total={total}
            data={risks}
            imperativeHandleRef={datatableRef}
            bulkActionDropdownItems={bulkActions}
            columns={getRiskColumns()}
            filterProps={filters}
            data-testid="RiskRegisterLibraryView"
            disabledRowSelectionCheckboxTooltip={t`Risk is already in your register.`}
            data-id="XWGtoM9e"
            isRowSelectionEnabled={({ original }) => isEmpty(original.risks)}
            tableSearchProps={{
                placeholder: t`Search by name, description, control code, or requirement`,
                hideSearch: false,
                debounceDelay: 1000,
                defaultValue: '',
            }}
            onRowSelection={handleRowSelection}
            onFetchData={loadRiskLibrary}
            onRowClick={noop}
        />
    );
});
