import { isEmpty } from 'lodash-es';
import { sharedRiskLibraryRegisterByIdsController } from '@controllers/risk';
import { routeController } from '@controllers/route';
import { SchemaDropdown } from '@cosmos/components/schema-dropdown';
import type { RiskLibraryResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useNavigate } from '@remix-run/react';

export const ActionButtonCell = observer(
    ({
        row: { original },
    }: {
        row: { original: RiskLibraryResponseDto };
    }): React.JSX.Element => {
        const navigate = useNavigate();
        const { risks, riskId } = original;

        const { registerRiskByIds } = sharedRiskLibraryRegisterByIdsController;

        const items = isEmpty(risks)
            ? [
                  {
                      id: 'add-to-register',
                      label: t`Add to register`,
                      type: 'item',
                      onClick: () => {
                          registerRiskByIds('COPY_BY_IDS', [riskId]);
                      },
                  },
              ]
            : [
                  {
                      id: 'manage-in-register',
                      label: t`Manage in register`,
                      type: 'item',
                      onClick: () => {
                          navigate(
                              `${routeController.userPartOfUrl}/risk/register/management/${riskId}/overview`,
                          );
                      },
                  },
              ];

        return (
            <SchemaDropdown
                isIconOnly
                size="sm"
                startIconName="HorizontalMenu"
                level="tertiary"
                label={t`Horizontal menu`}
                colorScheme="neutral"
                items={items}
                data-testid="ActionButtonCell"
                data-id="6GvBWQ_R"
            />
        );
    },
);
