import { Text } from '@cosmos/components/text';
import { Truncation } from '@cosmos-lab/components/truncation';
import type { RiskLibraryResponseDto } from '@globals/api-sdk/types';

export const RiskDescriptionCell = ({
    row: { original },
}: {
    row: { original: RiskLibraryResponseDto };
}): React.ReactNode => {
    const { description } = original;

    return (
        <Text data-testid="RiskDescriptionCell" data-id="xEmfZTYr">
            <Truncation maxLength={150}>{description}</Truncation>
        </Text>
    );
};
