import { panel<PERSON>ontroller } from '@controllers/panel';
import { PanelHeader } from '@cosmos/components/panel';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import type { RequirementDetailsProps } from '../types/requirement-details-props.type';
import { ActionStackHeaderComponent } from './action-stack-header.component';

export const RequirementDetailsHeaderPanelComponent = observer(
    ({ requirement }: RequirementDetailsProps): React.JSX.Element => {
        const { currentWorkspaceId } = sharedWorkspacesController;
        const { id, name, isReady, frameworkId } = requirement;
        const navigate = useNavigate();

        return (
            <PanelHeader
                data-testid="RequirementDetailsHeaderPanelComponent"
                data-id="DA33sERo"
                title={name}
                slot={<ActionStackHeaderComponent isReady={isReady} />}
                action={{
                    label: 'Open',
                    level: 'secondary',
                    endIconName: 'Expand',
                    onClick: () => {
                        navigate(
                            `/workspaces/${currentWorkspaceId}/compliance/frameworks/all/current/${frameworkId}/requirements/${id}/overview`,
                        );
                        panelController.closePanel();
                    },
                }}
            />
        );
    },
);
