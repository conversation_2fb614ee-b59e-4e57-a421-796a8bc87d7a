import { isNil } from 'lodash-es';
import { sharedControlsController } from '@controllers/controls';
import { sharedRequirementDetailsController } from '@controllers/requirements';
import type {
    ControlListResponseDto,
    RequirementDetailResponseDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable } from '@globals/mobx';
import { FEDRAMP_TAILORED_LI_SAAS_CLASS } from '../constants/fed-ramp-class.constant';
import {
    DEFAULT_MAPPED_CONTROLS_PAGE_SIZE,
    REQUIREMENT_DETAILS_PANEL_PAGINATION_CONTROLS_TEST_ID,
} from '../constants/requirements-details-panel.constant';
import { buildPartDescriptionProps } from '../helpers/build-parts-descriptions.helper';
import type { PartDescriptionProps } from '../types/part-description.type';

class RequirementDetailsModel {
    constructor() {
        makeAutoObservable(this);
    }

    get panelIsLoading(): boolean {
        return (
            sharedRequirementDetailsController.isRequirementLoading ||
            sharedRequirementDetailsController.isRequirementFetching
        );
    }

    get requirement(): RequirementDetailResponseDto | null {
        return sharedRequirementDetailsController.requirement;
    }

    get partsDescription(): PartDescriptionProps[] {
        const { requirement } = sharedRequirementDetailsController;

        if (isNil(requirement) || isNil(requirement.parts)) {
            return [];
        }

        const partsDescriptionProps = buildPartDescriptionProps(
            requirement.parts,
            requirement.params,
            requirement.values,
        );

        return partsDescriptionProps;
    }

    get propsDescriptions(): string | undefined {
        const { requirement } = sharedRequirementDetailsController;

        if (isNil(requirement) || isNil(requirement.props)) {
            return;
        }

        const propsDescriptionsList = requirement.props.filter(
            ({ class: partClass }) =>
                partClass === FEDRAMP_TAILORED_LI_SAAS_CLASS,
        );

        return propsDescriptionsList.map((prop) => prop.value).join(', ');
    }

    get controlsIsLoading(): boolean {
        return sharedControlsController.isLoading;
    }

    get controls(): ControlListResponseDto[] {
        const { controls } = sharedControlsController;

        return controls;
    }

    get totalMappedControls(): number {
        return sharedControlsController.total;
    }

    get defaultMappedControlsPageSize(): number {
        return DEFAULT_MAPPED_CONTROLS_PAGE_SIZE;
    }

    get requirementsDetailsPanelPaginationControlsTestId(): string {
        return REQUIREMENT_DETAILS_PANEL_PAGINATION_CONTROLS_TEST_ID;
    }

    mappedControlsPaginationOnPageChange = (page: number) => {
        const { getControlsList } = sharedControlsController;
        const { requirement } = sharedRequirementDetailsController;

        if (isNil(requirement)) {
            return;
        }

        getControlsList({
            page,
            requirementId: requirement.id,
            isArchived: false,
            limit: this.defaultMappedControlsPageSize,
        });
    };
}

export const sharedRequirementDetailsModel = new RequirementDetailsModel();
