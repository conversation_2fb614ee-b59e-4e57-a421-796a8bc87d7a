import type { DatatableProps } from '@cosmos/components/datatable';
import type { RequirementListResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { FrameworkRequirementActionsCellComponent } from '../lib/components/framework-requirement-actions-cell.component';
import { FrameworkRequirementScopeCellComponent } from '../lib/components/framework-requirement-scope-cell.component';
import { FrameworkRequirementStatusCellComponent } from '../lib/components/framework-requirement-status-cell.component';

const COLUMN_SIZE = 45;

export const getFrameworksDetailsColumns =
    (): DatatableProps<RequirementListResponseDto>['columns'] => [
        {
            accessorKey: 'actions',
            header: t`Actions`,
            isActionColumn: true,
            enableSorting: false,
            id: 'row-actions',
            meta: {
                shouldIgnoreRowClick: true,
            },
            maxSize: 68,
            minSize: 68,
            cell: FrameworkRequirementActionsCellComponent,
        },
        {
            accessorKey: 'isReady',
            header: t`Status`,
            id: 'is-ready',
            enableSorting: false,
            maxSize: 150,
            minSize: 100,
            cell: FrameworkRequirementStatusCellComponent,
        },
        {
            accessorKey: 'name',
            header: t`Code`,
            id: 'name',
            enableSorting: false,
            size: 100,
        },
        {
            header: t`Title`,
            id: 'description',
            accessorKey: 'description',
            enableSorting: false,
            minSize: 200,
        },
        {
            accessorKey: 'totalInScopeControls',
            header: t`Controls`,
            id: 'totalInScopeControls',
            enableSorting: false,
            size: COLUMN_SIZE,
        },
        {
            accessorKey: 'name',
            header: t`Scope`,
            id: 'scope',
            enableSorting: false,
            cell: FrameworkRequirementScopeCellComponent,
        },
    ];
