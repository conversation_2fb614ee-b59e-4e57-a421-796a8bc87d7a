import { useCallback, useEffect } from 'react';
import {
    SELECT_FRAMEWORK_LEVEL_MODAL_ID,
    SELECT_FRAMEWORK_PROFILE_MODAL_ID,
    SelectFrameworkLevelModalContent,
    SelectFrameworkProfileModalContent,
} from '@components/frameworks';
import { sharedFrameworkDetailsController } from '@controllers/frameworks';
import { modalController } from '@controllers/modal';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import { useFrameworkDetailsController } from '@views/current-frameworks';
import {
    CustomFrameworkRequirementsTable,
    NonCustomFrameworkRequirementsTable,
} from './components';
import { sharedFrameworkDetailsRequirementListModel } from './models/framework-details-requirement-list-model';

export const FrameworksDetailsView = observer((): React.JSX.Element => {
    const { currentWorkspace } = sharedWorkspacesController;
    const { needsLevel, needsProfile, frameworkDetails } =
        sharedFrameworkDetailsController;
    const navigate = useNavigate();
    const frameworksDetailsController = useFrameworkDetailsController(
        frameworkDetails?.id ?? 0,
    );

    const onCancel = useCallback(() => {
        navigate(
            `/workspaces/${currentWorkspace?.id}/compliance/frameworks/all/current`,
        );
    }, [currentWorkspace?.id, navigate]);

    useEffect(() => {
        if (needsLevel) {
            modalController.openModal({
                id: SELECT_FRAMEWORK_LEVEL_MODAL_ID,
                content: () => (
                    <SelectFrameworkLevelModalContent
                        data-id="MXT2836R"
                        frameworksDetailsController={
                            frameworksDetailsController
                        }
                        onCancel={onCancel}
                    />
                ),
                centered: true,
                size: 'md',
                disableClickOutsideToClose: true,
            });
        }

        if (needsProfile) {
            modalController.openModal({
                id: SELECT_FRAMEWORK_PROFILE_MODAL_ID,
                content: () => (
                    <SelectFrameworkProfileModalContent
                        data-id="MXT2836R"
                        frameworksDetailsController={
                            frameworksDetailsController
                        }
                        onCancel={onCancel}
                    />
                ),
                centered: true,
                size: 'md',
                disableClickOutsideToClose: true,
            });
        }
    }, [frameworksDetailsController, onCancel, needsLevel, needsProfile]);

    const { isCustomFramework } = sharedFrameworkDetailsRequirementListModel;

    return isCustomFramework ? (
        <CustomFrameworkRequirementsTable />
    ) : (
        <NonCustomFrameworkRequirementsTable />
    );
});
