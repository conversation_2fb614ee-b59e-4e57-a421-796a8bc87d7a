import { Metadata } from '@cosmos/components/metadata';
import { t } from '@globals/i18n/macro';
import type { FrameworkDetailsCellProps } from '../../types/framework-details.type';

export const FrameworkRequirementStatusCellComponent = ({
    row: { original },
}: FrameworkDetailsCellProps): React.JSX.Element => {
    const { isReady, archivedAt } = original;
    const label = isReady ? 'Ready' : 'Not Ready';

    if (archivedAt) {
        return (
            <Metadata
                colorScheme="neutral"
                iconName="OutOfScope"
                label={t`Out of scope`}
                type="tag"
            />
        );
    }

    return (
        <Metadata
            colorScheme={isReady ? 'success' : 'critical'}
            iconName={isReady ? 'CheckCircle' : 'Cancel'}
            label={label}
            type="tag"
            data-id="sfJW27Us"
            data-testid="FrameworkRequirementStatusCellComponent"
        />
    );
};
