import type { DatatableProps } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { COLUMN_SIZES } from '@helpers/table';
import { BulkAddTestNameCell } from '../lib/cells/library-test-template-bulk-add-error-name-cell';
import { BulkAddRefLinkCell } from '../lib/cells/library-test-template-bulk-add-error-reflink-cell';
import type { LibraryTestTemplateBulkAddErrorTableRow } from '../types/library-test-template-bulk-add-error-table.types';

export const getLibraryTestTemplateBulkActionErrorColumns =
    (): DatatableProps<LibraryTestTemplateBulkAddErrorTableRow>['columns'] => [
        {
            id: 'name',
            header: t`Test Name`,
            accessorKey: 'nameCell',
            cell: BulkAddTestNameCell,
            size: COLUMN_SIZES.XLARGE,
        },
        {
            id: 'reflink',
            accessorKey: 'refLinkCell',
            header: '',
            cell: BulkAddRefLinkCell,
            size: COLUMN_SIZES.MEDIUM,
        },
    ];

export const LIBRARY_TEST_TEMPLATE_BULK_ACTION_ERROR =
    getLibraryTestTemplateBulkActionErrorColumns();
