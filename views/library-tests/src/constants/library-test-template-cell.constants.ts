/**
 * Test truncation lengths.
 */
export const TEST_TEMPLATE_NEW_NAME_TRUNCATION_LENGTH = 16;
export const TEST_TEMPLATE_NAME_TRUNCATION_LENGTH = 22;
export const TEST_TEMPLATE_DESCRIPTION_TRUNCATION_LENGTH = 48;

/**
 * Font sizes.
 */
export const TEST_TEMPLATE_DEFAULT_FONT_SIZE = '200';

/**
 * Stack props.
 */
export const TEST_TEMPLATE_CELL_STACK_PROPS = {
    align: 'center',
    justify: 'start',
    height: '100%',
    width: '100%',
    direction: 'row',
} as const;

/**
 * Spacing values.
 */
export const TEST_TEMPLATE_CELL_GAP_DEFAULT = '2x';

/**
 * Test template cell test IDs.
 */
export const TEST_TEMPLATE_CELL_TEST_IDS = {
    NAME: 'LibraryTestTemplateNameCell',
    DESCRIPTION: 'LibraryTestTemplateDescriptionCell',
    CATEGORY: 'LibraryTestTemplateCategoryCell',
    RATING: 'LibraryTestTemplateRatingCell',
    OPTIONS: 'LibraryTestTemplateOptionsCell',
    UPDATED_AT: 'LibraryTestTemplateUpdatedAtCell',
} as const;
