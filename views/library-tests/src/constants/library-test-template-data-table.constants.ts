import type { DatatableProps } from '@cosmos/components/datatable';
import type { LibraryTestTemplateResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { COLUMN_SIZES } from '@helpers/table';
import { LibraryTestTemplateCategoryCell } from '../lib/cells/library-test-template-category-cell';
import { LibraryTestTemplateDescriptionCell } from '../lib/cells/library-test-template-description-cell';
import { LibraryTestTemplateNameCell } from '../lib/cells/library-test-template-name-cell';
import { LibraryTestTemplateOptionsCell } from '../lib/cells/library-test-template-options-cell';
import { LibraryTestTemplateRatingCell } from '../lib/cells/library-test-template-rating-cell';
import { LibraryTestTemplateUpdatedAtCell } from '../lib/cells/library-test-template-updated-at-cell';

export const getLibraryTestTemplateColumns =
    (): DatatableProps<LibraryTestTemplateResponseDto>['columns'] => [
        {
            id: 'actions',
            header: '',
            accessorKey: 'id',
            enableSorting: false,
            isActionColumn: true,
            cell: LibraryTestTemplateOptionsCell,
            meta: {
                shouldIgnoreRowClick: true,
            },
            minSize: COLUMN_SIZES.SMALL,
            maxSize: COLUMN_SIZES.SMALL,
        },
        {
            id: 'name',
            header: () => t`Name`,
            accessorFn: (row) => row.name,
            enableSorting: true,
            cell: LibraryTestTemplateNameCell,
            minSize: COLUMN_SIZES.LARGE,
        },
        {
            id: 'description',
            header: () => t`Description`,
            accessorKey: 'description',
            enableSorting: true,
            cell: LibraryTestTemplateDescriptionCell,
            minSize: COLUMN_SIZES.LARGE,
        },
        {
            id: 'category',
            header: () => t`Category`,
            accessorKey: 'category',
            cell: LibraryTestTemplateCategoryCell,
            enableSorting: true,
            minSize: COLUMN_SIZES.MEDIUM,
        },
        {
            id: 'rating',
            header: () => t`Rating`,
            accessorKey: 'rating',
            cell: LibraryTestTemplateRatingCell,
            enableSorting: true,
            minSize: COLUMN_SIZES.MEDIUM,
        },
        {
            id: 'updatedAt',
            header: () => t`Last updated`,
            accessorKey: 'updatedAt',
            enableSorting: true,
            cell: LibraryTestTemplateUpdatedAtCell,
            minSize: COLUMN_SIZES.MEDIUM,
        },
    ];

export const LIBRARY_TEST_TEMPLATE_COLUMNS = getLibraryTestTemplateColumns();
