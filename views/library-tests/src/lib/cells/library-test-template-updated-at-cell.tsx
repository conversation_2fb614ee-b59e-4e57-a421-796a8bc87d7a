import { Stack } from '@cosmos/components/stack';
import { DateTime } from '@cosmos-lab/components/date-time';
import {
    TEST_TEMPLATE_CELL_STACK_PROPS,
    TEST_TEMPLATE_CELL_TEST_IDS,
} from '../../constants/library-test-template-cell.constants';
import type { LibraryTestTemplateTableCellProps } from '../../types/library-test-template-data-table.types';

export const LibraryTestTemplateUpdatedAtCell = ({
    row: { original },
}: LibraryTestTemplateTableCellProps): React.JSX.Element => {
    const { templateId, updatedAt } = original;

    return (
        <Stack
            {...TEST_TEMPLATE_CELL_STACK_PROPS}
            data-testid={TEST_TEMPLATE_CELL_TEST_IDS.UPDATED_AT}
            data-id="NhHPNPHF"
        >
            <DateTime
                date={updatedAt}
                format="field"
                data-testid={TEST_TEMPLATE_CELL_TEST_IDS.UPDATED_AT}
                data-id={`${templateId}-updated-at`}
            />
        </Stack>
    );
};
