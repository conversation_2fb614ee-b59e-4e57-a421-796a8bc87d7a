import { DocumentViewer } from '@components/document-viewer';
import {
    sharedVendorsCurrentTrustCenterDocumentDetailController,
    sharedVendorTrustCenterController,
} from '@controllers/vendors';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useVendorTrustCenterRedirect } from '@views/vendors-profile-trust-center';
import { LOCALHOST_STORAGE_URL } from './constants/vendors-profile-trust-center-doucment-detail-view.constants';

const getFileExtension = (fileName: string | null): string => {
    if (!fileName) {
        return '';
    }

    const parts = fileName.split('.');
    const lastPart = parts.pop();

    return lastPart ? lastPart.toLowerCase() : '';
};

export const VendorsProfileTrustCenterDocumentDetailView = observer(
    (): React.JSX.Element | null => {
        const { isLoading, downloadUrl, fileName } =
            sharedVendorsCurrentTrustCenterDocumentDetailController;
        const { isVendorTrustCenterEnabled } =
            sharedVendorTrustCenterController;

        useVendorTrustCenterRedirect({
            levelsToParentRoute: 3,
        });

        if (!isVendorTrustCenterEnabled) {
            return null;
        }

        /*
        // if src for file is from localhost send fileExtension as empty string
        // (files from localhost download instead of displaying in DocumentViewer)
        */
        const isLocalhost = downloadUrl?.includes(LOCALHOST_STORAGE_URL);
        const fileExtension = isLocalhost ? '' : getFileExtension(fileName);

        return (
            <Stack
                data-testid="VendorsProfileTrustCenterDocumentDetailView"
                data-id="ni3m3X1c"
                height="100%"
                justify="center"
            >
                {isLoading ? (
                    <Loader isSpinnerOnly label={t`Loading...`} />
                ) : (
                    <DocumentViewer
                        src={downloadUrl ?? ''}
                        label={fileName ?? ''}
                        data-id="kJRsbY6u"
                        fileType={fileExtension}
                    />
                )}
            </Stack>
        );
    },
);
