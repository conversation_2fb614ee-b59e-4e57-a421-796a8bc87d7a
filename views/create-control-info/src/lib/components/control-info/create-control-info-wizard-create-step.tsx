import { isEmpty } from 'lodash-es';
import { useCallback } from 'react';
import { sharedControlCustomFieldsListController } from '@controllers/controls';
import { Box } from '@cosmos/components/box';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { Trans } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { sharedControlInfoFormModel } from '@models/controls';
import { sharedCustomFieldsValuesModel } from '@models/custom-fields';
import { Form } from '@ui/forms';
import { buildControlInfoFormSchema } from '../../schemas/control-info-form-schema';
import { CustomFieldRenderer } from './custom-fields';

const FORM_ID = 'create-control-info';

interface Props {
    formRef: React.ForwardedRef<HTMLFormElement>;
}

export const CreateControlInfoWizardCreateStep = observer(
    ({ formRef }: Props): React.JSX.Element => {
        const { handleSubmit } = sharedControlInfoFormModel;
        const formSchema = buildControlInfoFormSchema();
        const { customFieldsList, isLoading, customFieldsListQuery } =
            sharedControlCustomFieldsListController;

        const handleCustomFieldChange = useCallback(
            (fieldName: string, value: string) => {
                sharedCustomFieldsValuesModel.setCustomFieldValue(
                    fieldName,
                    value,
                );
            },
            [],
        );

        if (
            !customFieldsListQuery.data &&
            !isLoading &&
            sharedFeatureAccessModel.isCustomFieldsEnabled
        ) {
            sharedControlCustomFieldsListController.loadCustomFieldsList();
        }

        return (
            <Stack
                flexGrow="4"
                gap="6x"
                direction="column"
                data-testid="CreateControlInfoWizardCreateStep"
                data-id="NnL2m3_T"
            >
                <Stack direction="column">
                    <Box>
                        <Text type="subheadline" size="400" as="p">
                            <Trans>Add your control basic information</Trans>
                        </Text>
                    </Box>
                </Stack>
                <Stack gap="md" direction="column">
                    <Form
                        hasExternalSubmitButton
                        data-id="create-control-info-form"
                        ref={formRef}
                        formId={FORM_ID}
                        schema={formSchema}
                        onSubmit={handleSubmit}
                    />

                    {!isLoading &&
                        !isEmpty(customFieldsList) &&
                        sharedFeatureAccessModel.isCustomFieldsEnabled && (
                            <Stack gap="md" direction="column">
                                {customFieldsList.flatMap((section) =>
                                    section.customFields.map((field) => (
                                        <CustomFieldRenderer
                                            key={field.customFieldId}
                                            field={field}
                                            formId={FORM_ID}
                                            data-id="zJFx_ZG8"
                                            value={
                                                sharedCustomFieldsValuesModel
                                                    .customFieldValues[
                                                    field.name
                                                ] || ''
                                            }
                                            onChange={(value) => {
                                                action(() => {
                                                    handleCustomFieldChange(
                                                        field.name,
                                                        value,
                                                    );
                                                })();
                                            }}
                                        />
                                    )),
                                )}
                            </Stack>
                        )}
                </Stack>
            </Stack>
        );
    },
);
