import { useEffect } from 'react';
import { sharedFrameworksController } from '@controllers/frameworks';
import { sharedMapRequirementsController } from '@controllers/requirements';
import { Accordion } from '@cosmos/components/accordion';
import { Box } from '@cosmos/components/box';
import { DEFAULT_PAGE_SIZE_OPTIONS } from '@cosmos/components/datatable';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { getFrameworkBadge } from '@cosmos-lab/components/framework-badge';
import { PaginationControls } from '@cosmos-lab/components/pagination-controls';
import { Trans } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { sharedMapRequirementsModel } from '@models/controls';
import { useParams } from '@remix-run/react';
import { useFrameworksPagination } from '../../helpers/use-frameworks-pagination.helper';
import { MappedRequirements } from './mapped-requirements';

export const CreateControlInfoWizardMapRequirementStep = observer(
    (): React.JSX.Element => {
        const { productFrameworks } = sharedFrameworksController;

        const { requirementId, frameworkId } = useParams();

        useEffect(() => {
            /**
             * This effect takes care of pre-mapping the requirement to the control being created
             * when the wizard is launched form the requirement context.
             */
            action(() => {
                if (requirementId && frameworkId) {
                    sharedMapRequirementsController.mapRequirementsToFramework(
                        Number(frameworkId),
                        [
                            {
                                id: requirementId,
                                label: 'Dummy label',
                                value: requirementId,
                            },
                        ],
                    );
                }
            })();
        }, [requirementId, frameworkId]);

        const {
            currentPage,
            pageSize,
            frameworksTotal,
            shouldShowPagination,
            handlePageChange,
            handlePageSizeChange,
        } = useFrameworksPagination();

        return (
            <Stack
                direction="column"
                data-testid="CreateControlInfoWizardCreateStep"
                data-id="NnL2m3_T"
            >
                <Stack direction="column">
                    <Box>
                        <Text type="subheadline" size="400" as="p">
                            <Trans>Map control&apos;s requirements</Trans>
                        </Text>
                    </Box>

                    <Stack direction="column" p="lg" gap="lg">
                        {productFrameworks.map((framework) => {
                            const requirementsCount =
                                sharedMapRequirementsModel.getFrameworkMappedRequirements(
                                    framework.id,
                                ).length;

                            return (
                                <Accordion
                                    key={framework.id}
                                    title={framework.name}
                                    data-id={`${framework.name.toLowerCase().replaceAll(/\s+/g, '-')}-accordion`}
                                    body={
                                        <MappedRequirements
                                            framework={framework}
                                        />
                                    }
                                    supportingContent={
                                        <Stack
                                            direction="row"
                                            gap="md"
                                            align="center"
                                        >
                                            <Text type="body" size="100">
                                                <Trans>
                                                    Mapped Requirements
                                                </Trans>
                                            </Text>
                                            <Metadata
                                                label={requirementsCount.toString()}
                                                type="number"
                                                colorScheme="neutral"
                                            />
                                        </Stack>
                                    }
                                    iconSlot={{
                                        slotType: 'frameworkBadge',
                                        typeProps: {
                                            badgeName: getFrameworkBadge(
                                                framework.tag,
                                            ),
                                            colorScheme: 'primary',
                                            size: 'md',
                                        },
                                    }}
                                />
                            );
                        })}
                    </Stack>

                    {shouldShowPagination && (
                        <PaginationControls
                            total={frameworksTotal}
                            pageSize={pageSize}
                            initialPage={currentPage}
                            pageSizeOptions={DEFAULT_PAGE_SIZE_OPTIONS}
                            data-id="frameworks-pagination"
                            onPageChange={handlePageChange}
                            onPageSizeChange={handlePageSizeChange}
                        />
                    )}
                </Stack>
            </Stack>
        );
    },
);
