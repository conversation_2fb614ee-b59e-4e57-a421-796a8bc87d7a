import type { RefObject } from 'react';
import { sharedPolicyBuilderDetailsController } from '@controllers/policy-builder';
import { observer, runInAction } from '@globals/mobx';
import { sharedPolicyBuilderDetailsFormModel } from '@models/policies';
import { Form } from '@ui/forms';

interface Props {
    formRef: RefObject<HTMLFormElement>;
}

export const PoliciesBuilderOverviewDetailsEditComponent = observer(
    ({ formRef }: Props): React.JSX.Element => {
        return (
            <Form
                hasExternalSubmitButton
                data-id="policy-builder-details-edit-form"
                formId="policy-builder-details-edit"
                ref={formRef}
                schema={sharedPolicyBuilderDetailsFormModel.editDetailsSchema}
                onSubmit={(values) => {
                    runInAction(() => {
                        sharedPolicyBuilderDetailsController.savePolicyDetails(
                            values as {
                                description: string;
                                renewalDate: {
                                    renewalFrequency: { value: string };
                                    renewalDate: string;
                                };
                                assignedTo: 'ALL' | 'GROUP' | 'NONE';
                                notifyNewMembers?: boolean;
                                selectedGroups?: { value: string }[];
                            },
                        );
                    });
                }}
            />
        );
    },
);
