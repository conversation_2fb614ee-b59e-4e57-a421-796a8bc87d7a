import { ViewEditCardComponent } from '@components/view-edit-card';
import { sharedPersonnelGroupController } from '@controllers/personnel-group-controller';
import { sharedPolicyBuilderDetailsController } from '@controllers/policy-builder';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { useFormSubmit } from '@ui/forms';
import { PoliciesBuilderOverviewDetailsEditComponent } from './policies-builder-overview-details-edit.component';
import { PoliciesBuilderOverviewDetailsReadOnlyComponent } from './policies-builder-overview-details-readonly.component';

export const PoliciesBuilderOverviewDetailsComponent = observer(() => {
    const { isUpdatingPolicy, updatePolicyDetailsMutation } =
        sharedPolicyBuilderDetailsController;
    const { formRef, triggerSubmit } = useFormSubmit();

    const handleEdit = action(() => {
        // Load personnel groups to show the "Specific groups" option
        sharedPersonnelGroupController.loadPersonnelGroups();
    });

    return (
        <ViewEditCardComponent
            data-id="policies-builder-overview-details"
            title={t`Details`}
            isMutationPending={isUpdatingPolicy}
            hasMutationError={updatePolicyDetailsMutation.hasError}
            readOnlyComponent={
                <PoliciesBuilderOverviewDetailsReadOnlyComponent />
            }
            editComponent={
                <PoliciesBuilderOverviewDetailsEditComponent
                    formRef={formRef}
                />
            }
            onEdit={handleEdit}
            onSave={triggerSubmit}
        />
    );
});
