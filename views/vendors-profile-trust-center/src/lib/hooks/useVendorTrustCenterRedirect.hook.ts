import { useEffect } from 'react';
import { sharedVendorTrustCenterController } from '@controllers/vendors';
import { getParentRoute } from '@helpers/path';
import { useLocation, useNavigate } from '@remix-run/react';

export const useVendorTrustCenterRedirect = ({
    levelsToParentRoute,
}: {
    levelsToParentRoute?: number;
} = {}): void => {
    const { isInfoLoading, isVendorTrustCenterEnabled } =
        sharedVendorTrustCenterController;

    const navigate = useNavigate();
    const location = useLocation();
    const { pathname } = location;
    const parentRoute = getParentRoute(pathname, levelsToParentRoute);

    useEffect(() => {
        if (!isInfoLoading && !isVendorTrustCenterEnabled) {
            navigate(parentRoute, { replace: true });
        }
    }, [isInfoLoading, isVendorTrustCenterEnabled, navigate, parentRoute]);
};
