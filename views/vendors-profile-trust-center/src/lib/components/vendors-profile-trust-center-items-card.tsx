import { panelController } from '@controllers/panel';
import { sharedVendorTrustCenterController } from '@controllers/vendors';
import { Box } from '@cosmos/components/box';
import { Card } from '@cosmos/components/card';
import { Grid } from '@cosmos/components/grid';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { vendorTrustCenterItemsPanelModel } from './models/vendor-trust-center-items-panel-model';
import { VendorsProfileTrustCenterItemComponent } from './vendors-profile-trust-center-item-component';
import { VendorsProfileTrustCenterItemsDetailsPanel } from './vendors-profile-trust-center-items-details-panel';

export const VendorsProfileTrustCenterItemsCard = observer(
    (): React.JSX.Element | null => {
        const { itemsByCategory, isItemsLoading } =
            sharedVendorTrustCenterController;

        const { setVendorCategories, setIndex } =
            vendorTrustCenterItemsPanelModel;

        setVendorCategories(itemsByCategory);

        const handleOpenPanel = (index: number | undefined) => {
            if (index !== undefined) {
                setIndex(index);
            }

            panelController.openPanel({
                id: 'trust-center-details-panel',
                content: () => (
                    <VendorsProfileTrustCenterItemsDetailsPanel data-id="UwzalLLv" />
                ),
            });
        };

        return (
            <Card
                isLoading={isItemsLoading}
                data-testid="VendorsProfileTrustCenterItemsCard"
                data-id="Lxr-ANWN"
                cardHeight="100%"
                size="lg"
                title={t`Items`}
                body={
                    <Grid
                        gap={'2x'}
                        columns={{ initial: '1', md: '2', lg: '3' }}
                    >
                        {itemsByCategory.map(({ type, title, items }, idx) => (
                            <Card
                                key={type}
                                title={title}
                                cardHeight="100%"
                                data-id="_ZCEyFby"
                                body={
                                    <Box>
                                        {items.map(({ name, maturity }) => (
                                            <VendorsProfileTrustCenterItemComponent
                                                data-id="gRNGwJBR"
                                                key={name}
                                                maturityLevel={maturity}
                                                title={name}
                                            />
                                        ))}
                                    </Box>
                                }
                                actions={[
                                    {
                                        actionType: 'button',
                                        id: 'view-all-button',
                                        typeProps: {
                                            label: t`View all`,
                                            level: 'tertiary',
                                            onClick: () => {
                                                handleOpenPanel(idx);
                                            },
                                        },
                                    },
                                ]}
                            />
                        ))}
                    </Grid>
                }
            />
        );
    },
);
