import { panelController } from '@controllers/panel';
import type { VendorTrustCenterItemsByCategoryResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable } from '@globals/mobx';

class VendorTrustCenterItemsPanelModel {
    currentIndex = 0;
    currentCategory: VendorTrustCenterItemsByCategoryResponseDto | undefined;
    vendorItems: VendorTrustCenterItemsByCategoryResponseDto[] | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    paginatePanelContent = (num: number) => {
        const newIndex = this.currentIndex + num;

        if (
            newIndex < 0 ||
            !this.vendorItems ||
            newIndex >= this.vendorItems.length
        ) {
            return;
        }

        this.setIndex(newIndex);
    };

    setVendorCategories = (
        items: VendorTrustCenterItemsByCategoryResponseDto[],
    ) => {
        this.vendorItems = items;
    };

    get totalItems(): number {
        return this.vendorItems?.length ?? 0;
    }

    setIndex = (index: number) => {
        this.currentIndex = index;
        this.currentCategory = this.vendorItems?.[this.currentIndex];
    };

    handleNextPage = (): void => {
        this.paginatePanelContent(1);
    };

    handlePrevPage = (): void => {
        this.paginatePanelContent(-1);
    };

    handleClose = (): void => {
        panelController.closePanel();
    };
}

export const vendorTrustCenterItemsPanelModel =
    new VendorTrustCenterItemsPanelModel();
