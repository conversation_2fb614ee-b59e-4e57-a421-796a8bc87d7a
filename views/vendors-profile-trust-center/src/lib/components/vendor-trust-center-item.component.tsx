import { isArray, isEmpty, isNil } from 'lodash-es';
import { Box } from '@cosmos/components/box';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Divider } from '@cosmos-lab/components/divider';
import { MarkdownViewer } from '@cosmos-lab/components/markdown-viewer';
import type { SbCategoryTypeEnum } from '@globals/api-sdk/types';
import { observer } from '@globals/mobx';
import { AppLink } from '@ui/app-link';
import { SecurityGradeEntry } from './vendor-trust-center-security-grade-entry.component';

export interface TrustCenterItemProps {
    item: {
        id: string;
        name: string;
        description?: string;
        additionalFields?: {
            href?: string;
            listEntries?: {
                url?: string;
                label?: string;
                grade?: string;
                description?: string;
            }[];
            choiceAnswer?: string;
        };
    };
    categoryType?: SbCategoryTypeEnum;
    isLastItem: boolean;
}

export const TrustCenterItem = observer(
    ({
        item,
        categoryType,
        isLastItem,
    }: TrustCenterItemProps): React.JSX.Element => {
        const { id, name, description, additionalFields } = item;

        return (
            <Stack
                key={id}
                direction="column"
                data-id="Zp3xF6nY"
                gap="sm"
                data-testid="TrustCenterItem"
            >
                <Text type="title" size="200">
                    {name}
                </Text>

                {!isEmpty(description) && (
                    <MarkdownViewer data-id="Bw9qL2mK">
                        {description ?? ''}
                    </MarkdownViewer>
                )}

                {!isNil(additionalFields?.href) && (
                    <AppLink isExternal href={additionalFields.href}>
                        {additionalFields.href}
                    </AppLink>
                )}

                {categoryType === 'security_grades' &&
                    isArray(additionalFields?.listEntries) && (
                        <Stack direction="column" gap="sm">
                            {additionalFields.listEntries.map((entry) => (
                                <SecurityGradeEntry
                                    key={`${id}-${entry.label}-${entry.url}`}
                                    entry={entry}
                                    data-id="DNIh2R5E"
                                />
                            ))}
                        </Stack>
                    )}

                {!isNil(additionalFields?.choiceAnswer) && (
                    <Text>{additionalFields.choiceAnswer}</Text>
                )}

                {!isLastItem && (
                    <Box pt="3x">
                        <Divider />
                    </Box>
                )}
            </Stack>
        );
    },
);
