import {
    MonitoringTableCellActionsComponent,
    MonitoringTableCellCategoryComponent,
    MonitoringTableCellConnectionsComponent,
    MonitoringTableCellFindingsComponent,
    MonitoringTableCellResultComponent,
    MonitoringTableCellStatusComponent,
    MonitoringTableCellTestNameComponent,
} from '@components/monitoring';
import type { DatatableProps, FilterProps } from '@cosmos/components/datatable';
import type { MonitorTestInstanceResponseDto } from '@globals/api-sdk/types';

export const MONITORING_COLUMNS: DatatableProps<MonitorTestInstanceResponseDto>['columns'] =
    [
        {
            header: '',
            id: 'action',
            isActionColumn: true,
            accessorKey: 'testId',
            enableSorting: false,
            meta: { shouldIgnoreRowClick: true },
            cell: MonitoringTableCellActionsComponent,
        },
        {
            accessorKey: 'testName',
            header: 'Name',
            id: 'name',
            enableSorting: false,
            minSize: 300,
            cell: MonitoringTableCellTestNameComponent,
        },
        {
            accessorKey: 'connections',
            header: 'Result',
            id: 'checkResultStatus',
            enableSorting: true,
            cell: MonitoringTableCellResultComponent,
        },
        {
            accessorKey: 'findingsCount',
            header: 'Findings',
            id: 'findingsCount',
            enableSorting: true,
            cell: MonitoringTableCellFindingsComponent,
        },
        {
            accessorKey: 'connections',
            header: 'Status',
            id: 'checkStatus',
            enableSorting: true,
            meta: {
                shouldIgnoreRowClick: true,
            },
            cell: MonitoringTableCellStatusComponent,
        },
        {
            accessorKey: 'connections',
            header: 'Category',
            id: 'category',
            enableSorting: true,
            cell: MonitoringTableCellCategoryComponent,
        },
        {
            accessorKey: 'connections',
            header: 'Active connections',
            id: 'activeConnections',
            enableSorting: false,
            cell: MonitoringTableCellConnectionsComponent,
        },
    ];

export const MONITOR_CHECK_TYPE_ENUM = {
    POLICY: 'POLICY',
    IN_DRATA: 'IN_DRATA',
    AGENT: 'AGENT',
    INFRASTRUCTURE: 'INFRASTRUCTURE',
    IDENTITY: 'IDENTITY',
    VERSION_CONTROL: 'VERSION_CONTROL',
    TICKETING: 'TICKETING',
    OBSERVABILITY: 'OBSERVABILITY',
};

const TEST_TYPE_OPTIONS_ENUM = {
    DRATA: 'DRATA',
    CUSTOM_PUBLISHED: 'CUSTOM_PUBLISHED',
    CUSTOM_DRAFT: 'CUSTOM_DRAFT',
    DRATA_CUSTOM_PUBLISHED: 'DRATA_CUSTOM_PUBLISHED',
    DRATA_CUSTOM_DRAFT: 'DRATA_CUSTOM_DRAFT',
};

const CHECK_STATUS_ENUM = {
    ENABLED: 'ENABLED',
    DISABLED: 'DISABLED',
    UNUSED: 'UNUSED',
    TESTING: 'TESTING',
};

export const MONITORING_FILTERS: FilterProps = {
    clearAllButtonLabel: 'Reset',
    triggerLabel: 'Filters',
    filters: [
        {
            filterType: 'checkbox',
            id: 'isNew',
            label: '',
            options: [
                {
                    label: 'New',
                    value: 'NEW',
                },
            ],
        },
        {
            filterType: 'checkbox',
            id: 'allowedCheckResultStatuses',
            label: 'Result',
            options: [
                {
                    label: 'Passed',
                    value: 'PASSED',
                },
                {
                    label: 'Failed',
                    value: 'FAILED',
                },
                {
                    label: 'Error',
                    value: 'ERROR',
                },
            ],
        },
        {
            filterType: 'checkbox',
            id: 'allowedStatuses',
            label: 'Status',

            options: [
                {
                    label: 'Enabled',
                    value: CHECK_STATUS_ENUM.ENABLED,
                },
                {
                    label: 'Disabled',
                    value: CHECK_STATUS_ENUM.DISABLED,
                },
                {
                    label: 'Unused',
                    value: CHECK_STATUS_ENUM.UNUSED,
                },
                {
                    label: 'Testing',
                    value: CHECK_STATUS_ENUM.TESTING,
                },
            ],
        },
        {
            filterType: 'checkbox',
            id: 'allowedCategories',
            label: 'Category',

            options: [
                {
                    label: 'Device',
                    value: MONITOR_CHECK_TYPE_ENUM.AGENT,
                },
                {
                    label: 'Identity Provider',
                    value: MONITOR_CHECK_TYPE_ENUM.IDENTITY,
                },
                {
                    label: 'In Drata',
                    value: MONITOR_CHECK_TYPE_ENUM.IN_DRATA,
                },
                {
                    label: 'Infrastructure',
                    value: MONITOR_CHECK_TYPE_ENUM.INFRASTRUCTURE,
                },
                {
                    label: 'Observability',
                    value: MONITOR_CHECK_TYPE_ENUM.OBSERVABILITY,
                },
                {
                    label: 'Policy',
                    value: MONITOR_CHECK_TYPE_ENUM.POLICY,
                },
                {
                    label: 'Version Control',
                    value: MONITOR_CHECK_TYPE_ENUM.VERSION_CONTROL,
                },
                {
                    label: 'Ticketing',
                    value: MONITOR_CHECK_TYPE_ENUM.TICKETING,
                },
            ],
        },
        {
            filterType: 'checkbox',
            id: 'allowedTestSources',
            label: 'Type',
            options: [
                {
                    label: 'Drata',
                    value: TEST_TYPE_OPTIONS_ENUM.DRATA,
                },
                {
                    label: 'Custom (published)',
                    value: TEST_TYPE_OPTIONS_ENUM.CUSTOM_PUBLISHED,
                },
                {
                    label: 'Custom (draft)',
                    value: TEST_TYPE_OPTIONS_ENUM.CUSTOM_DRAFT,
                },
                {
                    label: 'Drata Custom (published)',
                    value: TEST_TYPE_OPTIONS_ENUM.DRATA_CUSTOM_PUBLISHED,
                },
                {
                    label: 'Drata Custom (draft)',
                    value: TEST_TYPE_OPTIONS_ENUM.DRATA_CUSTOM_DRAFT,
                },
            ],
        },
        {
            filterType: 'checkbox',
            id: 'hasExclusions',
            label: 'Exclusions',
            options: [
                {
                    label: 'Has Exclusions',
                    value: 'true',
                },
            ],
        },
        {
            filterType: 'combobox',
            id: 'allowedConnections',
            isMultiSelect: true,
            placeholder: 'Select all that apply',

            label: 'Connection',

            options: [],
        },
        {
            filterType: 'combobox',
            id: 'allowedControls',
            isMultiSelect: true,
            placeholder: 'Select all that apply',

            label: 'Control',

            options: [],
        },
        {
            filterType: 'combobox',
            id: 'allowedFrameworks',
            isMultiSelect: true,
            placeholder: 'Select all that apply',

            label: 'Framework',

            options: [],
        },
        {
            filterType: 'checkbox',
            id: 'allowedTicketStatuses',
            label: 'Tickets',
            options: [
                {
                    label: 'In Progress',
                    value: 'IN_PROGRESS',
                },
                {
                    label: 'Done',
                    value: 'ARCHIVED',
                },
            ],
        },
    ],
};
