import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { SchemaDropdown } from '@cosmos/components/schema-dropdown';
import type { CustomerRequestListItemResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { getStatusOptionsDropdownForTable } from './status.constants';

interface ActionsCellProps {
    row: { original: CustomerRequestListItemResponseDto };
}

export const ActionsCell = ({
    row: { original },
}: ActionsCellProps): React.JSX.Element => {
    return (
        <SchemaDropdown
            isIconOnly
            label={t`Actions`}
            level="tertiary"
            startIconName="Action"
            data-testid="ActionsCell"
            colorScheme="neutral"
            data-id=""
            items={getStatusOptionsDropdownForTable().filter((option) => {
                return option.id !== original.status;
            })}
            onSelectGlobalOverride={({ id }) => {
                sharedCustomerRequestDetailsController.updateCustomerRequestStatusWithNavigation(
                    id,
                    original.id,
                    true,
                );
            }}
        />
    );
};
