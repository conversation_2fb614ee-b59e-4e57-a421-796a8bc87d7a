import { Metadata } from '@cosmos/components/metadata';
import type { CustomerRequestListItemResponseDto } from '@globals/api-sdk/types';
import { getStatusMetadata } from '@models/request-details-page-header';

interface StatusCellProps {
    row: { original: CustomerRequestListItemResponseDto };
}

export const StatusCell = ({
    row: { original },
}: StatusCellProps): React.JSX.Element => {
    const { label, iconName, colorScheme } = getStatusMetadata(original.status);

    return (
        <Metadata
            label={label}
            type="status"
            iconName={iconName}
            colorScheme={colorScheme}
            data-testid="StatusCell"
            data-id="LKPytn3k"
        />
    );
};
