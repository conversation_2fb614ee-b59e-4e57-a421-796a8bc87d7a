import { Icon } from '@cosmos/components/icon';
import type {
    SchemaDropdownItemData,
    SchemaDropdownItems,
} from '@cosmos/components/schema-dropdown';
import { t } from '@globals/i18n/macro';

export type StatusOptionKey =
    | 'OUTSTANDING'
    | 'IN_REVIEW'
    | 'ACCEPTED'
    | 'DELETE';
export const getStatusOptionsForTable = (): Record<
    StatusOptionKey,
    SchemaDropdownItemData
> => ({
    OUTSTANDING: {
        id: 'OUTSTANDING',
        type: 'item',
        label: t`Change to New`,
    },
    IN_REVIEW: {
        id: 'IN_REVIEW',
        type: 'item',
        label: t`Change to Prepared`,
    },
    ACCEPTED: {
        id: 'ACCEPTED',
        type: 'item',
        label: t`Change to Completed`,
    },
    DELETE: {
        id: 'DELETE-REQUEST',
        type: 'item',
        label: t`Delete request`,
        colorScheme: 'critical',
        startSlot: <Icon name="Trash" size="200" />,
    },
});
export const getStatusOptionsDropdownForTable = (): SchemaDropdownItems =>
    Object.values(getStatusOptionsForTable());
