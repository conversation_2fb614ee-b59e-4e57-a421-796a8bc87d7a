import { sharedAuditorController } from '@controllers/auditor';
import { Modal } from '@cosmos/components/modal';
import { Text } from '@cosmos/components/text';
import { t, Trans } from '@globals/i18n/macro';

interface AuditHubAuditorClientAuditResourcesModalDetailsViewProps {
    onClose: () => void;
    isSubmitting: boolean;
    handleOnSubmit: () => void;
}

export const AuditHubAuditorClientAuditStatusModalView = ({
    onClose,
    isSubmitting,
    handleOnSubmit,
}: AuditHubAuditorClientAuditResourcesModalDetailsViewProps): React.JSX.Element => {
    const summaryData = sharedAuditorController.auditSummaryByIdData;

    const isAuditStatusCompleted =
        summaryData?.auditorFramework.status === 'COMPLETED';

    const headerTitle = isAuditStatusCompleted
        ? t`Are you sure?`
        : t`Complete audit`;

    const submitLabel = isAuditStatusCompleted ? t`Yes, I'm sure` : t`Complete`;

    return (
        <>
            <Modal.Header
                title={headerTitle}
                closeButtonAriaLabel={t`Generate audit resources`}
                onClose={onClose}
            />
            <Modal.Body>
                <Text type="title" size="200">
                    {isAuditStatusCompleted ? (
                        <Trans>
                            This will open the audit again for everyone who can
                            access it.
                        </Trans>
                    ) : (
                        <Trans>
                            If everything is in order, we&apos;ll move the audit
                            to <strong>Completed audits.</strong>{' '}
                            Congratulations on an audit well-done!
                        </Trans>
                    )}
                </Text>
            </Modal.Body>
            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Close`,
                        level: 'secondary',
                        onClick: onClose,
                    },
                    {
                        label: submitLabel,
                        isLoading: isSubmitting,
                        onClick: () => {
                            handleOnSubmit();
                            onClose();
                        },
                    },
                ]}
            />
        </>
    );
};
