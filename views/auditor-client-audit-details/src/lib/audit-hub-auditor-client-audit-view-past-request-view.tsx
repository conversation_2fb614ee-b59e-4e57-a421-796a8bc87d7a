import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { AuditHubAuditorClientAuditViewPastRequestTableView } from './audit-hub-auditor-client-audit-view-past-request-table-view';

interface AuditHubAuditorClientViewPastRequestViewProps {
    onClose: () => void;
}

export const AuditHubAuditorClientViewPastRequestView = ({
    onClose,
}: AuditHubAuditorClientViewPastRequestViewProps): React.JSX.Element => {
    return (
        <>
            <Modal.Header
                title={t`Download history`}
                closeButtonAriaLabel={t`Download history`}
                onClose={onClose}
            />
            <Modal.Body>
                <AuditHubAuditorClientAuditViewPastRequestTableView />
            </Modal.Body>
            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Close`,
                        level: 'secondary',
                        onClick: onClose,
                    },
                ]}
            />
        </>
    );
};
