import type { DatatableProps } from '@cosmos/components/datatable';
import type { CustomerRequestListItemResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { ActionsCell } from '../requests-table-cells/actions-cell';
import { ControlsCell } from '../requests-table-cells/controls-cell';
import { MessagesCell } from '../requests-table-cells/messages-cell';
import { OwnersCell } from '../requests-table-cells/owners-cell';
import { StatusCell } from '../requests-table-cells/status-cell';

const REQUESTS_COLUMN_SIZES = {
    SMALL: 100,
    MEDIUM: 200,
    LARGE: 300,
};

class CustomerRequestsTableColumnsModel {
    constructor() {
        makeAutoObservable(this);
    }

    get requestColumns(): DatatableProps<CustomerRequestListItemResponseDto>['columns'] {
        return [
            {
                accessorKey: 'id',
                header: '',
                id: 'actions',
                enableSorting: false,
                enableHiding: true,
                cell: ActionsCell,
                isActionColumn: true,
                meta: { shouldIgnoreRowClick: true },
                size: REQUESTS_COLUMN_SIZES.SMALL,
                minSize: REQUESTS_COLUMN_SIZES.SMALL,
            },
            {
                accessorKey: 'unreadMessages',
                header: t`Message`,
                id: 'message',
                enableSorting: false,
                cell: MessagesCell,
                size: REQUESTS_COLUMN_SIZES.SMALL,
                minSize: REQUESTS_COLUMN_SIZES.SMALL,
            },
            {
                accessorKey: 'status',
                header: t`Status`,
                id: 'status',
                enableSorting: true,
                cell: StatusCell,
                size: REQUESTS_COLUMN_SIZES.SMALL,
                minSize: REQUESTS_COLUMN_SIZES.SMALL,
            },
            {
                accessorKey: 'code',
                header: t`ID`,
                id: 'code',
                enableSorting: true,
                size: REQUESTS_COLUMN_SIZES.SMALL,
                minSize: REQUESTS_COLUMN_SIZES.SMALL,
            },
            {
                accessorKey: 'description',
                header: t`Title`,
                id: 'title',
                enableSorting: true,
                size: REQUESTS_COLUMN_SIZES.LARGE,
                minSize: REQUESTS_COLUMN_SIZES.LARGE,
            },
            {
                accessorKey: 'controls',
                header: t`Controls`,
                id: 'controls',
                enableSorting: true,
                cell: ControlsCell,
                size: REQUESTS_COLUMN_SIZES.MEDIUM,
                minSize: REQUESTS_COLUMN_SIZES.MEDIUM,
                meta: { shouldIgnoreRowClick: true },
            },
            {
                accessorKey: 'owners',
                header: t`Owners`,
                id: 'owners',
                enableSorting: false,
                cell: OwnersCell,
                size: REQUESTS_COLUMN_SIZES.MEDIUM,
                minSize: REQUESTS_COLUMN_SIZES.MEDIUM,
                meta: { shouldIgnoreRowClick: true },
            },
        ];
    }
}

export const sharedCustomerRequestsTableColumnsModel =
    new CustomerRequestsTableColumnsModel();
