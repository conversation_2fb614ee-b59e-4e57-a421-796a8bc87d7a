import { Modal } from '@cosmos/components/modal';
import { Text } from '@cosmos/components/text';
import { t, Trans } from '@globals/i18n/macro';

interface AuditHubAuditorClientAuditResourcesModalDetailsViewProps {
    onClose: () => void;
    isSubmitting: boolean;
    handleOnSubmit: () => void;
}

export const AuditHubAuditorClientAuditResourcesModalDetailsView = ({
    onClose,
    isSubmitting,
    handleOnSubmit,
}: AuditHubAuditorClientAuditResourcesModalDetailsViewProps): React.JSX.Element => {
    return (
        <>
            <Modal.Header
                title={t`Generate audit resources`}
                closeButtonAriaLabel={t`Generate audit resources`}
                onClose={onClose}
            />
            <Modal.Body>
                <Text type="title" size="200">
                    <Trans>
                        It usually takes 3 to 5 minutes but can take longer if
                        the file is large. We&apos;ll notify you with an email
                        when it&apos;s ready to download.
                    </Trans>
                </Text>
            </Modal.Body>
            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Close`,
                        level: 'secondary',
                        onClick: onClose,
                    },
                    {
                        label: t`Confirm`,
                        isLoading: isSubmitting,
                        onClick: () => {
                            handleOnSubmit();
                            onClose();
                        },
                    },
                ]}
            />
        </>
    );
};
