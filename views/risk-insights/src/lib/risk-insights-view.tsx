import { TreatmentOverviewCard } from '@components/risks';
import { Box } from '@cosmos/components/box';
import { Grid } from '@cosmos/components/grid';
import { AssessmentProgressCard } from './risk-insights-assessment-progress-card';
import { RiskInsightsFilters } from './risk-insights-filters';
import { RiskScoresCard } from './risk-insights-risk-scores-card';

export const RiskInsightsView = (): React.JSX.Element => {
    return (
        <Grid
            areas='"filters" "content"'
            columns="1"
            gapX="xl"
            gapY="3xl"
            data-testid="RiskInsightsView"
            data-id="0Ushkef6"
        >
            <Box gridArea="filters">
                <RiskInsightsFilters />
            </Box>
            <Box gridArea="content">
                <Grid
                    areas='"assessmentProgress treatmentOverview" "riskScores riskScores"'
                    columns="1fr 2fr"
                    gapX="2xl"
                    gapY="3xl"
                    align="stretch"
                >
                    <Box gridArea="assessmentProgress">
                        <AssessmentProgressCard />
                    </Box>
                    <Box gridArea="treatmentOverview">
                        <TreatmentOverviewCard />
                    </Box>
                    <Box gridArea="riskScores">
                        <RiskScoresCard />
                    </Box>
                </Grid>
            </Box>
        </Grid>
    );
};
