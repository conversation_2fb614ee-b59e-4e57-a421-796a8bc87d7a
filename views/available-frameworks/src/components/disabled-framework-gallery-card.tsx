import { useMemo } from 'react';
import {
    ACTIVATE_FRAMEWORK_MODAL_ID,
    ActivateFrameworkModalContent,
    FrameworkCard,
    type FrameworkCardVariant,
    getMetrics,
    sharedFrameworkReadinessToggleModel,
} from '@components/frameworks';
import {
    type DisabledFrameworkResponseDto,
    sharedFrameworksController,
} from '@controllers/frameworks';
import { modalController } from '@controllers/modal';
import type { Action } from '@cosmos/components/action-stack';
import type { FrameworkResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

const PRERELEASE_FRAMEWORK_TAGS: FrameworkResponseDto['tag'][] = ['NIS2'];

const MAP_CONTROLS_FRAMEWORK_TAGS: FrameworkResponseDto['tag'][] = [
    'COBIT',
    'FFIEC',
    'MSSSPA',
    'SOX_ITGC',
];

export const DisabledFrameworkGalleryCard = observer(
    ({
        row: framework,
    }: {
        row: DisabledFrameworkResponseDto;
    }): React.JSX.Element => {
        const { readinessToggle } = sharedFrameworkReadinessToggleModel;

        const { value, controlsLabel, requirementsLabel } = getMetrics(
            framework,
            readinessToggle === 'control',
        );

        let variant: FrameworkCardVariant = 'default';

        if (PRERELEASE_FRAMEWORK_TAGS.includes(framework.tag)) {
            variant = 'prerelease';
        }

        if (MAP_CONTROLS_FRAMEWORK_TAGS.includes(framework.tag)) {
            variant = 'map-controls';
        }

        const isNistCsfEnabled =
            sharedFrameworksController.isFrameworkEnabled('NISTCSF');

        if (framework.tag === 'NISTCSF2' && isNistCsfEnabled) {
            variant = 'activate';
        }

        const isPci3Enabled =
            sharedFrameworksController.isFrameworkEnabled('PCI');

        if (framework.tag === 'PCI4' && isPci3Enabled) {
            variant = 'activate';
        }

        const actionStack = useMemo(() => {
            let actions: Action[];

            switch (variant) {
                case 'activate': {
                    actions = [
                        {
                            actionType: 'button',
                            id: 'framework-card-button-action',
                            typeProps: {
                                label: t`Activate`,
                                size: 'md',
                                colorScheme: 'primary',
                                level: 'tertiary',
                                onClick: () => {
                                    modalController.openModal({
                                        id: ACTIVATE_FRAMEWORK_MODAL_ID,
                                        content: () => (
                                            <ActivateFrameworkModalContent
                                                data-id="MXT2836R"
                                                framework={framework}
                                            />
                                        ),
                                        centered: true,
                                        size: 'md',
                                    });
                                },
                            },
                        },
                    ];
                    break;
                }
                case 'default':
                case 'map-controls':
                case 'prerelease':
                default: {
                    actions = [
                        {
                            actionType: 'button',
                            id: 'framework-card-button-action',
                            typeProps: {
                                label: t`Learn more`,
                                size: 'md',
                                // this data-id is targeted by Chameleon
                                'data-id': 'learn-more-popup',
                                endIconName: 'ChevronRight',
                                colorScheme: 'primary',
                                level: 'tertiary',
                            },
                        },
                    ];
                    break;
                }
            }

            return [
                {
                    id: 'framework-card-action-stack',
                    actions,
                },
            ];
        }, [framework, variant]);

        return (
            <FrameworkCard
                framework={framework}
                value={value}
                controlsLabel={controlsLabel}
                requirementsLabel={requirementsLabel}
                variant={variant}
                data-id="CGk1y-mg"
                actionStack={actionStack}
            />
        );
    },
);
