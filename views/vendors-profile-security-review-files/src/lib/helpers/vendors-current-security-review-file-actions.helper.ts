import type { ComponentProps } from 'react';
import { openDeleteSecurityReviewFileModal } from '@components/vendors-current-security-review-files';
import type { ActionStack } from '@cosmos/components/action-stack';
import { t } from '@globals/i18n/macro';
import { VENDORS_PROFILE_SECURITY_REVIEW_FILE_PAGE_HEADER_KEY } from '../constants/vendors-profile-security-review-files.constants';

export const getVendorsProfileSecurityReviewFileActions = () =>
    [
        {
            actionType: 'button',
            id: `${VENDORS_PROFILE_SECURITY_REVIEW_FILE_PAGE_HEADER_KEY}-button`,
            typeProps: {
                isIconOnly: true,
                label: t`Delete`,
                colorScheme: 'danger',
                level: 'tertiary',
                startIconName: 'Trash',
                onClick: () => {
                    openDeleteSecurityReviewFileModal();
                },
            },
        },
    ] as const satisfies ComponentProps<typeof ActionStack>['actions'];
