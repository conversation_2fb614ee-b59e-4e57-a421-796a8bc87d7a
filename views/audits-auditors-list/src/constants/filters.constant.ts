import { sharedAuditorsController } from '@controllers/audits';
import type { FilterProps } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { computed } from '@globals/mobx';

export const getAuditorsListTableFilters = computed((): FilterProps => {
    return {
        clearAllButtonLabel: t`Reset`,
        filters: [
            {
                filterType: 'radio',
                id: 'appAccess',
                label: t`Application access`,
                options: [
                    {
                        label: t`None`,
                        value: 'NONE',
                    },
                    {
                        label: t`Read-only`,
                        value: 'READ_ONLY',
                    },
                    {
                        label: t`Read-only with downloads`,
                        value: 'READ_ONLY_DOWNLOADS',
                    },
                ],
            },
            {
                filterType: 'combobox',
                id: 'firmName',
                label: t`By firm`,
                placeholder: t`Search by firm name`,
                searchDebounce: 300,
                options: sharedAuditorsController.searchableFirmOptions,
                onFetchOptions: ({ search }) => {
                    sharedAuditorsController.searchFirmOptions({ search });
                },
            },
        ],
        triggerLabel: t`Filters`,
    } as const satisfies FilterProps;
});
