import { noop } from 'lodash-es';
import { AppDatatable } from '@components/app-datatable';
import { sharedAuditorsController } from '@controllers/audits';
import { Box } from '@cosmos/components/box';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getAuditorsListTableFilters } from '../constants/filters.constant';
import { AUDITOR_LIST_TABLE_COLUMNS } from '../constants/table-columns.constant';
import { openCreateAuditorModal } from '../helpers/open-create-auditor-modal';

export const AuditsAuditorsListView = observer((): React.JSX.Element => {
    const { auditors, total, isLoading, loadPage } = sharedAuditorsController;

    return (
        <Box data-testid="AuditsAuditorsListView" data-id="_g7hYO2R">
            <AppDatatable
                isLoading={isLoading}
                tableId="auditors-list"
                total={total}
                data={auditors}
                columns={AUDITOR_LIST_TABLE_COLUMNS}
                filterProps={getAuditorsListTableFilters.get()}
                data-testid="AuditsAuditorsListView"
                data-id="WG5SuueY"
                tableActions={[
                    {
                        actionType: 'button',
                        id: 'create-auditor-button',
                        typeProps: {
                            level: 'secondary',
                            label: t`Add auditor`,
                            onClick: openCreateAuditorModal,
                        },
                    },
                ]}
                filterViewModeProps={{
                    props: {
                        initialSelectedOption: 'unpinned',
                        togglePinnedLabel: 'Pin filters to page',
                        toggleUnpinnedLabel: 'Move filters to dropdown',
                        selectedOption: 'unpinned',
                    },
                    viewMode: 'toggleable',
                }}
                onFetchData={loadPage}
                onRowClick={noop}
            />
        </Box>
    );
});
