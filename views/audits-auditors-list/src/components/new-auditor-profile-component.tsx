import type React from 'react';
import { sharedAuditorsController } from '@controllers/audits';
import { modalController } from '@controllers/modal';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { zOldAuditorRequestDto } from '@globals/api-sdk/zod';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { AppLink } from '@ui/app-link';
import { Form, type FormSchema, useFormSubmit } from '@ui/forms';

const FORM_ID = 'create-auditor-profile-form';

const buildAuditorFormSchema = (): FormSchema => ({
    firstName: {
        type: 'text',
        label: t`First name`,
        validator: zOldAuditorRequestDto.shape.firstName
            .trim()
            .min(1, t`First name is required`),
    },
    lastName: {
        type: 'text',
        label: t`Last name`,
        validator: zOldAuditorRequestDto.shape.lastName
            .trim()
            .min(1, t`Last name is required`),
    },
    email: {
        type: 'text',
        label: t`Email`,
        validator: zOldAuditorRequestDto.shape.email,
    },
    firm: {
        type: 'text',
        label: t`Firm`,
        validator: zOldAuditorRequestDto.shape.firmName
            .trim()
            .min(1, t`Firm is required`),
    },
    grantAccess: {
        type: 'checkbox',
        label: t`Grant read-only access to the application`,
        helpText: t`Read-only access provides a view to your application, including data outside the scope of individual audits.`,
        isOptional: true,
    },
    allowDownloads: {
        type: 'checkbox',
        label: t`Allow test, control, and requirement downloads`,
        helpText: t`Downloads require read-only access to the application.`,
        isOptional: true,
        shownIf: {
            fieldName: 'grantAccess',
            operator: 'equals',
            value: true,
        },
    },
});

export const NewAuditorProfileComponent = observer((): React.JSX.Element => {
    const { formRef, triggerSubmit } = useFormSubmit();

    return (
        <>
            <Modal.Header
                title={t`Create auditor`}
                closeButtonAriaLabel={t`Close create auditor`}
                data-id="create-auditor-modal-header"
                onClose={() => {
                    modalController.closeModal('create-auditor-modal');
                }}
            />
            <Modal.Body>
                <Stack direction="column" gap="2xl">
                    <Form
                        hasExternalSubmitButton
                        ref={formRef}
                        formId={FORM_ID}
                        schema={buildAuditorFormSchema()}
                        data-id="create-auditor-form"
                        onSubmit={
                            sharedAuditorsController.handleCreateAuditorFormSubmission
                        }
                    />

                    <Text
                        size="200"
                        type="body"
                        colorScheme="neutral"
                        data-id="auditor-experience-link"
                    >
                        <AppLink
                            isExternal
                            href="https://help.drata.com/en/articles/6928357-auditor-experience"
                        >
                            {t`Learn more about the auditor experience`}
                        </AppLink>
                    </Text>
                </Stack>
            </Modal.Body>
            <Modal.Footer
                data-id="create-auditor-modal-footer"
                rightActionStack={[
                    {
                        label: t`Close`,
                        level: 'secondary',
                        onClick: () => {
                            modalController.closeModal('create-auditor-modal');
                        },
                    },
                    {
                        label: t`Save`,
                        level: 'primary',
                        colorScheme: 'primary',
                        isLoading:
                            sharedAuditorsController.createAuditorIsLoading,
                        onClick: () => {
                            triggerSubmit();
                        },
                    },
                ]}
            />
        </>
    );
});
