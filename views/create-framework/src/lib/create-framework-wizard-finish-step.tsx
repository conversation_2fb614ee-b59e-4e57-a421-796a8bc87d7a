import { Callout } from '@cosmos-lab/components/callout';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export const CreateFrameworkWizardConfirmStep = observer((): JSX.Element => {
    return (
        <Callout
            data-testid="CreateFrameworkWizardConfirmStep"
            data-id="gLj2_IHa"
            illustrationName="Highfive"
            size="lg"
            primaryLabelText={t`Congratulations! Your Framework is Ready.`}
            secondaryLabelText={t`Enjoy tracking your compliance with Drata! You can map controls and add more requirements at any time.`}
        />
    );
});
