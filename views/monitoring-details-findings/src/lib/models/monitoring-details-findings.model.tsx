import { sharedMonitoringFindingsReportController } from '@controllers/monitoring';
import {
    activeMonitoringDetailsController,
    sharedFindingsFiltersController,
} from '@controllers/monitoring-details';
import type { BulkAction } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import {
    CPSM_TEST_IDS,
    MALWARE_DETECTION_SOFTWARE_INSTALLED,
    MONITOR_CHECK_TYPE_ENUM,
    VULNERABILITY_MONITORING_TEST_IDS,
} from '@views/monitoring';
import { getDatatableStructure } from '../helpers/structure-helpers';

class MonitoringDetailsFindingsModel {
    constructor() {
        makeAutoObservable(this);
    }

    CATEGORIES_ALLOWED_DOWNLOAD_CSV = [
        MONITOR_CHECK_TYPE_ENUM.OBSERVABILITY,
        MONITOR_CHECK_TYPE_ENUM.INFRASTRUCTURE,
    ];

    isSpecificTestId = (testId: number) => {
        return [
            ...(CPSM_TEST_IDS as readonly number[]),
            ...(VULNERABILITY_MONITORING_TEST_IDS as readonly number[]),
            MALWARE_DETECTION_SOFTWARE_INSTALLED,
        ].includes(testId);
    };

    isAllowedCategory = (category: string) => {
        return this.CATEGORIES_ALLOWED_DOWNLOAD_CSV.includes(category);
    };

    get allowBulkActions() {
        const { monitorDetailsData } = activeMonitoringDetailsController;

        if (!monitorDetailsData) {
            return false;
        }

        return (
            this.isAllowedCategory(monitorDetailsData.category) ||
            this.isSpecificTestId(monitorDetailsData.testId)
        );
    }

    get getMonitoringDetailsFindingsBulkActions(): BulkAction[] {
        if (!this.allowBulkActions) {
            return [];
        }

        const actions: BulkAction[] = [];

        if (this.canManageExclusions) {
            actions.push({
                actionType: 'button',
                id: 'bulk-actions-findings-exclude',
                typeProps: {
                    label: t`Exclude`,
                    level: 'tertiary',
                    onClick: () => {
                        console.warn('Implement exclude bulk action');
                    },
                },
            });
        }

        actions.push({
            actionType: 'button',
            id: 'bulk-actions-findings-download-csv',
            typeProps: {
                label: t`Download CSV`,
                level: 'tertiary',
                onClick: this.handleCsvDownload,
            },
        });

        return actions;
    }

    handleCsvDownload = () => {
        const { monitorDetailsData } = activeMonitoringDetailsController;

        if (!monitorDetailsData) {
            return;
        }

        if (
            monitorDetailsData.testId === MALWARE_DETECTION_SOFTWARE_INSTALLED
        ) {
            sharedMonitoringFindingsReportController.downloadEdrFailedTestReport(
                monitorDetailsData.testId,
            );

            return;
        }

        if (
            monitorDetailsData.category ===
                MONITOR_CHECK_TYPE_ENUM.OBSERVABILITY &&
            CPSM_TEST_IDS.includes(
                monitorDetailsData.testId as (typeof CPSM_TEST_IDS)[number],
            )
        ) {
            sharedMonitoringFindingsReportController.downloadCspmFailedTestReport(
                monitorDetailsData.testId,
            );

            return;
        }

        if (this.isAllowedCategory(monitorDetailsData.category)) {
            sharedMonitoringFindingsReportController.downloadFailedFindings({
                testId: monitorDetailsData.testId,
                testName: monitorDetailsData.testName,
                checkType: monitorDetailsData.category,
            });
        }
    };

    get datatableStructure(): { columns: unknown[]; filters: unknown[] } {
        const { findingsFilters, isLoadingFindingsFilters } =
            sharedFindingsFiltersController;

        if (isLoadingFindingsFilters || !findingsFilters) {
            return {
                columns: [],
                filters: [],
            };
        }

        const {
            category,
            source,
            testId,
            additionalProperties,
            filters: filterValues,
        } = findingsFilters;

        return getDatatableStructure({
            category,
            source,
            testId,
            filtersValues: Object.fromEntries(
                Object.entries(filterValues).map(([key, value]) => [
                    key,
                    Array.isArray(value) ? value : [value],
                ]),
            ),
            additionalProperties: additionalProperties.map((a) => ({
                id: a.id,
            })),
        });
    }

    get canManageExclusions(): boolean {
        return (
            sharedFindingsFiltersController.findingsFilters
                ?.canManageExclusions ?? false
        );
    }
}

export const sharedMonitoringDetailsFindingsModel =
    new MonitoringDetailsFindingsModel();
