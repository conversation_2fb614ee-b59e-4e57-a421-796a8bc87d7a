import {
    CPSM_TEST_IDS,
    VULNERABILITY_MONITORING_TEST_IDS,
} from '@views/monitoring';
import {
    getCaCColumns,
    getCustomColumns,
    getEDRColumns,
    getIdentityColumns,
    getInDrataColumns,
    getInfrastructureColumns,
    getPolicyColumns,
    getTicketingColumns,
    getVersionControlColumns,
    getVulnerabilityColumns,
    getWizColumns,
} from './columns-helpers';
import {
    getAgentFilters,
    getCaCFilters,
    getCustomFilters,
    getEDRFilters,
    getIdentityFilters,
    getInDrataFilters,
    getInfrastructureFilters,
    getPolicyFilters,
    getTicketingFilters,
    getVersionControlFilters,
    getVulnerabilityFilters,
    getWizFilters,
} from './filters-helpers';

export const getDatatableStructure = ({
    category,
    source,
    testId,
    filtersValues,
    additionalProperties,
}: {
    category: unknown;
    source: unknown;
    testId: unknown;
    filtersValues: Record<string, unknown[]>;
    additionalProperties: { id: unknown }[];
}): { columns: unknown[]; filters: unknown[] } => {
    // Create a mapping of category/source/testId combinations to their respective handlers
    const structureMap: Record<
        string,
        () => { columns: unknown[]; filters: unknown[] }
    > = {
        INFRASTRUCTURE_ACORN: () => ({
            columns: getCaCColumns(),
            filters: getCaCFilters(filtersValues as Record<string, string[]>),
        }),
        INFRASTRUCTURE: () => ({
            columns: getInfrastructureColumns(),
            filters: getInfrastructureFilters(
                filtersValues as Record<string, string[]>,
            ),
        }),
        OBSERVABILITY_CPSM: () => ({
            columns: getWizColumns(),
            filters: getWizFilters(filtersValues as Record<string, string[]>),
        }),
        OBSERVABILITY_VULNERABILITY: () => ({
            columns: getVulnerabilityColumns(),
            filters: getVulnerabilityFilters(
                filtersValues as Record<string, string[]>,
            ),
        }),
        OBSERVABILITY: () => ({
            columns: getInfrastructureColumns(),
            filters: getInfrastructureFilters(
                filtersValues as Record<string, string[]>,
            ),
        }),
        POLICY: () => ({
            columns: getPolicyColumns(),
            filters: getPolicyFilters(),
        }),
        AGENT_MALWARE_DETECTION: () => ({
            columns: getEDRColumns(
                additionalProperties.map((prop) => prop.id as string),
            ),
            filters: getEDRFilters(filtersValues as Record<string, string[]>),
        }),
        AGENT: () => ({
            columns: getInDrataColumns(),
            filters: getAgentFilters(),
        }),
        CUSTOM: () => ({
            columns: getCustomColumns(),
            filters: getCustomFilters(),
        }),
        IDENTITY: () => ({
            columns: getIdentityColumns(),
            filters: getIdentityFilters(
                filtersValues as Record<string, string[]>,
            ),
        }),
        TICKETING: () => ({
            columns: getTicketingColumns(),
            filters: getTicketingFilters(
                filtersValues as Record<string, string[]>,
            ),
        }),
        VERSION_CONTROL: () => ({
            columns: getVersionControlColumns(),
            filters: getVersionControlFilters(),
        }),
        IN_DRATA: () => ({
            columns: getInDrataColumns(),
            filters: getInDrataFilters(),
        }),
    };

    // Generate a key based on the input parameters
    let key = category as string;

    if (category === 'INFRASTRUCTURE' && source === 'ACORN') {
        key = 'INFRASTRUCTURE_ACORN';
    } else if (category === 'OBSERVABILITY') {
        if (CPSM_TEST_IDS.includes(testId as (typeof CPSM_TEST_IDS)[number])) {
            key = 'OBSERVABILITY_CPSM';
        } else if (
            VULNERABILITY_MONITORING_TEST_IDS.includes(
                testId as (typeof VULNERABILITY_MONITORING_TEST_IDS)[number],
            )
        ) {
            key = 'OBSERVABILITY_VULNERABILITY';
        }
    } else if (
        category === 'AGENT' &&
        testId === 'MALWARE_DETECTION_SOFTWARE_INSTALLED'
    ) {
        key = 'AGENT_MALWARE_DETECTION';
    }

    // Return the structure for the key or default to INFRASTRUCTURE
    return (structureMap[key] ?? structureMap.INFRASTRUCTURE)();
};
