import { isEmpty } from 'lodash-es';
import { useCallback } from 'react';
import { AppDatatable } from '@components/app-datatable';
import {
    activeMonitoringCodeDetailsController,
    sharedFindingsController,
    sharedFindingsFiltersController,
} from '@controllers/monitoring-details';
import { panelController } from '@controllers/panel';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import type {
    DatatableProps,
    FetchDataResponseParams,
    FilterProps,
} from '@cosmos/components/datatable';
import { EmptyState } from '@cosmos/components/empty-state';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { breakpointMd } from '@cosmos/constants/tokens';
import type { FindingItemResponseDto } from '@globals/api-sdk/types';
import { observer, runInAction } from '@globals/mobx';
import {
    MONITORING_DETAILS_FINDINGS_PANEL_ID,
    MonitoringDetailsFindingsPanelView,
    sharedMonitoringDetailsFindingsPanelModel,
} from '@views/monitoring-details-findings-panel';
import { sharedMonitoringDetailsFindingsModel } from './models/monitoring-details-findings.model';

export const MonitoringDetailsFindingsView = observer((): React.JSX.Element => {
    const {
        resultStatus,
        monitoringControlInstance,
        isLoading: isLoadingMonitoringControlInstance,
    } = activeMonitoringCodeDetailsController;

    const { findingsList, findingsListTotal, isLoadingFindingsList } =
        sharedFindingsController;

    const { findingsFilters, isLoadingFindingsFilters } =
        sharedFindingsFiltersController;
    const currentTestId = findingsFilters?.testId;

    const preventSelectAllAcrossPages = useCallback(() => true, []);

    const openFindingPanel = useCallback(
        ({ row }: { row: FindingItemResponseDto }) => {
            runInAction(() => {
                sharedMonitoringDetailsFindingsPanelModel.selectedFindingItem =
                    row;
                panelController.openPanel({
                    id: MONITORING_DETAILS_FINDINGS_PANEL_ID,
                    queryParams: {
                        panelEntityId: row.id,
                        panelEntityFindingId: row.findingId,
                        panelConnectionId: row.connectionId,
                    },
                    content: () => (
                        <MonitoringDetailsFindingsPanelView data-id="finding-details-panel" />
                    ),
                });
            });
        },
        [],
    );

    const loadFindings = useCallback(
        (params: FetchDataResponseParams) => {
            if (currentTestId) {
                sharedFindingsController.load({
                    testId: Number(currentTestId),
                    ...params,
                });
            }
        },
        [currentTestId],
    );

    if (
        isLoadingFindingsFilters ||
        isLoadingMonitoringControlInstance ||
        isEmpty(findingsFilters)
    ) {
        return <Loader isSpinnerOnly label={'Loading...'} />;
    }

    if (resultStatus === 'ERROR') {
        return (
            <Stack
                data-testid="MonitoringDetailsFindingsViewErrorResultStatus"
                data-id="mEYSmngD"
                height="75%"
                justify="center"
                align="center"
            >
                <Box width={breakpointMd}>
                    <EmptyState
                        title="No findings due to error"
                        description="This test was unable to generate findings due to an error that prevented it from running. Please review the details of the error to resolve it and try again."
                        data-id="mEYSmngD"
                        data-testid="MonitoringDetailsFindingsViewErrorResultStatus"
                        illustrationName="Warning"
                        leftAction={
                            <Button
                                href={`/compliance/monitoring/details/${monitoringControlInstance?.id}/overview`}
                                label="View error details"
                                level="secondary"
                            />
                        }
                    />
                </Box>
            </Stack>
        );
    }

    const {
        getMonitoringDetailsFindingsBulkActions,
        datatableStructure: { columns: structureColumns, filters },
    } = sharedMonitoringDetailsFindingsModel;

    return (
        <AppDatatable
            getRowId={(row) => row.findingId}
            isRowSelectionEnabled={preventSelectAllAcrossPages}
            tableId="datatable-findings"
            data-id="datatable-findings"
            data={findingsList}
            total={findingsListTotal}
            data-testid="MonitoringDetailsFindingsView"
            isLoading={isLoadingFindingsList || isLoadingFindingsFilters}
            bulkActionDropdownItems={getMonitoringDetailsFindingsBulkActions}
            columns={
                structureColumns as DatatableProps<FindingItemResponseDto>['columns']
            }
            filterProps={
                isEmpty(filters)
                    ? undefined
                    : {
                          filters: filters as FilterProps['filters'],
                          clearAllButtonLabel: 'Reset filters',
                          triggerLabel: 'Filter',
                      }
            }
            emptyStateProps={{
                title: 'No findings for this test',
                description:
                    'Test results you need to address will appear here.',
                illustrationName: 'AddCircle',
            }}
            filterViewModeProps={{
                props: {
                    selectedOption: 'pinned',
                    initialSelectedOption: 'pinned',
                    togglePinnedLabel: 'Pin filters to page',
                    toggleUnpinnedLabel: 'Move filters to dropdown',
                },
                viewMode: 'toggleable',
            }}
            onRowClick={openFindingPanel}
            onFetchData={loadFindings}
        />
    );
});
