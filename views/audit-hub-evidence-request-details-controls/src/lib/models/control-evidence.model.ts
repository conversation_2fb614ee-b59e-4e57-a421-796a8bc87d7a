import { groupBy } from 'lodash-es';
import type { EVIDENCE_TYPE_API_VALUE_MAP } from '@controllers/audit-hub';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import type { AuditHubEvidenceResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import type { EvidenceRowData } from '../constants';
import { EVIDENCE_TYPES } from '../constants/evidence.constants';

export interface TransformedEvidence extends AuditHubEvidenceResponseDto {
    groupedType: string;
    displayType: string;
}

export class ControlEvidenceModel {
    /**
     * Evidence types that should be grouped as manual evidence.
     */
    readonly EVIDENCE_LIBRARY: keyof typeof EVIDENCE_TYPE_API_VALUE_MAP =
        'Evidence Library';
    readonly MISCELLANEOUS_EVIDENCE: keyof typeof EVIDENCE_TYPE_API_VALUE_MAP =
        'Miscellaneous Evidence';

    /**
     * Column size constants for table layout.
     */
    readonly COLUMN_SIZES = {
        BASE_NAME_COLUMN: 250,
        MANUAL_ARTIFACT_COLUMN: 180,
        MANUAL_TYPE_COLUMN: 80,
        DATE_COLUMN: 140,
        ACTIONS_COLUMN: 80,
    } as const;

    constructor() {
        makeAutoObservable(this);
    }

    /**
     * Helper method to group Evidence Library and Miscellaneous Evidence as Manual evidence.
     */
    getGroupedEvidenceType(evidenceType: string): string {
        if (
            evidenceType === this.EVIDENCE_LIBRARY ||
            evidenceType === this.MISCELLANEOUS_EVIDENCE
        ) {
            return EVIDENCE_TYPES.MANUAL_EVIDENCE;
        }

        return evidenceType;
    }

    /**
     * Check if the given evidence type is a manual evidence type.
     */
    isManualEvidenceType(evidenceType: string): boolean {
        return evidenceType === EVIDENCE_TYPES.MANUAL_EVIDENCE;
    }

    /**
     * Calculate the name column size based on evidence type.
     */
    getNameColumnSize(evidenceType: string): number {
        const isManual = this.isManualEvidenceType(evidenceType);
        const manualColumnsWidth =
            this.COLUMN_SIZES.MANUAL_ARTIFACT_COLUMN +
            this.COLUMN_SIZES.MANUAL_TYPE_COLUMN;

        return isManual
            ? this.COLUMN_SIZES.BASE_NAME_COLUMN
            : this.COLUMN_SIZES.BASE_NAME_COLUMN + manualColumnsWidth;
    }

    /**
     * Get the appropriate date header based on evidence type.
     */
    getDateHeader(evidenceType: string): string {
        switch (evidenceType) {
            case EVIDENCE_TYPES.POLICIES: {
                return t`Published date`;
            }
            case EVIDENCE_TYPES.TEST_EVIDENCE: {
                return t`Run date`;
            }
            default: {
                return t`Creation date`;
            }
        }
    }

    /**
     * Helper method to determine if evidence should be displayed as URL or File.
     */
    getEvidenceDisplayType(evidence: AuditHubEvidenceResponseDto): string {
        if (evidence.artifact.includes('Auto Generated')) {
            return 'URL';
        }

        return t`File`;
    }

    /**
     * Transform evidences to group Evidence Library and Miscellaneous Evidence as Manual evidence.
     */
    get transformedEvidences(): TransformedEvidence[] {
        const { controlEvidences } = sharedCustomerRequestDetailsController;

        return controlEvidences.map((evidence) => ({
            ...evidence,
            groupedType: this.getGroupedEvidenceType(evidence.type),
            displayType: this.getEvidenceDisplayType(evidence),
        }));
    }

    /**
     * Group evidences by the transformed type.
     */
    get groupedEvidences(): Record<string, TransformedEvidence[]> {
        return groupBy(this.transformedEvidences, 'groupedType');
    }

    /**
     * Get sorted evidence types for display.
     */
    get evidenceTypes(): string[] {
        return Object.keys(this.groupedEvidences).sort((a, b) =>
            a.localeCompare(b),
        );
    }

    /**
     * Get evidences for a specific type.
     */
    getEvidencesOfType(evidenceType: string): TransformedEvidence[] {
        return this.groupedEvidences[evidenceType] ?? [];
    }

    /**
     * Transform evidence data for table display.
     */
    getTableDataForType(evidenceType: string): EvidenceRowData[] {
        const evidencesOfType = this.getEvidencesOfType(evidenceType);

        return evidencesOfType.map(
            (evidence, index): EvidenceRowData => ({
                name: evidence.name || t`Unknown Evidence`,
                artifact: evidence.artifact,
                displayType: evidence.displayType,
                date: evidence.date,
                evidenceType,
                index,
                originalEvidence: evidence,
            }),
        );
    }

    /**
     * Generate table ID for a specific evidence type.
     */
    getTableId(evidenceType: string): string {
        return `control-evidence-table-${evidenceType.toLowerCase().replaceAll(/\s+/g, '-')}`;
    }

    /**
     * Generate test ID for a specific evidence type.
     */
    getTestId(evidenceType: string): string {
        return `ControlEvidenceTable-${evidenceType}`;
    }
}

export const sharedControlEvidenceModel = new ControlEvidenceModel();
