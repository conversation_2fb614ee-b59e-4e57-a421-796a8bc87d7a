import type { DatatableProps } from '@cosmos/components/datatable';
import { Text } from '@cosmos/components/text';
import { DateTime } from '@cosmos-lab/components/date-time';
import { t } from '@globals/i18n/macro';
import { EvidenceActionsCell } from '../cells/evidence-actions-cell';
import { sharedControlEvidenceModel } from '../models/control-evidence.model';
import type { EvidenceRowData } from './evidence.constants';

/**
 * Generates column definitions for the control evidence table.
 * This function creates appropriate columns based on the evidence type.
 */
export function getControlEvidenceColumns(
    evidenceType: string,
): DatatableProps<EvidenceRowData>['columns'] {
    const model = sharedControlEvidenceModel;

    const isManualEvidence = model.isManualEvidenceType(evidenceType);
    const nameColumnSize = model.getNameColumnSize(evidenceType);

    // Create name column
    const nameColumn: DatatableProps<EvidenceRowData>['columns'][0] = {
        accessorKey: 'name',
        header: t`Name`,
        id: 'name',
        enableSorting: false,
        size: nameColumnSize,
        cell: ({ row }) => (
            <Text
                size="200"
                type="body"
                colorScheme="neutral"
                data-testid="EvidenceNameCell"
                data-id="5cmr45A9"
            >
                {row.original.name || t`Unknown Evidence`}
            </Text>
        ),
    };

    const columns: DatatableProps<EvidenceRowData>['columns'] = [nameColumn];

    // Manual evidence specific columns
    if (isManualEvidence) {
        const manualEvidenceColumns: DatatableProps<EvidenceRowData>['columns'] =
            [
                {
                    accessorKey: 'artifact',
                    header: t`Artifact`,
                    id: 'artifact',
                    enableSorting: false,
                    size: model.COLUMN_SIZES.MANUAL_ARTIFACT_COLUMN,
                    cell: ({ row }) => (
                        <Text
                            size="200"
                            type="body"
                            colorScheme="neutral"
                            data-testid="EvidenceArtifactCell"
                            data-id="W2-nUsH4"
                        >
                            {row.original.artifact || ''}
                        </Text>
                    ),
                },
                {
                    accessorKey: 'displayType',
                    header: t`Type`,
                    id: 'type',
                    enableSorting: false,
                    size: model.COLUMN_SIZES.MANUAL_TYPE_COLUMN,
                    cell: ({ row }) => (
                        <Text
                            size="200"
                            type="body"
                            colorScheme="neutral"
                            data-testid="EvidenceTypeCell"
                            data-id="AimpaPMC"
                        >
                            {row.original.displayType || ''}
                        </Text>
                    ),
                },
            ];

        columns.push(...manualEvidenceColumns);
    }

    // Add date column with appropriate header
    const dateColumn: DatatableProps<EvidenceRowData>['columns'][0] = {
        accessorKey: 'date',
        header: model.getDateHeader(evidenceType),
        id: 'date',
        enableSorting: false,
        size: model.COLUMN_SIZES.DATE_COLUMN,
        cell: ({ row }) => {
            const dateValue = row.original.date;

            if (!dateValue) {
                return null;
            }

            return (
                <DateTime
                    date={dateValue.split('T')[0]}
                    format="table"
                    data-id="i6JhNccT"
                />
            );
        },
    };

    // Actions column
    const actionsColumn: DatatableProps<EvidenceRowData>['columns'][0] = {
        accessorKey: 'index',
        header: t`Actions`,
        id: 'actions',
        enableSorting: false,
        size: model.COLUMN_SIZES.ACTIONS_COLUMN,
        isActionColumn: true,
        cell: ({ row }) => <EvidenceActionsCell row={row} data-id="QZb-CSCY" />,
        meta: {
            shouldIgnoreRowClick: true,
        },
    };

    columns.push(dateColumn, actionsColumn);

    return columns;
}
