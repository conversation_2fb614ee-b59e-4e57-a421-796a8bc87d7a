import type { EVIDENCE_TYPE_API_VALUE_MAP } from '@controllers/audit-hub';
import type { AuditHubEvidenceResponseDto } from '@globals/api-sdk/types';

export const EVIDENCE_TYPES = {
    MANUAL_EVIDENCE: 'Manual evidence',
    POLICIES: 'Policies' as keyof typeof EVIDENCE_TYPE_API_VALUE_MAP,
    TEST_EVIDENCE: 'Test Evidence' as keyof typeof EVIDENCE_TYPE_API_VALUE_MAP,
} as const;

export interface EvidenceRowData {
    name: string;
    artifact?: string;
    displayType?: string;
    date?: string;
    evidenceType: string;
    index: number;
    originalEvidence: AuditHubEvidenceResponseDto;
}

export const isManualEvidenceType = (evidenceType: string): boolean => {
    return evidenceType === EVIDENCE_TYPES.MANUAL_EVIDENCE;
};
