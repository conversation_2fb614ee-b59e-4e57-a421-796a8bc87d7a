import { isNil } from 'lodash-es';
import {
    sharedControlDetailsController,
    sharedControlPoliciesController,
    sharedControlsDownloadController,
} from '@controllers/controls';
import { modalController } from '@controllers/modal';
import { Button } from '@cosmos/components/button';
import type { DatatableProps, TableAction } from '@cosmos/components/datatable';
import type { PolicyWithControlWorkspaceGroupResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, runInAction } from '@globals/mobx';
import { COLUMN_SIZES } from '@helpers/table';
import { sharedResetPoliciesModalModel } from '@models/controls';
import {
    LinkedWorkspacesCell,
    PoliciesCompareToDefaultsModal,
    PolicyActionsCell,
    PolicyOwnerCell,
    PolicyRenewalDateCell,
    PolicyStatusCell,
    PolicyVersionCell,
} from '../components';
import { openMapPoliciesModal } from '../helpers/map-policies-modal.helper';

class ControlsPoliciesViewModel {
    constructor() {
        makeAutoObservable(this);
    }

    get displayCompareToDefaults(): boolean {
        const { hasControlTemplatePermission } = sharedFeatureAccessModel;
        const { controlDetails } = sharedControlDetailsController;
        const isTemplatedControl = !isNil(
            controlDetails?.fk_control_template_id,
        );

        return hasControlTemplatePermission && isTemplatedControl;
    }

    get emptyStateProps(): DatatableProps<PolicyWithControlWorkspaceGroupResponseDto>['emptyStateProps'] {
        return {
            illustrationName: 'AddPage',
            title: t`Map policies to this control`,
            description: t`Controls help enforce your policies.`,
            leftAction: this.displayCompareToDefaults ? (
                <Button
                    label={t`Compare to defaults`}
                    level="tertiary"
                    onClick={() => {
                        const { controlId } = sharedControlPoliciesController;

                        if (controlId) {
                            sharedResetPoliciesModalModel.loadPolicyComparison(
                                controlId,
                            );
                        }

                        modalController.openModal({
                            id: 'policy-compare-to-defaults-modal',
                            size: 'lg',
                            content: () => (
                                <PoliciesCompareToDefaultsModal data-id="O78iVRjN" />
                            ),
                        });
                    }}
                />
            ) : undefined,
            rightAction: (
                <Button
                    label={t`Map policies`}
                    level="primary"
                    onClick={openMapPoliciesModal}
                />
            ),
        };
    }

    get tableSearchProps(): DatatableProps<PolicyWithControlWorkspaceGroupResponseDto>['tableSearchProps'] {
        return {
            hideSearch: true,
        };
    }

    get columns(): DatatableProps<PolicyWithControlWorkspaceGroupResponseDto>['columns'] {
        return [
            {
                id: 'actions',
                isActionColumn: true,
                enableSorting: false,
                meta: { shouldIgnoreRowClick: true },
                cell: PolicyActionsCell,
                maxSize: COLUMN_SIZES.SMALL,
                minSize: COLUMN_SIZES.SMALL,
            },
            {
                accessorKey: 'policy.name',
                header: t`Name`,
                id: 'name',
                enableSorting: true,
                minSize: COLUMN_SIZES.LARGE,
                maxSize: COLUMN_SIZES.LARGE,
            },
            {
                accessorKey: 'policy',
                header: t`Version`,
                id: 'version',
                enableSorting: true,
                cell: PolicyVersionCell,
                minSize: COLUMN_SIZES.MEDIUM,
                maxSize: COLUMN_SIZES.MEDIUM,
            },
            {
                accessorKey: 'policy',
                header: t`Status`,
                id: 'status',
                enableSorting: true,
                cell: PolicyStatusCell,
                minSize: COLUMN_SIZES.MEDIUM,
                maxSize: COLUMN_SIZES.MEDIUM,
            },
            {
                header: t`Renewal date`,
                id: 'renewalDate',
                accessorKey: 'policy',
                enableSorting: true,
                cell: PolicyRenewalDateCell,
                minSize: COLUMN_SIZES.LARGE,
                maxSize: COLUMN_SIZES.LARGE,
            },
            {
                accessorKey: 'policy',
                header: t`Owner`,
                id: 'owner',
                enableSorting: true,
                cell: PolicyOwnerCell,
                minSize: COLUMN_SIZES.LARGE,
                maxSize: COLUMN_SIZES.LARGE,
            },
            {
                accessorKey: 'controlWorkspaceGroup',
                header: t`Linked workspaces`,
                id: 'linkedWorkspaces',
                enableSorting: false,
                cell: LinkedWorkspacesCell,
                minSize: COLUMN_SIZES.LARGE,
                maxSize: COLUMN_SIZES.LARGE,
            },
        ];
    }

    handleDownloadPolicies = (): void => {
        const { controlId } = sharedControlPoliciesController;
        const { downloadControlPolicies } = sharedControlsDownloadController;

        if (controlId) {
            runInAction(() => {
                downloadControlPolicies(controlId);
            });
        }
    };

    get tableActions(): TableAction[] {
        const { total } = sharedControlPoliciesController;
        const hasPolicies = total > 0;

        if (!hasPolicies) {
            return [];
        }

        return [
            {
                actionType: 'button',
                id: 'download-button',
                typeProps: {
                    startIconName: 'Download',
                    level: 'tertiary',
                    colorScheme: 'neutral',
                    label: t`Download`,
                    isLoading:
                        sharedControlsDownloadController.isDownloadControlPoliciesLoading,
                    onClick: this.handleDownloadPolicies,
                },
            } satisfies TableAction,
            ...(this.displayCompareToDefaults
                ? [
                      {
                          actionType: 'button',
                          id: 'compare-to-defaults-button',
                          typeProps: {
                              level: 'tertiary',
                              label: t`Compare to defaults`,
                              onClick: () => {
                                  const { controlId } =
                                      sharedControlPoliciesController;

                                  if (controlId) {
                                      sharedResetPoliciesModalModel.loadPolicyComparison(
                                          controlId,
                                      );
                                  }

                                  modalController.openModal({
                                      id: 'policy-compare-to-defaults-modal',
                                      size: 'lg',
                                      content: () => (
                                          <PoliciesCompareToDefaultsModal data-id="O78iVRjN" />
                                      ),
                                  });
                              },
                          },
                      } satisfies TableAction,
                  ]
                : []),
            {
                actionType: 'button',
                id: 'map-policies-button',
                typeProps: {
                    level: 'secondary',
                    label: t`Map policies`,
                    onClick: openMapPoliciesModal,
                },
            },
        ];
    }
}

export const sharedControlsPoliciesViewModel = new ControlsPoliciesViewModel();
