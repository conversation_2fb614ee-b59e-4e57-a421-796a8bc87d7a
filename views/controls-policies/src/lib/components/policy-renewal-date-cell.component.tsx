import { isNil } from 'lodash-es';
import { Feedback } from '@cosmos/components/feedback';
import { Metadata } from '@cosmos/components/metadata';
import { Text } from '@cosmos/components/text';
import type { PolicyResponseDto } from '@globals/api-sdk/types';
import { formatDate } from '@helpers/date-time';

export const PolicyRenewalDateCell = ({
    row,
}: {
    row: {
        original: {
            policy: PolicyResponseDto;
        };
    };
}): React.ReactNode => {
    const { policy } = row.original;
    let latestVersionId = 0;
    let latestVersion = null;

    if (isNil(policy.versions)) {
        return null;
    }

    for (const version of policy.versions) {
        if (version.id >= latestVersionId) {
            latestVersionId = version.id;
            latestVersion = version;
        }
    }

    if (isNil(latestVersion?.renewalDate)) {
        return <Metadata colorScheme="neutral" label="-" type="tag" />;
    }

    const currentDate = new Date();
    const renewalDate = new Date(latestVersion.renewalDate);
    const hasExpiredRenewalDate = currentDate > renewalDate;

    const futureDate = new Date();

    futureDate.setMonth(currentDate.getMonth() + 2);
    const isWithinRenewalPeriod =
        renewalDate <= futureDate && renewalDate >= currentDate;

    if (isWithinRenewalPeriod) {
        const severity = hasExpiredRenewalDate ? 'critical' : 'warning';

        return (
            <Feedback
                data-testid="policy-renewal-date"
                severity={severity}
                title={formatDate('sentence', latestVersion.renewalDate)}
            />
        );
    }

    return (
        <Text data-testid="PolicyRenewalDateCell" data-id="z42CfFuW">
            {formatDate('sentence', latestVersion.renewalDate)}
        </Text>
    );
};
