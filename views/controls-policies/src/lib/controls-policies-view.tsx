import { AppDatatable } from '@components/app-datatable';
import { sharedControlPoliciesController } from '@controllers/controls';
import { panelController } from '@controllers/panel';
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import { action, observer } from '@globals/mobx';
import { PolicyDetailsPanelView } from '@views/policy-details-panel';
import { sharedControlsPoliciesViewModel } from './models/controls-policies-view.model';

const openPolicyDetailsPanel = action((policyId: number): void => {
    sharedPolicyBuilderController.loadPolicyWithAllData(policyId);

    panelController.openPanel({
        id: 'policy-details-panel',
        content: () => <PolicyDetailsPanelView data-id="_33iIY" />,
    });
});

export const ControlsPoliciesView = observer((): React.JSX.Element => {
    const { controlPolicies, isLoading, total, loadControlPoliciesPage } =
        sharedControlPoliciesController;
    const { tableActions, emptyStateProps, tableSearchProps, columns } =
        sharedControlsPoliciesViewModel;

    return (
        <AppDatatable
            // isRowSelectionEnabled
            isLoading={isLoading}
            tableId="datatable-controls-policies-list"
            total={total}
            data={controlPolicies}
            columns={columns}
            data-id="Tl2tQxdI"
            tableActions={tableActions}
            emptyStateProps={emptyStateProps}
            tableSearchProps={tableSearchProps}
            onFetchData={loadControlPoliciesPage}
            onRowClick={({ row }) => {
                openPolicyDetailsPanel(row.policy.id);
            }}
        />
    );
});
