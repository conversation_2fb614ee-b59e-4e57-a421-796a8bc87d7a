import { LibraryTestActiveTestsCardComponent } from '@components/library-test-active-tests-card';
import { LibraryTestDetailsCardComponent } from '@components/library-test-details-card';
import { LibraryTestLogicCardComponent } from '@components/library-test-logic-card';
import { LibraryTestMappingsCardComponent } from '@components/library-test-mappings-card';
import { activeLibraryTestController } from '@controllers/library-test';
import { Box } from '@cosmos/components/box';
import { Grid } from '@cosmos/components/grid';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export const LibraryTestOverviewView = observer((): React.JSX.Element => {
    const { isLoading, isAp2Test } = activeLibraryTestController;

    if (isLoading) {
        return (
            <Stack
                direction="column"
                align="center"
                gap="6x"
                p="8x"
                data-testid="LibraryTestOverviewView-loading"
                data-id="loading-state"
            >
                <Loader
                    isSpinnerOnly
                    label={t`Loading test details...`}
                    size="lg"
                    colorScheme="primary"
                />
            </Stack>
        );
    }

    return (
        <Stack
            data-id="LGrKZl3e"
            data-testid="LibraryTestOverviewView"
            direction="column"
            gap={'md'}
            py="8x"
        >
            <Grid columns="2" gap="8x">
                <Stack
                    direction="column"
                    gap="md"
                    data-id="LibraryTestDetailsContainer"
                >
                    <Box p="md" data-id="LibraryTestDetails">
                        <LibraryTestDetailsCardComponent />
                    </Box>
                    <Box p="md" data-id="LibraryTestMappings">
                        <LibraryTestMappingsCardComponent />
                    </Box>
                </Stack>
                <Stack
                    direction="column"
                    gap="md"
                    data-id="LibraryTestActiveTestsContainer"
                >
                    <Box p="md" data-id="LibraryTestActiveTests">
                        <LibraryTestActiveTestsCardComponent />
                    </Box>
                </Stack>
            </Grid>
            <Grid>
                <Stack
                    direction="column"
                    gap="md"
                    data-id="LibraryTestLogicCardContainer"
                >
                    {isAp2Test && (
                        <Box p="md" data-id="LibraryTestLogic">
                            <LibraryTestLogicCardComponent />
                        </Box>
                    )}
                </Stack>
            </Grid>
        </Stack>
    );
});
