import { isEmpty } from 'lodash-es';
import { TextBreakLine } from '@components/text-break-line';
import {
    sharedRequirementDetailsController,
    sharedRequirementsController,
} from '@controllers/requirements';
import { Banner } from '@cosmos/components/banner';
import { Grid } from '@cosmos/components/grid';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Skeleton } from '@cosmos/components/skeleton';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { frameworksAdditionalInfo2DescriptionHeader } from '../helpers/frameworks-additional-info-2-description-header.helper';
import { frameworksAdditionalInfo3DescriptionHeader } from '../helpers/frameworks-additional-info-3-description-header.helper';
import { frameworksAdditionalInfoDescriptionHeader } from '../helpers/frameworks-additional-info-description-header.helper';
import { frameworksDescriptionHeader } from '../helpers/frameworks-description-header.helper';

export const FrameworksOverviewViewCard = observer((): React.JSX.Element => {
    const { requirement, isRequirementLoading, isRequirementFetching } =
        sharedRequirementDetailsController;
    const { isUpdatingScope } = sharedRequirementsController;
    const isLoadingOrUpdating =
        isRequirementLoading || isUpdatingScope || isRequirementFetching;

    const descriptionTitle = frameworksDescriptionHeader();
    const additionalInfoDescriptionTitle =
        frameworksAdditionalInfoDescriptionHeader();
    const additionalInfo2DescriptionTitle =
        frameworksAdditionalInfo2DescriptionHeader();
    const additionalInfo3DescriptionTitle =
        frameworksAdditionalInfo3DescriptionHeader();

    return (
        <Grid
            gap={'xl'}
            data-testid="FrameworksOverviewViewCard"
            data-id="MnpB3GLy"
        >
            {requirement?.archivedAt && (
                <Banner
                    title={t`Requirement marked out of scope`}
                    severity="primary"
                    body={requirement.rationale ?? ''}
                />
            )}
            <KeyValuePair
                label={t`Code`}
                type={isLoadingOrUpdating ? 'REACT_NODE' : 'TEXT'}
                value={isLoadingOrUpdating ? <Skeleton /> : requirement?.name}
            />
            <KeyValuePair
                label={t`Control description`}
                type={'REACT_NODE'}
                value={
                    isLoadingOrUpdating ? (
                        <Skeleton />
                    ) : (
                        <TextBreakLine text={requirement?.description ?? ''} />
                    )
                }
            />
            {!isEmpty(requirement?.longDescription) && (
                <KeyValuePair
                    label={descriptionTitle}
                    type="REACT_NODE"
                    value={
                        isLoadingOrUpdating ? (
                            <Skeleton />
                        ) : (
                            <TextBreakLine
                                text={String(requirement?.longDescription)}
                            />
                        )
                    }
                />
            )}
            {!isEmpty(requirement?.additionalInfo) && (
                <KeyValuePair
                    label={additionalInfoDescriptionTitle}
                    type="REACT_NODE"
                    value={
                        isLoadingOrUpdating ? (
                            <Skeleton />
                        ) : (
                            <TextBreakLine
                                text={String(requirement?.additionalInfo)}
                            />
                        )
                    }
                />
            )}
            {!isEmpty(requirement?.additionalInfo2) && (
                <KeyValuePair
                    label={additionalInfo2DescriptionTitle}
                    type="REACT_NODE"
                    value={
                        isLoadingOrUpdating ? (
                            <Skeleton />
                        ) : (
                            <TextBreakLine
                                text={String(requirement?.additionalInfo2)}
                            />
                        )
                    }
                />
            )}
            {!isEmpty(requirement?.additionalInfo3) && (
                <KeyValuePair
                    label={additionalInfo3DescriptionTitle}
                    type="REACT_NODE"
                    value={
                        isLoadingOrUpdating ? (
                            <Skeleton />
                        ) : (
                            <TextBreakLine
                                text={String(requirement?.additionalInfo3)}
                            />
                        )
                    }
                />
            )}
        </Grid>
    );
});
