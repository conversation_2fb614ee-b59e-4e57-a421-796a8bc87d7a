import { Text } from '@cosmos/components/text';
import type { PolicyTableVersionResponseDto } from '@globals/api-sdk/types';

export const ExplanationOfChangesCell = ({
    row: { original },
}: {
    row: { original: PolicyTableVersionResponseDto };
}): React.JSX.Element => {
    const { changesExplanation } = original;

    return (
        <Text data-testid="ExplanationOfChangesCell" data-id="xbJFFWgp">
            {changesExplanation || 'No explanation of changes was added'}
        </Text>
    );
};
