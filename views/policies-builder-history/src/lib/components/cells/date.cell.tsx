import { DateTime, type DateTimeProps } from '@cosmos-lab/components/date-time';
import type { PolicyTableVersionResponseDto } from '@globals/api-sdk/types';

export const DateCell = ({
    row: { original },
    column: { id },
}: {
    row: { original: PolicyTableVersionResponseDto };
    column: { id: string };
}): React.JSX.Element => {
    const { createdAt, approvedAt, publishedAt } = original;

    const dateMap: Record<string, DateTimeProps['date']> = {
        CREATED_AT: createdAt ?? '',
        APPROVED_AT: approvedAt ?? '',
        PUBLISHED_AT: publishedAt ?? '',
    };

    return (
        <DateTime
            date={dateMap[id]}
            format="sentence"
            data-testid="DateCell"
            data-id={`${id.toLowerCase()}-date`}
        />
    );
};
