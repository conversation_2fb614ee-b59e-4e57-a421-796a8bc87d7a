import { EmptyValue } from '@cosmos-lab/components/empty-value';
import { AvatarIdentity } from '@cosmos-lab/components/identity';
import type { PolicyTableVersionResponseDto } from '@globals/api-sdk/types';
import { getFullName, getInitials } from '@helpers/formatters';

interface UserCellProps {
    row: {
        original: PolicyTableVersionResponseDto;
    };
}

export const UserCell = ({
    row: {
        original: { owner },
    },
}: UserCellProps): React.JSX.Element => {
    if (!owner) {
        return <EmptyValue label="-" />;
    }

    // TODO: this is really bad, update PolicyTableVersionResponseDto type to return full user type instead of [string]: unknown
    const fullName = getFullName(
        owner.firstName as string,
        owner.lastName as string,
    );
    const avatar = owner.avatarUrl as string;

    return (
        <AvatarIdentity
            imgSrc={avatar}
            data-testid="UserCell"
            data-id="ZBfjyeAs"
            primaryLabel={fullName}
            fallbackText={getInitials(fullName)}
        />
    );
};
