import { isEmpty, isNil } from 'lodash-es';
import {
    PoliciesBuilderHistoryTableCellDownloadComponent,
    PoliciesBuilderHistoryTableCellVersionComponent,
} from '@components/policies-builder-history';
import type {
    DatatableProps,
    FilterProps,
    FilterViewModeProps,
    TableSearchProps,
} from '@cosmos/components/datatable';
import type { EmptyStateProps } from '@cosmos/components/empty-state';
import { policyVersionControllerGetPolicyVersionHistoryByPolicyIdOptions } from '@globals/api-sdk/queries';
import type { PolicyTableVersionResponseDto } from '@globals/api-sdk/types';
import { logger } from '@globals/logger';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';
import { COLUMN_SIZES } from '@helpers/table';
import { DateCell } from '../components/cells/date.cell';
import { ExplanationOfChangesCell } from '../components/cells/explanation-of-changes.cell';
import { UserCell } from '../components/cells/user.cell';

export type PoliciesBuilderHistoryDatatableQuery = Required<
    Parameters<
        typeof policyVersionControllerGetPolicyVersionHistoryByPolicyIdOptions
    >
>[0]['query'];

export class PoliciesBuilderHistoryDatatableController {
    policyId: number | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    policyVersionHistoryQuery = new ObservedQuery(
        policyVersionControllerGetPolicyVersionHistoryByPolicyIdOptions,
    );

    load = (query: PoliciesBuilderHistoryDatatableQuery): void => {
        if (isNil(this.policyId)) {
            logger.warn(
                'PoliciesBuilderHistoryDatatableController: load method called before policyId has been set.',
            );

            return;
        }

        this.policyVersionHistoryQuery.load({
            path: { policyId: this.policyId },
            query,
        });
    };

    setPolicyId = (id: number): void => {
        this.policyId = id;
    };

    get isLoading(): boolean {
        return this.policyVersionHistoryQuery.isLoading;
    }

    get total(): number {
        return this.policyVersionHistoryQuery.data?.total ?? 0;
    }

    get data(): PolicyTableVersionResponseDto[] {
        return this.policyVersionHistoryQuery.data?.data ?? [];
    }

    get hasError(): boolean {
        return this.policyVersionHistoryQuery.hasError;
    }

    get columns(): DatatableProps<PolicyTableVersionResponseDto>['columns'] {
        return [
            {
                id: 'DOWNLOAD',
                header: '',
                enableSorting: false,
                isActionColumn: true,
                minSize: 40,
                cell: PoliciesBuilderHistoryTableCellDownloadComponent,
            },
            {
                id: 'ID',
                header: 'Version',
                enableSorting: true,
                accessorKey: 'formatted',
                cell: PoliciesBuilderHistoryTableCellVersionComponent,
                minSize: COLUMN_SIZES.MEDIUM,
            },
            {
                id: 'EXPLANATION_OF_CHANGES',
                header: 'Explanation of changes',
                enableSorting: false,
                accessorKey: 'changesExplanation',
                isActionColumn: true,
                cell: ExplanationOfChangesCell,
                minSize: 400,
            },
            {
                id: 'OWNER',
                header: 'Owner',
                enableSorting: false,
                accessorKey: 'owner.firstName',
                cell: UserCell,
                minSize: COLUMN_SIZES.LARGE,
            },
            {
                id: 'APPROVER',
                header: 'Approver',
                enableSorting: false,
                accessorKey: 'approver.firstName',
                cell: UserCell,
                minSize: COLUMN_SIZES.LARGE,
            },
            {
                id: 'CREATED_AT',
                header: 'Created on',
                enableSorting: true,
                accessorKey: 'createdAt',
                cell: DateCell,
                minSize: COLUMN_SIZES.LARGE,
            },
            {
                id: 'APPROVED_AT',
                header: 'Approved on',
                enableSorting: true,
                accessorKey: 'approvedAt',
                cell: DateCell,
                minSize: COLUMN_SIZES.LARGE,
            },
            {
                id: 'PUBLISHED_AT',
                header: 'Published on',
                enableSorting: true,
                cell: DateCell,
                minSize: COLUMN_SIZES.LARGE,
            },
        ];
    }

    get filterProps(): FilterProps | undefined {
        return undefined;
    }

    get emptyStateProps(): EmptyStateProps {
        // If we have an error, show error state
        if (this.hasError) {
            return {
                title: 'Unable to load policy versions',
                description:
                    'There was an error loading the policy version history. Please try again.',
            };
        }

        // If this is the first time loading and no data, show first visit state
        // Check if query has never been called (query is null)
        if (
            this.policyVersionHistoryQuery.query === null &&
            isEmpty(this.data)
        ) {
            return {
                title: 'No policy versions yet',
                description:
                    "This policy doesn't have any versions yet. Versions will appear here once the policy is published.",
            };
        }

        // If we have filters/search applied and no results, show filtered state
        // TODO: Add logic to detect if filters are applied
        // For now, show generic no results state
        return {
            title: 'No policy versions found',
            description:
                'There are no policy versions that match the current criteria.',
        };
    }

    get tableSearchProps(): TableSearchProps {
        return {
            hideSearch: true,
        };
    }

    get tableId(): string {
        return 'datatable-policies-history';
    }

    get filterViewModeProps(): FilterViewModeProps {
        return {
            viewMode: 'toggleable',
            props: {
                initialSelectedOption: 'pinned',
                togglePinnedLabel: 'Pin filters to page',
                toggleUnpinnedLabel: 'Move filters to dropdown',
                selectedOption: 'pinned',
            },
        };
    }
}

export const policiesBuilderHistoryDatatableController =
    new PoliciesBuilderHistoryDatatableController();
