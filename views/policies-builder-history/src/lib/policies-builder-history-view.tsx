import { isNil } from 'lodash-es';
import { AppDatatable } from '@components/app-datatable';
import { PoliciesBuilderEmptyStateComponent } from '@components/policies-builder-empty-state';
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import { Card } from '@cosmos/components/card';
import { Loader } from '@cosmos/components/loader';
import type { PolicyTableVersionResponseDto } from '@globals/api-sdk/types';
import { observer } from '@globals/mobx';
import { policiesBuilderHistoryDatatableController } from './controllers';
import type { PoliciesBuilderHistoryDatatableQuery } from './controllers/policies-builder-history-datatable.controller';

export const PoliciesBuilderHistoryView = observer((): React.JSX.Element => {
    if (sharedPolicyBuilderController.isInitialLoading) {
        return (
            <Card
                size="lg"
                title="Details"
                data-testid="PoliciesBuilderHistoryDetailsComponent"
                data-id="keol8JI4"
                body={<Loader isSpinnerOnly label={'Loading...'} />}
            />
        );
    }

    if (isNil(sharedPolicyBuilderController.policy?.latestPolicyVersion)) {
        return <PoliciesBuilderEmptyStateComponent />;
    }

    return (
        <AppDatatable<
            PolicyTableVersionResponseDto,
            PoliciesBuilderHistoryDatatableQuery
        >
            controller={policiesBuilderHistoryDatatableController}
            data-testid="PoliciesBuilderHistoryView"
            data-id="8NcGvlR0"
        />
    );
});
