import { isEmpty } from 'lodash-es';
import { Avatar } from '@cosmos/components/avatar';
import { Box } from '@cosmos/components/box';
import { Feedback } from '@cosmos/components/feedback';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { AvatarIdentity } from '@cosmos-lab/components/identity';
import type {
    AccessApplicationSummaryResponseDto,
    AccessReviewApplicationResponseDto,
    AccessReviewPeriodApplicationResponseDto,
    UserResponseDto,
} from '@globals/api-sdk/types';
import { observer } from '@globals/mobx';
import { getFullName } from '@helpers/formatters';
import { getAvatarFallbackText } from '@helpers/get-avatar-fallback-text';

/**
 * Type for reviewer that might have userData nested property.
 */
type ReviewerWithUserData = UserResponseDto & {
    userData?: {
        firstName?: string;
        lastName?: string;
        avatarUrl?: string | null;
        email?: string;
    };
};

/**
 * Safely extracts user values with fallback to userData when primary values are empty.
 * Handles both UserResponseDto and objects that might have userData nested property.
 */
const getUserValues = (reviewer: ReviewerWithUserData) => {
    const firstName = reviewer.firstName || reviewer.userData?.firstName || '';
    const lastName = reviewer.lastName || reviewer.userData?.lastName || '';
    const avatarUrl =
        reviewer.avatarUrl || reviewer.userData?.avatarUrl || undefined;
    const email = reviewer.email || reviewer.userData?.email || '';

    return {
        firstName,
        lastName,
        avatarUrl,
        email,
        fullName: getFullName(firstName, lastName),
    };
};

const MAX_VISIBLE_REVIEWERS = 3;

type ApplicationWithReviewers = AccessApplicationSummaryResponseDto & {
    reviewers: UserResponseDto[];
    needsReviewer: boolean;
};

const Reviewer = observer(
    ({
        reviewer,
        totalReviewers,
    }: {
        reviewer: UserResponseDto;
        totalReviewers: number;
    }): React.JSX.Element => {
        const { fullName, avatarUrl } = getUserValues(reviewer);

        if (totalReviewers > 1) {
            return (
                <Avatar
                    size="sm"
                    key={reviewer.id}
                    imgSrc={avatarUrl}
                    fallbackText={getAvatarFallbackText(fullName)}
                />
            );
        }

        return (
            <AvatarIdentity
                key={reviewer.id}
                primaryLabel={fullName}
                imgSrc={avatarUrl}
                data-id="reviewer-identity"
                fallbackText={getAvatarFallbackText(fullName)}
                data-testid="Reviewer"
            />
        );
    },
);

const OverflowReviewers = ({
    reviewersCount,
}: {
    reviewersCount: number;
}): React.JSX.Element => {
    const overflowCount = reviewersCount - MAX_VISIBLE_REVIEWERS;

    return (
        <Box ml="xs" data-testid="OverflowReviewers" data-id="arZ-mqa0">
            <Text size="100" colorScheme="neutral">
                +{overflowCount}
            </Text>
        </Box>
    );
};

export const ReviewersCell = observer(
    ({
        row: { original },
    }: {
        row: {
            original:
                | AccessReviewApplicationResponseDto
                | ApplicationWithReviewers
                | AccessReviewPeriodApplicationResponseDto;
        };
    }): React.JSX.Element => {
        const { reviewers } = original;
        const showOverflowReviewers = reviewers.length > MAX_VISIBLE_REVIEWERS;
        const visibleReviewers = reviewers.slice(0, MAX_VISIBLE_REVIEWERS);

        if (isEmpty(reviewers)) {
            return (
                <Feedback
                    title="Needs reviewer"
                    severity="critical"
                    data-id="needs-reviewer-feedback"
                />
            );
        }

        if (!isEmpty(reviewers)) {
            return (
                <Stack direction="row" gap="xs" align="center">
                    {visibleReviewers.map((reviewer) => {
                        return (
                            <Reviewer
                                key={reviewer.id}
                                reviewer={reviewer}
                                totalReviewers={reviewers.length}
                                data-id="NCIZnjm_"
                            />
                        );
                    })}
                    {showOverflowReviewers && (
                        <OverflowReviewers reviewersCount={reviewers.length} />
                    )}
                </Stack>
            );
        }

        return (
            <Text data-id="onAMc8fz" data-testid="ReviewersCell">
                No reviewers
            </Text>
        );
    },
);
