import { Text } from '@cosmos/components/text';
import type {
    AccessApplicationSummaryResponseDto,
    AccessReviewApplicationResponseDto,
    AccessReviewPeriodApplicationResponseDto,
    UserResponseDto,
} from '@globals/api-sdk/types';
import { observer } from '@globals/mobx';
import { getApplicationTypeLabel } from '../../../helpers';

type ApplicationWithReviewers = AccessApplicationSummaryResponseDto & {
    reviewers: UserResponseDto[];
    needsReviewer: boolean;
};

export const ApplicationTypeCell = observer(
    ({
        row: { original },
    }: {
        row: {
            original:
                | AccessReviewApplicationResponseDto
                | ApplicationWithReviewers
                | AccessReviewPeriodApplicationResponseDto;
        };
    }): React.JSX.Element => {
        return (
            <Text data-testid="ApplicationTypeCell" data-id="CT8uZm6q">
                {getApplicationTypeLabel(original.source)}
            </Text>
        );
    },
);
