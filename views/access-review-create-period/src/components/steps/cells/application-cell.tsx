import { OrganizationIdentity } from '@cosmos-lab/components/identity';
import type {
    AccessApplicationSummaryResponseDto,
    AccessReviewApplicationResponseDto,
    AccessReviewPeriodApplicationResponseDto,
    UserResponseDto,
} from '@globals/api-sdk/types';
import { observer } from '@globals/mobx';
import { providers } from '@globals/providers';
import { getInitials } from '@helpers/formatters';

type ApplicationWithReviewers = AccessApplicationSummaryResponseDto & {
    reviewers: UserResponseDto[];
    needsReviewer: boolean;
};

export const ApplicationCell = observer(
    ({
        row: { original },
    }: {
        row: {
            original:
                | AccessReviewApplicationResponseDto
                | ApplicationWithReviewers
                | AccessReviewPeriodApplicationResponseDto;
        };
    }): React.JSX.Element => {
        // Get provider logo based on clientType for direct connections
        const providerInfo =
            original.clientType && original.clientType in providers
                ? providers[original.clientType as keyof typeof providers]
                : null;

        let imgSrc = original.logo || undefined;

        if (
            !imgSrc &&
            (original.source === 'DIRECT_CONNECTION' ||
                original.source === 'PARTNER_CONNECTION')
        ) {
            imgSrc = providerInfo?.logo;
        }
        if (!imgSrc && original.source === 'MANUALLY_ADDED') {
            imgSrc = providers.CUSTOM.logo;
        }

        return (
            <OrganizationIdentity
                data-testid="ApplicationCell"
                primaryLabel={providerInfo?.name ?? original.name}
                fallbackText={getInitials(providerInfo?.name ?? original.name)}
                imgSrc={imgSrc || undefined}
                data-id="application-identity"
            />
        );
    },
);
