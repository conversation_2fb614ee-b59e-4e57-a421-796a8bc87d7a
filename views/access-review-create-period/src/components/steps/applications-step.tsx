import { forwardRef, useEffect, useMemo } from 'react';
import { z } from 'zod';
import {
    sharedAccessReviewApplicationsController,
    sharedCreateReviewPeriodController,
} from '@controllers/access-reviews';
import { Avatar } from '@cosmos/components/avatar';
import { Button } from '@cosmos/components/button';
import { Combobox } from '@cosmos/components/combobox';
import { FormField } from '@cosmos/components/form-field';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type { AccessReviewApplicationResponseDto } from '@globals/api-sdk/types';
import { Trans, useLingui } from '@globals/i18n/macro';
import { action, observer, toJS } from '@globals/mobx';
import { getInitials } from '@helpers/formatters';
import { type CustomFieldRenderProps, Form, type FormSchema } from '@ui/forms';
import { openAccessReviewAddApplicationModal } from '@views/access-review-applications';
import { getApplicationLogo, getApplicationTypeLabel } from '../../helpers';

interface ResponseDto extends ListBoxItemData {
    label: string;
    reviewers: AccessReviewApplicationResponseDto['reviewers'];
    source: AccessReviewApplicationResponseDto['source'];
    websiteUrl: string;
    accessApplicationId: number;
}

const AppsCombobox = observer(
    ({
        formId,
        name,
        'data-id': dataId,
        onChange,
        feedback,
        required,
        optionalText,
    }: CustomFieldRenderProps): React.JSX.Element => {
        const { t } = useLingui();
        const {
            accessReviewApplicationsList: applications,
            isLoading,
            hasNextPage,
            handleFetchOptions,
            accessReviewApplicationsQuery,
        } = sharedAccessReviewApplicationsController;
        const {
            setApplicationsData,
            selectedApplications,
            _resetCombobox,
            resetCombobox,
        } = sharedCreateReviewPeriodController;

        const handleAddNewApplication = () => {
            openAccessReviewAddApplicationModal(
                (app: AccessReviewApplicationResponseDto) => {
                    const applicationToAdd: ResponseDto = {
                        id: app.id.toString(),
                        label: app.name,
                        value: app.id.toString(),
                        source: app.source,
                        websiteUrl: app.websiteUrl,
                        description: getApplicationTypeLabel(app.source),
                        reviewers: app.reviewers,
                        accessApplicationId: app.accessApplicationId,
                        startSlot: (
                            <Avatar
                                size="sm"
                                imgSrc={getApplicationLogo(app)}
                                fallbackText={getInitials(app.name)}
                            />
                        ),
                    };

                    // Convert to AccessReviewApplicationResponseDto format
                    const appToAdd: AccessReviewApplicationResponseDto = {
                        id: Number(applicationToAdd.id),
                        name: applicationToAdd.label,
                        reviewers: applicationToAdd.reviewers,
                        source: applicationToAdd.source,
                        websiteUrl: applicationToAdd.websiteUrl,
                        accessApplicationId:
                            applicationToAdd.accessApplicationId,
                        logo: getApplicationLogo(app) || null,
                        clientType: app.clientType,
                        createdAt: app.createdAt,
                        updatedAt: app.updatedAt,
                        deletedAt: app.deletedAt,
                        externalId: app.externalId,
                        summary: app.summary,
                        warnings: app.warnings,
                    };

                    setApplicationsData([...selectedApplications, appToAdd]);
                    onChange([...selectedApplications, applicationToAdd]);
                    accessReviewApplicationsQuery.invalidate();
                    resetCombobox();
                },
            );
        };

        const initialSelectedApplications = useMemo(() => {
            const selectedApps = toJS(selectedApplications);

            // Create a Map to deduplicate selected applications by ID
            const uniqueApps = new Map<
                number,
                AccessReviewApplicationResponseDto
            >();

            selectedApps.forEach((app) => {
                uniqueApps.set(app.id, app);
            });

            return [...uniqueApps.values()].map(
                (app): ListBoxItemData => ({
                    id: app.id.toString(),
                    label: app.name,
                    name: app.name,
                    value: app.id.toString(),
                    source: app.source,
                    websiteUrl: app.websiteUrl,
                    description: getApplicationTypeLabel(app.source),
                    reviewers: app.reviewers,
                    logo: getApplicationLogo(app),
                    startSlot: (
                        <Avatar
                            size="sm"
                            imgSrc={getApplicationLogo(app)}
                            fallbackText={getInitials(app.name)}
                        />
                    ),
                }),
            );
        }, [selectedApplications]);

        const applicationsAsListBoxItems = useMemo(() => {
            // Create a Map to deduplicate applications by ID
            const uniqueApps = new Map<
                number,
                AccessReviewApplicationResponseDto
            >();

            applications.forEach((app) => {
                uniqueApps.set(app.id, app);
            });

            return [...uniqueApps.values()].map(
                (app): ListBoxItemData => ({
                    id: app.id.toString(), // Only id needed, not key
                    label: app.name,
                    name: app.name,
                    value: app.id.toString(),
                    source: app.source,
                    websiteUrl: app.websiteUrl,
                    description: getApplicationTypeLabel(app.source),
                    reviewers: app.reviewers,
                    logo: getApplicationLogo(app),
                    startSlot: (
                        <Avatar
                            size="sm"
                            imgSrc={getApplicationLogo(app)}
                            fallbackText={getInitials(app.name)}
                        />
                    ),
                }),
            );
        }, [applications]);

        const handleOnChange = action((values: ListBoxItemData[]) => {
            const selectedApps = values.map((app) => {
                return {
                    id: Number(app.id),
                    name: app.label,
                    reviewers: app.reviewers,
                    source: app.source,
                    websiteUrl: app.websiteUrl,
                    accessApplicationId: app.accessApplicationId,
                };
            });

            setApplicationsData(
                selectedApps as AccessReviewApplicationResponseDto[],
            );
            onChange(values);
        });

        useEffect(() => {
            onChange([...initialSelectedApplications]);
        }, [_resetCombobox, onChange, initialSelectedApplications]);

        return (
            <Stack
                direction="column"
                gap="2xl"
                data-id="add-new-application-button-container"
            >
                <FormField
                    data-id={dataId}
                    formId={formId}
                    name={name}
                    label="Applications"
                    layout="stack"
                    feedback={feedback}
                    required={required}
                    optionalText={optionalText}
                    renderInput={({
                        describeIds,
                        inputId,
                        inputTestId,
                        labelId,
                        feedbackType,
                    }) => (
                        <Combobox
                            isMultiSelect
                            key={_resetCombobox}
                            aria-describedby={describeIds}
                            aria-labelledby={labelId}
                            data-id={inputTestId}
                            id={inputId}
                            name={name}
                            placeholderText={t`Search by application`}
                            hasMore={hasNextPage}
                            isLoading={isLoading}
                            options={applicationsAsListBoxItems}
                            clearSelectedItemButtonLabel={t`Clear`}
                            loaderLabel={t`Loading applications...`}
                            feedbackType={feedbackType}
                            defaultSelectedOptions={initialSelectedApplications}
                            onFetchOptions={handleFetchOptions}
                            onChange={handleOnChange}
                        />
                    )}
                />
                <Stack gap="sm" direction="column">
                    <Text size="100" colorScheme="neutral" type="body">
                        <Trans>
                            You can manually add a new application to your
                            library if it is missing above. It will
                            automatically be added to this review period.
                        </Trans>
                    </Text>
                    <Stack direction="row" justify="start">
                        <Button
                            label={t`Add new application`}
                            level="secondary"
                            size="sm"
                            data-id="add-new-application-button"
                            onClick={handleAddNewApplication}
                        />
                    </Stack>
                </Stack>
            </Stack>
        );
    },
);

const ApplicationsStepComponent = forwardRef<
    HTMLFormElement,
    {
        formId: string;
        onSubmit: () => void;
    }
>(({ formId, onSubmit }, formRef): React.JSX.Element => {
    const { t } = useLingui();

    const handleSubmit = () => {
        onSubmit();
    };

    const formSchema = useMemo(
        (): FormSchema => ({
            selectedApplications: {
                type: 'custom',
                label: t`Applications`,
                initialValue: [],
                render: (renderProps: CustomFieldRenderProps) => (
                    <AppsCombobox
                        {...renderProps}
                        data-id="applications-combobox"
                    />
                ),
                validator: z
                    .array(
                        z.object({
                            id: z.string(),
                            label: z.string(),
                            value: z.string(),
                        }),
                    )
                    .min(
                        1,
                        t`You must select the applications you want to include in your next user access review period to save this setup.`,
                    ),
                isOptional: false,
            },
        }),
        [t],
    );

    return (
        <Stack
            gap="xl"
            direction="column"
            data-testid="ApplicationsStep"
            data-id="access-review-applications-step"
            style={{ maxWidth: '600px' }}
        >
            <Stack gap="lg" direction="column">
                <Text type="title">
                    <Trans>Select applications to review</Trans>
                </Text>
            </Stack>

            <Stack gap="2x" direction="column">
                <Form
                    hasExternalSubmitButton
                    formId={formId}
                    data-id={formId}
                    ref={formRef}
                    schema={formSchema}
                    onSubmit={handleSubmit}
                />
            </Stack>
        </Stack>
    );
});

ApplicationsStepComponent.displayName = 'ApplicationsStep';

export const ApplicationsStep = observer(ApplicationsStepComponent);
