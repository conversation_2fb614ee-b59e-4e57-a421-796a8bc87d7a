import { sharedCreateReviewPeriodController } from '@controllers/access-reviews';
import { Stack } from '@cosmos/components/stack';
import { Wizard } from '@cosmos-lab/components/wizard';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import { useFormSubmit } from '@ui/forms';
import { ApplicationsStep } from './components/steps/applications-step';
import { PeriodsStep } from './components/steps/periods-step';
import { ReviewersStep } from './components/steps/reviewers-step';

export const AccessReviewCreatePeriodView = observer((): React.JSX.Element => {
    const navigate = useNavigate();
    const { formRef: formDetailsRef, triggerSubmit: triggerSubmitDetails } =
        useFormSubmit();
    const {
        formRef: formApplicationsRef,
        triggerSubmit: triggerSubmitApplications,
    } = useFormSubmit();

    const { currentWorkspaceId } = sharedWorkspacesController;
    const { createReviewPeriod } = sharedCreateReviewPeriodController;

    const handleComplete = async () => {
        const success = await createReviewPeriod().catch((error) => {
            console.error('Error creating review period:', error);
        });

        if (!success) {
            return;
        }

        navigate(
            `/workspaces/${currentWorkspaceId}/governance/access-review/active`,
        );
    };

    const handleCancel = (): void => {
        if (!currentWorkspaceId) {
            return;
        }
        navigate(
            `/workspaces/${currentWorkspaceId}/governance/access-review/applications`,
        );
    };

    const PeriodsStepWizard = () => {
        return (
            <PeriodsStep
                formRef={formDetailsRef}
                formId="periods-step-form"
                data-testid="PeriodsStepWizard"
                data-id="rFQRzEra"
                onSubmit={triggerSubmitDetails}
            />
        );
    };

    const ApplicationsStepWizard = () => {
        return (
            <ApplicationsStep
                ref={formApplicationsRef}
                formId="applications-step-form"
                data-testid="ApplicationsStepWizard"
                data-id="access-review-applications-step"
                onSubmit={triggerSubmitApplications}
            />
        );
    };

    const ReviewersStepWizard = () => {
        return (
            <ReviewersStep
                data-testid="ReviewersStepWizard"
                data-id="yCP3J-Tq"
            />
        );
    };

    return (
        <Stack direction="column" align="center" data-id="x9imC7OV">
            <Wizard
                nextButtonLabel={t`Continue`}
                data-id="access-review-create-period-wizard"
                data-testid="AccessReviewCreatePeriodWizardView"
                completeButtonLabel={t`Complete set up`}
                steps={[
                    {
                        component: PeriodsStepWizard,
                        stepTitle: t`Period Details`,
                        stepSubtitle: t`Set the review period dates`,
                        isStepSkippable: false,
                        onStepChange: triggerSubmitDetails,
                    },
                    {
                        component: ApplicationsStepWizard,
                        stepTitle: t`Applications`,
                        stepSubtitle: t`Select applications to review`,
                        isStepSkippable: false,
                        onStepChange: triggerSubmitApplications,
                    },
                    {
                        component: ReviewersStepWizard,
                        stepTitle: t`Review`,
                        stepSubtitle: t`Assign reviewers and confirm`,
                        isStepSkippable: false,
                    },
                ]}
                onCancel={handleCancel}
                onComplete={handleComplete}
            />
        </Stack>
    );
});
