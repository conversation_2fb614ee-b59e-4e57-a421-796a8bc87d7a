import { sharedRiskMitigationControlsController } from '@controllers/risk-details';
import type { ExtendedDataTableColumnDef } from '@cosmos/components/datatable';
import type { RiskControlResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { ActionCell } from '../components/cells/action-cell-component';
import { ControlCodeCell } from '../components/cells/control-code-cell-component';

export const getRiskRegisterMitigatingControlsColumns =
    (): ExtendedDataTableColumnDef<RiskControlResponseDto>[] => {
        const { canUnmapControls } = sharedRiskMitigationControlsController;

        const baseColumns: ExtendedDataTableColumnDef<RiskControlResponseDto>[] =
            [
                {
                    id: 'code',
                    accessorKey: 'id',
                    header: t`Control code`,
                    enableSorting: true,
                    cell: ControlCodeCell,
                    size: 140,
                    maxSize: 180,
                },
                {
                    id: 'name',
                    accessorKey: 'name',
                    header: t`Name`,
                    enableSorting: true,
                    size: 400,
                    maxSize: 450,
                },
                {
                    id: 'description',
                    accessorKey: 'description',
                    header: t`Description`,
                    enableSorting: false,
                },
            ];

        // Only show action column if user can unmap controls
        if (canUnmapControls) {
            const actionColumn: ExtendedDataTableColumnDef<RiskControlResponseDto> =
                {
                    id: 'actions',
                    accessorKey: 'id',
                    header: '',
                    cell: ActionCell,
                    enableSorting: false,
                    isActionColumn: true,
                    size: 60,
                    maxSize: 80,
                    meta: {
                        shouldIgnoreRowClick: true,
                    },
                };

            baseColumns.unshift(actionColumn);
        }

        return baseColumns;
    };
