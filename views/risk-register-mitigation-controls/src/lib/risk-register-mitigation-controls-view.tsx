import { Box } from '@cosmos/components/box';
import { observer } from '@globals/mobx';
import { RiskMitigationControlsTableComponent } from '../components/risk-mitigation-controls-table-component';

export const RiskRegisterMitigationControlsView = observer(
    (): React.JSX.Element => {
        return (
            <Box
                p="2xl"
                data-testid="RiskRegisterMitigationControlsView"
                data-id="2o6TiaAy"
            >
                <RiskMitigationControlsTableComponent />
            </Box>
        );
    },
);
