import { noop } from 'lodash-es';
import type { ObjectItem } from '@components/object-selector';
import { openPolicyWithSLASelector } from '@components/policies';
import { Button } from '@cosmos/components/button';
import { Feedback } from '@cosmos/components/feedback';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type { ActivePoliciesDataWithSlaResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { CustomFieldRenderProps } from '@ui/forms';

/**
 * Custom field component that replaces the policies dropdown with a button
 * that opens the global policy selector modal.
 */
export const ReplacePoliciesButtonField = (
    props: CustomFieldRenderProps,
): React.JSX.Element => {
    const { value = [], setValue, onBlur, error, 'data-id': dataId } = props;
    const typedValue = value as { id: number; name: string }[];
    const selectedPoliciesCount = Array.isArray(typedValue)
        ? typedValue.length
        : 0;
    const hasSelectedPolicies = selectedPoliciesCount > 0;

    const handleSubmit = action(
        (
            selectedItems:
                | ObjectItem<ActivePoliciesDataWithSlaResponseDto>[]
                | ObjectItem<ActivePoliciesDataWithSlaResponseDto>,
        ) => {
            const items = Array.isArray(selectedItems)
                ? selectedItems
                : [selectedItems];

            const policyData = items.map((item) => ({
                id: item.objectData.id,
                name: item.label,
            }));

            (
                setValue as unknown as (
                    value: { id: number; name: string }[],
                ) => void
            )(policyData);
        },
    );

    const handleOpenModal = action(() => {
        const defaultSelectedItems: ObjectItem<ActivePoliciesDataWithSlaResponseDto>[] =
            Array.isArray(typedValue)
                ? typedValue.map((policy) => ({
                      id: String(policy.id),
                      value: String(policy.id),
                      label: policy.name,
                      description: '',
                      objectType: 'POLICY' as const,
                      objectData: {
                          id: policy.id,
                      } as ActivePoliciesDataWithSlaResponseDto,
                      avatar: {
                          fallbackText: 'P',
                          imgAlt: t`Policy`,
                      },
                  }))
                : [];

        openPolicyWithSLASelector({
            config: {
                selectionMode: 'multi',
                modal: {
                    id: 'replace-policies',
                    title: t`Select Policy`,
                    size: 'lg',
                    confirmButtonLabel: t`Select`,
                    cancelButtonLabel: t`Cancel`,
                    showSelectedCount: true,
                    disableClickOutsideToClose: true,
                },
                search: {
                    placeholder: t`Search by policy name...`,
                    label: t`Search policies`,
                    loaderLabel: t`Loading policies...`,
                    emptyStateMessage: t`No policies found matching your search criteria.`,
                    clearAllLabel: t`Clear all`,
                },
                filters: {
                    excludeIds: [],
                },
                defaultSelectedItems,
            },
            callbacks: {
                onSelected: handleSubmit,
                onCancel: noop,
            },
        });
    });

    const getButtonLabel = () => {
        if (hasSelectedPolicies) {
            return t`Edit selected policies`;
        }

        return t`Select policies to replace`;
    };

    const getSelectedPoliciesText = () => {
        if (selectedPoliciesCount === 1) {
            return t`1 policy selected`;
        }

        const policiesText = t`policies selected`;

        return `${selectedPoliciesCount} ${policiesText}`;
    };

    const handleResetSelections = action(() => {
        (
            setValue as unknown as (
                value: { id: number; name: string }[],
            ) => void
        )([]);
    });

    return (
        <Stack
            direction="column"
            gap="sm"
            data-testid="ReplacePoliciesButtonField"
            data-id={dataId}
        >
            <Button
                label={getButtonLabel()}
                level="secondary"
                size="md"
                data-id={`${dataId}-button`}
                onClick={handleOpenModal}
                onBlur={onBlur}
            />

            {hasSelectedPolicies && (
                <Stack direction="row" gap="md" align="center">
                    <Text
                        size="200"
                        colorScheme="neutral"
                        data-id={`${dataId}-selected-count`}
                    >
                        {getSelectedPoliciesText()}
                    </Text>
                    <Button
                        data-id={`${dataId}-reset-button`}
                        label={t`Reset selections`}
                        level="tertiary"
                        size="sm"
                        onClick={handleResetSelections}
                    />
                </Stack>
            )}

            {error?.message && (
                <Feedback
                    title={error.message}
                    severity="critical"
                    data-id={`${dataId}-error`}
                />
            )}
        </Stack>
    );
};
