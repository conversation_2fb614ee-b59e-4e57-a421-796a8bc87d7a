/**
 * Form data types for the policy creation wizard steps.
 */

export interface PolicySourceFormData {
    sourceType: 'BUILDER' | 'UPLOADED' | 'EXTERNAL';
    uploadedFile?: File;
    externalFileId?: string;
}

export interface DetailsFormData {
    name: string;
    description: string;
    renewalDate: {
        renewalFrequency: {
            id: string;
            label: string;
            value: string;
        };
        renewalDate: string;
    };
    owner: { id: string; label: string; value: string };
}

export interface PersonnelGroupsFormData {
    assignedTo: 'ALL' | 'NONE' | 'GROUP';
    selectedGroups?: { id: string; label: string; value: string }[];
    notifyNewMembers?: boolean;
}

export interface ReplacePoliciesFormData {
    shouldReplacePolicies?: 'yes' | 'no';
    policiesToReplace?: { id: number; name: string }[];
}

export interface ExternalSourceFormData {
    providerType: string;
    externalFileId?: string;
}
