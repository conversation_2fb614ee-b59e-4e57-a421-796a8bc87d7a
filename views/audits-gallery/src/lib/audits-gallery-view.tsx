import { AppDatatable } from '@components/app-datatable';
import { AuditGalleryCard } from '@components/audits';
import { sharedAuditsControllers } from '@controllers/audits';
import { observer } from '@globals/mobx';
import { COLUMNS } from '../constants/columns.constant';
import { AUDITS_FILTERS } from '../constants/filters.constant';

const TABLE_ID = 'audits-active-audits-table';

export const AuditsGalleryView = observer((): React.JSX.Element => {
    const { audits, total, isLoading, loadPage } = sharedAuditsControllers;

    return (
        <AppDatatable
            isFullPageTable
            viewMode="gallery"
            columns={COLUMNS}
            data={audits}
            isLoading={isLoading}
            tableId={TABLE_ID}
            total={total}
            filterProps={AUDITS_FILTERS}
            data-id="sG4bvLmw"
            galleryCard={({ row }) => (
                <AuditGalleryCard row={row} data-id="TnWKF3sX" />
            )}
            filterViewModeProps={{
                props: {
                    initialSelectedOption: 'unpinned',
                    togglePinnedLabel: 'Pin filters to page',
                    toggleUnpinnedLabel: 'Move filters to dropdown',
                    selectedOption: 'unpinned',
                },
                viewMode: 'toggleable',
            }}
            onFetchData={loadPage}
        />
    );
});
