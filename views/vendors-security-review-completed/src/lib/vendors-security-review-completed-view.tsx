import { useEffect } from 'react';
import { sharedVendorsSecurityReviewDetailsController } from '@controllers/vendors';
import { Card } from '@cosmos/components/card';
import { Grid } from '@cosmos/components/grid';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate, useParams } from '@remix-run/react';
import { VendorsSecurityReviewsCompletedCalloutCard } from './components/vendors-security-review-completed-callout-card';
import { VendorsSecurityReviewsCompletedDecisionCard } from './components/vendors-security-review-completed-decision-card';
import { VendorsSecurityReviewsCompletedObservationsCard } from './components/vendors-security-review-completed-observations-card';
import { VendorsSecurityReviewsCompletedScopeCard } from './components/vendors-security-review-completed-scope-card';

export const VendorsSecurityReviewCompletedView = observer(
    (): React.JSX.Element => {
        const { workspaceId, vendorId, securityReviewId } = useParams();
        const navigate = useNavigate();

        const { securityReviewDetails, isLoading: isSecurityReviewLoading } =
            sharedVendorsSecurityReviewDetailsController;

        const { isVendorsDomainReadEnabled } = sharedFeatureAccessModel;
        const { currentWorkspace } = sharedWorkspacesController;

        useEffect(() => {
            if (!isVendorsDomainReadEnabled && currentWorkspace?.id) {
                navigate(`/workspaces/${currentWorkspace.id}/vendors/current`);
            }
        }, [isVendorsDomainReadEnabled, currentWorkspace?.id, navigate]);

        useEffect(() => {
            // If the security review is NOT completed, redirect to the security review page
            if (
                securityReviewDetails?.status !== 'COMPLETED' &&
                !isSecurityReviewLoading
            ) {
                navigate(
                    `/workspaces/${workspaceId}/vendors/current/${vendorId}/security-reviews/${securityReviewId}`,
                );
            }
        }, [
            securityReviewDetails?.status,
            navigate,
            workspaceId,
            vendorId,
            securityReviewId,
            isSecurityReviewLoading,
        ]);

        if (
            !isVendorsDomainReadEnabled ||
            isSecurityReviewLoading ||
            securityReviewDetails?.status !== 'COMPLETED'
        ) {
            return <Loader isSpinnerOnly label={t`Loading...`} />;
        }

        return (
            <>
                <VendorsSecurityReviewsCompletedCalloutCard />

                <Grid columns="2" gap="xl" pb="4xl">
                    <Stack direction="column">
                        <Card
                            title={t`Observations`}
                            body={
                                <VendorsSecurityReviewsCompletedObservationsCard />
                            }
                        />
                    </Stack>

                    <Stack direction="column" gap="xl">
                        <Card
                            title={t`Decision`}
                            body={
                                <VendorsSecurityReviewsCompletedDecisionCard />
                            }
                        />

                        <Card
                            title={t`Review scope`}
                            body={<VendorsSecurityReviewsCompletedScopeCard />}
                        />
                    </Stack>
                </Grid>
            </>
        );
    },
);
