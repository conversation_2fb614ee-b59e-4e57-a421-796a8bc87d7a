import { useEffect } from 'react';
import { VendorSecurityReviewsAISummaryComponent } from '@components/vendors-security-reviews';
import { sharedVendorsSecurityReviewDocumentsController } from '@controllers/vendors';
import { Box } from '@cosmos/components/box';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { PdfViewer } from '@cosmos-lab/components/pdf-viewer';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import { ContentAside } from '@ui/content-aside';
import { VendorsSecurityReviewsCompletedCalloutCard } from './components/vendors-security-review-completed-callout-card';
import { VendorsSecuritySOCCompletedReportReview } from './components/vendors-security-review-soc-completed-report-review';

export const VendorsSecurityReviewSOCCompletedView = observer(
    (): React.JSX.Element => {
        const { pdfDownloadUrl } =
            sharedVendorsSecurityReviewDocumentsController;

        const { isVendorsDomainReadEnabled } = sharedFeatureAccessModel;
        const { currentWorkspace } = sharedWorkspacesController;
        const navigate = useNavigate();

        useEffect(() => {
            if (!isVendorsDomainReadEnabled && currentWorkspace?.id) {
                navigate(`/workspaces/${currentWorkspace.id}/vendors/current`);
            }
        }, [isVendorsDomainReadEnabled, currentWorkspace?.id, navigate]);

        if (!isVendorsDomainReadEnabled) {
            return <Loader isSpinnerOnly label={t`Loading...`} />;
        }

        return (
            <Stack direction="column" data-id="pudzuvid">
                <ContentAside
                    data-testid="VendorsProfileSecurityReviewSocView"
                    data-id="FmVVf_kM"
                    content={<VendorsSecuritySOCCompletedReportReview />}
                >
                    <Stack direction="column">
                        <Stack direction="column" gap="xl" pb="xl">
                            <VendorsSecurityReviewsCompletedCalloutCard />

                            <VendorSecurityReviewsAISummaryComponent />
                        </Stack>
                        <Box height="100%">
                            <PdfViewer
                                src={pdfDownloadUrl?.signedUrl ?? ''}
                                label={'pdf-viewer'}
                                data-id={'cosmos-pdf-viewer'}
                            />
                        </Box>
                    </Stack>
                </ContentAside>
            </Stack>
        );
    },
);
