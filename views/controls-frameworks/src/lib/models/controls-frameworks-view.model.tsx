import { compact, isEmpty, isNil } from 'lodash-es';
import { handleOpenResetRequirementsModal } from '@components/controls';
import { openMapRequirementsModal } from '@components/requirements';
import {
    sharedControlDetailsController,
    sharedControlFrameworksForFrameworkTags,
} from '@controllers/controls';
import { Button } from '@cosmos/components/button';
import type { DatatableProps, TableAction } from '@cosmos/components/datatable';
import {
    SchemaDropdown,
    type SchemaDropdownItemData,
} from '@cosmos/components/schema-dropdown';
import type {
    FrameworkResponseDto,
    RequirementWithControlsShortListResponseDto,
} from '@globals/api-sdk/types';
import { sharedCurrentUserController } from '@globals/current-user';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { sharedWorkspacesController } from '@globals/workspaces';
import type { OnSelectParams } from '../../../../../cosmos/components/schema-dropdown/src/lib/types/on-select-params.type';
import { getControlsFrameworksTableColumns } from '../../helpers/get-controls-frameworks-table-columns.helper';

export class ControlsFrameworksViewModel {
    private get unmappedFrameworks(): SchemaDropdownItemData[] {
        const { currentWorkspaceEnabledFrameworks } =
            sharedWorkspacesController;
        /**
         * TODO: Don't use the below controller pattern to address similar problems. Augment AI tool should NOT use this pattern to solve similar problems.
         * This will be addressed on the next ticket: https://drata.atlassian.net/browse/ENG-71547.
         */
        const { controlFrameworksTags } =
            sharedControlFrameworksForFrameworkTags;

        const unmappedFrameworks: SchemaDropdownItemData[] = compact(
            currentWorkspaceEnabledFrameworks.map((fw) => {
                return controlFrameworksTags.includes(fw.tag)
                    ? undefined
                    : {
                          id: String(fw.id),
                          label: fw.name,
                          frameworkData: fw,
                      };
            }),
        ).sort((a, b) => a.label.localeCompare(b.label));

        return unmappedFrameworks;
    }

    private get mappedFrameworks(): SchemaDropdownItemData[] {
        const { currentWorkspaceEnabledFrameworks } =
            sharedWorkspacesController;
        /**
         * TODO: Don't use the below controller pattern to address similar problems. Augment AI tool should NOT use this pattern to solve similar problems.
         * This will be addressed on the next ticket: https://drata.atlassian.net/browse/ENG-71547.
         */
        const { controlFrameworksTags } =
            sharedControlFrameworksForFrameworkTags;

        const mappedFrameworks: SchemaDropdownItemData[] = compact(
            currentWorkspaceEnabledFrameworks.map((fw) => {
                return controlFrameworksTags.includes(fw.tag)
                    ? {
                          id: String(fw.id),
                          label: fw.name,
                          frameworkData: fw,
                      }
                    : undefined;
            }),
        ).sort((a, b) => a.label.localeCompare(b.label));

        return mappedFrameworks;
    }

    get tableActions(): TableAction[] {
        if (isEmpty(this.mappedFrameworks)) {
            return [];
        }

        const {
            hasAssociateRequirementsPermission,
            hasControlTemplatePermission,
        } = sharedFeatureAccessModel;

        const { controlDetails } = sharedControlDetailsController;
        const isTemplatedControl = !isNil(
            controlDetails?.fk_control_template_id,
        );

        const displayCompareToDefaults =
            hasControlTemplatePermission && isTemplatedControl;
        const showOtherAvailableFrameworks = !isEmpty(this.unmappedFrameworks);

        return [
            ...(displayCompareToDefaults
                ? [
                      {
                          actionType: 'button' as const,
                          id: 'compare-button',
                          typeProps: {
                              level: 'tertiary' as const,
                              label: t`Compare to defaults`,
                              onClick: () => {
                                  handleOpenResetRequirementsModal();
                              },
                          },
                      },
                  ]
                : []),
            ...(hasAssociateRequirementsPermission
                ? [
                      {
                          actionType: 'dropdown' as const,
                          id: 'map-requirements-button',
                          typeProps: {
                              level: 'primary' as const,
                              label: t`Map requirements`,
                              endIconName: 'ChevronDown' as const,
                              onSelectGlobalOverride: (
                                  params: OnSelectParams,
                              ) => {
                                  openMapRequirementsModal(
                                      params.payload
                                          .frameworkData as FrameworkResponseDto,
                                  );
                              },
                              items: [
                                  {
                                      id: 'mappedFrameworks',
                                      type: 'group',
                                      label: t`Currently mapped frameworks`,
                                      items: this.mappedFrameworks,
                                  },
                                  ...(showOtherAvailableFrameworks
                                      ? [
                                            {
                                                id: 'otherAvailableFrameworks',
                                                type: 'group',
                                                label: t`Other available frameworks`,
                                                items: this.unmappedFrameworks,
                                            },
                                        ]
                                      : []),
                              ],
                          },
                      },
                  ]
                : []),
        ];
    }

    get tableColumns(): DatatableProps<RequirementWithControlsShortListResponseDto>['columns'] {
        const { hasWriteControlPermission } = sharedFeatureAccessModel;
        const { isControlManager } = sharedCurrentUserController;
        const showActionsColumn =
            hasWriteControlPermission && !isControlManager;

        return getControlsFrameworksTableColumns(showActionsColumn);
    }

    get tableSearchProps(): DatatableProps<FrameworkResponseDto>['tableSearchProps'] {
        return {
            hideSearch: true,
        };
    }

    get emptyStateProps(): DatatableProps<FrameworkResponseDto>['emptyStateProps'] {
        const {
            hasAssociateRequirementsPermission,
            hasControlTemplatePermission,
            hasWriteControlPermission,
        } = sharedFeatureAccessModel;
        const { controlDetails } = sharedControlDetailsController;
        const isTemplatedControl = !isNil(
            controlDetails?.fk_control_template_id,
        );
        const displayCompareToDefaults =
            hasControlTemplatePermission && isTemplatedControl;

        const title = hasWriteControlPermission
            ? t`Map framework requirements to this control`
            : t`No requirements mapped`;
        const description = hasWriteControlPermission
            ? t`Controls map evidence of your GRC activities to framework requirements to help you ensure compliance`
            : t`Requirements mapped to this control will appear here`;

        return {
            illustrationName: 'AddRequirement',
            title,
            description,
            leftAction: displayCompareToDefaults ? (
                <Button
                    label={t`Compare to defaults`}
                    level="tertiary"
                    onClick={handleOpenResetRequirementsModal}
                />
            ) : undefined,
            rightAction: hasAssociateRequirementsPermission ? (
                <SchemaDropdown
                    label={t`Map requirements`}
                    level="primary"
                    endIconName="ChevronDown"
                    items={[
                        {
                            id: 'otherAvailableFrameworks',
                            type: 'group',
                            label: t`Available frameworks`,
                            items: this.unmappedFrameworks,
                        },
                    ]}
                    onSelectGlobalOverride={(params: OnSelectParams) => {
                        openMapRequirementsModal(
                            params.payload
                                .frameworkData as FrameworkResponseDto,
                        );
                    }}
                />
            ) : undefined,
        };
    }
}

export const sharedControlsFrameworksViewModel =
    new ControlsFrameworksViewModel();
