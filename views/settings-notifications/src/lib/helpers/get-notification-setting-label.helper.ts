import type { UserConfigurableSettingResponseDto } from '@globals/api-sdk/types';

export function getNotificationSettingLabel(
    featureType: UserConfigurableSettingResponseDto['type'] | 'ALL',
): string {
    switch (featureType) {
        case 'ALL': {
            return 'Email notifications';
        }

        /* Audits */
        case 'REQUEST_MESSAGE': {
            return 'New messages';
        }
        case 'REQUEST_STATUS_CHANGE': {
            return 'Status updates for requests';
        }
        /* Controls */
        case 'CONTROL_EVIDENCE_UPDATE_NOTIFICATION': {
            return 'Control updated';
        }
        case 'CONTROL_APPROVAL_NOTIFICATION': {
            return 'Control approver notifications';
        }
        case 'CONTROL_NEW_INTERNAL_NOTES': {
            return 'Internal note added to a control';
        }
        case 'CONTROL_EMAIL_FREQUENCY': {
            return 'Status change for controls assigned to me';
        }
        /* Monitoring */
        case 'EVIDENCE_COLLECTION_ERROR': {
            return 'Evidence collection error';
        }
        case 'TEST_ERROR_EMAIL_FREQUENCY': {
            return 'Automated test error';
        }
        /* Policies */
        case 'REPLIES_ON_POLICIES_THREADS': {
            return 'Policy comment replies';
        }
        case 'UPDATES_TO_POLICIES_ASSIGNED_TO_ME': {
            return 'Updates for policies assigned to me';
        }
        /* Risk */
        case 'NOTIFY_RISK_OWNER': {
            return 'Risk owner notifications';
        }
        /* Tasks */
        case 'MONTHLY_REMINDER_FOR_UPCOMING_TASKS': {
            return 'Upcoming task reminder';
        }
        case 'COMPLIANCE_EMAIL': {
            return 'Annual compliance task reminder';
        }
        case 'OVERDUE_TASKS_REMINDERS': {
            return 'Past due tasks';
        }
        /* Vulnerabilities */
        case 'NOTIFY_SLA_VULNERABILITIES': {
            return 'Reminders for vulnerabilities with missed or upcoming SLAs';
        }
        /* Workflows */
        case 'WORKFLOW_CREATOR_NOTIFICATIONS': {
            return 'Workflow creator notifications';
        }

        default: {
            return 'Unknown';
        }
    }
}
