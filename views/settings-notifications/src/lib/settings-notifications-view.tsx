import { isEmpty } from 'lodash-es';
import type React from 'react';
import { sharedConnectionsController } from '@controllers/connections';
import { sharedUserSettingsController } from '@controllers/settings-user';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { ToggleField } from '@cosmos/components/toggle-field';
import { Divider } from '@cosmos-lab/components/divider';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { observer } from '@globals/mobx';
import { NotificationSetting } from './components/notification-setting.component';
import { NOTIFICATION_SETTINGS } from './constants/notification-settings.constant';
import { areAuditHubSettingsAllowed } from './helpers/are-audit-hub-settings-allowed.helper';
import { areControlReadinessSettingsAllowed } from './helpers/are-control-readiness-settings-allowed.helper';
import { areVulnerabilitiesSettingsAllowed } from './helpers/are-vulnerabilities-settings-allowed.helper';
import { areWorkflowsSettingsAllowed } from './helpers/are-workflows-settings-allowed.helper';
import { getNotificationGroupLabel } from './helpers/get-notification-group-label.helper';
import { getNotificationSettingLabel } from './helpers/get-notification-setting-label.helper';

const FORM_ID = 'settings-user-notifications-form';

export const SettingsNotificationsView = observer(
    (): React.JSX.Element | null => {
        const { featureSettings, isLoading: isFeatureSettingsLoading } =
            sharedUserSettingsController;
        const {
            isLoading: isActiveConnectionsLoading,
            allConfiguredConnections: activeConnections,
        } = sharedConnectionsController;

        const { isReleaseOverdueTasksEnabled } = sharedFeatureAccessModel;

        if (isFeatureSettingsLoading || isActiveConnectionsLoading) {
            return null;
        }

        // Safe check since API is currently returning one BETA_OPT_IN
        const notificationSettings = featureSettings.filter(
            (setting) => setting.group === 'EMAIL_NOTIFICATIONS',
        );

        if (isEmpty(notificationSettings)) {
            throw new Error('User notification settings not found');
        }

        const allowedNotificationSettings = notificationSettings.filter(
            (setting) => {
                switch (setting.type) {
                    case 'REQUEST_MESSAGE':
                    case 'REQUEST_STATUS_CHANGE': {
                        // TODO: Use global entitlement flag controller
                        return areAuditHubSettingsAllowed();
                    }
                    case 'CONTROL_APPROVAL_NOTIFICATION':
                    case 'CONTROL_NEW_INTERNAL_NOTES': {
                        // TODO: Use global entitlement flag controller
                        return areControlReadinessSettingsAllowed();
                    }
                    case 'NOTIFY_SLA_VULNERABILITIES': {
                        // TODO: Use global entitlement flag controller
                        return areVulnerabilitiesSettingsAllowed(
                            activeConnections,
                        );
                    }
                    case 'WORKFLOW_CREATOR_NOTIFICATIONS': {
                        // TODO: Use global entitlement flag controller
                        return areWorkflowsSettingsAllowed();
                    }
                    case 'OVERDUE_TASKS_REMINDERS': {
                        return isReleaseOverdueTasksEnabled;
                    }
                    default: {
                        return true;
                    }
                }
            },
        );

        const areNotificationsEnabled = allowedNotificationSettings.some(
            (setting) => setting.enabledAt,
        );

        return (
            <Stack
                direction="column"
                gap="6x"
                data-testid="SettingsNotificationsView"
                data-id="7QmXb1Xm"
            >
                <Text type="title" size="200">
                    {getNotificationGroupLabel('ALL')}
                </Text>
                <ToggleField
                    label={getNotificationSettingLabel('ALL')}
                    checked={areNotificationsEnabled}
                    formId={FORM_ID}
                    name="isNotificationEnabledAll"
                    data-testid="NotificationSettingToggleNotificationAll"
                    data-id="uRApX_Ot"
                    layout="input-left"
                />
                {areNotificationsEnabled && (
                    <>
                        <Divider />

                        {NOTIFICATION_SETTINGS.map((section) => {
                            const hasAllowedSettings =
                                section.featureSettings.some((featureSetting) =>
                                    allowedNotificationSettings.some(
                                        (s) =>
                                            s.type ===
                                            featureSetting.featureType,
                                    ),
                                );

                            return (
                                <Stack
                                    direction="column"
                                    gap="6x"
                                    data-testid="SettingsNotificationsSection"
                                    data-id="7QmXb1Xm"
                                    key={section.group}
                                >
                                    {hasAllowedSettings && (
                                        <Text type="title" size="200">
                                            {getNotificationGroupLabel(
                                                section.group,
                                            )}
                                        </Text>
                                    )}

                                    {section.featureSettings.map(
                                        (featureSetting) => {
                                            const setting =
                                                allowedNotificationSettings.find(
                                                    (s) =>
                                                        s.type ===
                                                        featureSetting.featureType,
                                                );

                                            return (
                                                setting && (
                                                    <NotificationSetting
                                                        key={setting.type}
                                                        data-testid="NotificationSetting"
                                                        data-id={`notification-setting-${setting.id}`}
                                                        setting={setting}
                                                        hasFrequencyOptions={
                                                            featureSetting.hasFrequencyOptions
                                                        }
                                                    />
                                                )
                                            );
                                        },
                                    )}
                                </Stack>
                            );
                        })}
                    </>
                )}
            </Stack>
        );
    },
);
