import type { UserConfigurableSettingResponseDto } from '@globals/api-sdk/types';
import type { NotificationGroup } from '../types/notification-group.type';

export const NOTIFICATION_SETTINGS: {
    group: NotificationGroup;
    featureSettings: {
        featureType: UserConfigurableSettingResponseDto['type'];
        hasFrequencyOptions: boolean;
    }[];
}[] = [
    {
        group: 'AUDITS',
        featureSettings: [
            {
                featureType: 'REQUEST_MESSAGE',
                hasFrequencyOptions: false,
            },
            {
                featureType: 'REQUEST_STATUS_CHANGE',
                hasFrequencyOptions: false,
            },
        ],
    },
    {
        group: 'CONTROLS',
        featureSettings: [
            {
                featureType: 'CONTROL_EVIDENCE_UPDATE_NOTIFICATION',
                hasFrequencyOptions: false,
            },
            {
                featureType: 'CONTROL_APPROVAL_NOTIFICATION',
                hasFrequencyOptions: false,
            },
            {
                featureType: 'CONTROL_NEW_INTERNAL_NOTES',
                hasFrequencyOptions: false,
            },
            {
                featureType: 'CONTROL_EMAIL_FREQUENCY',
                hasFrequencyOptions: true,
            },
        ],
    },
    {
        group: 'MONITORING',
        featureSettings: [
            {
                featureType: 'EVIDENCE_COLLECTION_ERROR',
                hasFrequencyOptions: false,
            },
            {
                featureType: 'TEST_ERROR_EMAIL_FREQUENCY',
                hasFrequencyOptions: true,
            },
        ],
    },
    {
        group: 'POLICIES',
        featureSettings: [
            {
                featureType: 'REPLIES_ON_POLICIES_THREADS',
                hasFrequencyOptions: false,
            },
            {
                featureType: 'UPDATES_TO_POLICIES_ASSIGNED_TO_ME',
                hasFrequencyOptions: false,
            },
        ],
    },
    {
        group: 'RISK',
        featureSettings: [
            {
                featureType: 'NOTIFY_RISK_OWNER',
                hasFrequencyOptions: false,
            },
        ],
    },
    {
        group: 'TASKS',
        featureSettings: [
            {
                featureType: 'MONTHLY_REMINDER_FOR_UPCOMING_TASKS',
                hasFrequencyOptions: false,
            },
            {
                featureType: 'COMPLIANCE_EMAIL',
                hasFrequencyOptions: false,
            },
            {
                featureType: 'OVERDUE_TASKS_REMINDERS',
                hasFrequencyOptions: false,
            },
        ],
    },
    {
        group: 'VULNERABILITIES',
        featureSettings: [
            {
                featureType: 'NOTIFY_SLA_VULNERABILITIES',
                hasFrequencyOptions: true,
            },
        ],
    },
    {
        group: 'WORKFLOWS',
        featureSettings: [
            {
                featureType: 'WORKFLOW_CREATOR_NOTIFICATIONS',
                hasFrequencyOptions: false,
            },
        ],
    },
] as const;
