import { useEffect } from 'react';
import {
    VendorQuestionnairesDetailsComponent,
    VendorQuestionnairesQuestionsComponent,
} from '@components/vendor-questionnaires';
import {
    type QuestionnaireStatus,
    sharedVendorsQuestionnaireAddController,
} from '@controllers/vendors';
import { Button } from '@cosmos/components/button';
import { Card } from '@cosmos/components/card';
import { Grid } from '@cosmos/components/grid';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action, observer, runInAction } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { getParentRoute } from '@helpers/path';
import { useLocation, useNavigate } from '@remix-run/react';

export const VendorsQuestionnairesAddView = observer((): React.JSX.Element => {
    const { currentWorkspace } = sharedWorkspacesController;
    const {
        isReadonlySecurityQuestionnaires: isReadonly,
        canEditSecurityQuestionnaires: canEdit,
    } = sharedFeatureAccessModel;
    const navigate = useNavigate();
    const location = useLocation();

    // Check if user has any permission to view questionnaires
    useEffect(() => {
        runInAction(() => {
            if (!canEdit && !isReadonly && currentWorkspace?.id) {
                // Redirect to questionnaires table if no permissions at all
                navigate(
                    `/workspaces/${currentWorkspace.id}/vendors/questionnaires`,
                );
            }
        });
    }, [canEdit, isReadonly, currentWorkspace?.id, navigate]);

    // Show loader while redirecting if no permissions
    if (!canEdit && !isReadonly) {
        return <Loader label={t`Loading`} />;
    }

    const controller = sharedVendorsQuestionnaireAddController;

    const handleSubmit = action(() => {
        const status: QuestionnaireStatus = 'ACTIVE';

        controller.submitFormAndNavigate(() => {
            const parentRoute = getParentRoute(location.pathname);

            navigate(parentRoute);
        }, status);
    });

    const handleSaveDraft = action(() => {
        const status: QuestionnaireStatus = 'DRAFT';

        controller.submitFormAndNavigate(() => {
            const parentRoute = getParentRoute(location.pathname);

            navigate(parentRoute);
        }, status);
    });

    return (
        <Grid
            gap={'6x'}
            data-testid="VendorsQuestionnairesAddView"
            data-id="sy3MCFWV"
        >
            <Card
                size="lg"
                title={t`Details`}
                body={
                    <VendorQuestionnairesDetailsComponent
                        isCreateMode={!isReadonly}
                    />
                }
            />
            <Card
                size="lg"
                title={t`Questions`}
                body={
                    <VendorQuestionnairesQuestionsComponent
                        isCreateMode={!isReadonly}
                    />
                }
            />

            {!isReadonly && (
                <Stack direction="row" gap="4x" justify="end">
                    <Button
                        level="secondary"
                        type="button"
                        disabled={
                            controller.isSubmitting || !controller.canSubmit
                        }
                        label={
                            controller.isSubmitting
                                ? t`Saving...`
                                : t`Save draft`
                        }
                        onClick={handleSaveDraft}
                    />
                    <Button
                        level="primary"
                        type="button"
                        label={controller.isSubmitting ? t`Saving...` : t`Save`}
                        disabled={
                            controller.isSubmitting || !controller.canSubmit
                        }
                        onClick={handleSubmit}
                    />
                </Stack>
            )}
        </Grid>
    );
});
