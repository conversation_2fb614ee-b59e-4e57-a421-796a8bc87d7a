import { modalController } from '@controllers/modal';
import { action } from '@globals/mobx';
import { CopyQuestionnaireModal } from '../modals/copy-questionnaire-modal';

const COPY_QUESTIONNAIRE_MODAL_ID = 'copy-questionnaire-modal';

export interface OpenCopyQuestionnaireModalProps {
    defaultName: string;
    questionnaireId: number;
}

export const openCopyQuestionnaireModal = action(
    ({
        defaultName,
        questionnaireId,
    }: OpenCopyQuestionnaireModalProps): void => {
        modalController.openModal({
            id: COPY_QUESTIONNAIRE_MODAL_ID,
            content: () => (
                <CopyQuestionnaireModal
                    defaultName={defaultName}
                    data-id="copy-questionnaire-modal"
                    questionnaireId={questionnaireId}
                />
            ),
            centered: true,
            disableClickOutsideToClose: false,
            size: 'md',
        });
    },
);

export const closeCopyQuestionnaireModal = action((): void => {
    modalController.closeModal(COPY_QUESTIONNAIRE_MODAL_ID);
});
