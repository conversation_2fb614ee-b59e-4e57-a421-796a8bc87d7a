import { useEffect, useState } from 'react';
import { sharedCopyQuestionnaireValidationController } from '@controllers/copy-questionnaire-validation';
import { modalController } from '@controllers/modal';
import { sharedVendorsTypeformQuestionnairesController } from '@controllers/vendors';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { TextField } from '@cosmos/components/text-field';
import { t } from '@globals/i18n/macro';
import { action, observer, runInAction } from '@globals/mobx';

interface ValidationController {
    validateTitle: (title: string) => void;
    getTitleValidationError: (title: string) => string | null;
    titleValidationQuery: {
        isLoading: boolean;
    };
}

export interface CopyQuestionnaireModalProps {
    defaultName: string;
    questionnaireId: number;
}

const CopyQuestionnaireModalContent = observer(
    ({
        defaultName,
        questionnaireId,
    }: CopyQuestionnaireModalProps): React.JSX.Element => {
        const [name, setName] = useState(defaultName);
        const [isLoading, setIsLoading] = useState(false);

        // Type-safe controller access
        const controller =
            sharedCopyQuestionnaireValidationController as ValidationController;
        const { validateTitle, getTitleValidationError, titleValidationQuery } =
            controller;
        const { copyQuestionnaireWithName } =
            sharedVendorsTypeformQuestionnairesController;

        // Validate title when name changes
        useEffect(() => {
            runInAction(() => {
                validateTitle(name);
            });
        }, [name, validateTitle]);

        const handleSubmit = action(() => {
            if (
                !name.trim() ||
                getTitleValidationError(name) ||
                titleValidationQuery.isLoading
            ) {
                return;
            }

            setIsLoading(true);

            // Call the copy method with completion callback
            copyQuestionnaireWithName(questionnaireId, name.trim(), () => {
                setIsLoading(false);
            });
        });

        const handleCancel = action(() => {
            modalController.closeModal('copy-questionnaire-modal');
        });

        const handleKeyPress = (e: React.KeyboardEvent) => {
            if (
                e.key === 'Enter' &&
                name.trim() &&
                !getTitleValidationError(name) &&
                !titleValidationQuery.isLoading &&
                !isLoading
            ) {
                handleSubmit();
            }
        };

        const titleError = getTitleValidationError(name);

        const getFeedback = () => {
            if (titleError) {
                return {
                    type: 'error' as const,
                    message: titleError,
                };
            }
            if (!name.trim()) {
                return {
                    type: 'error' as const,
                    message: t`Name is required`,
                };
            }

            return undefined;
        };
        const isSubmitDisabled =
            !name.trim() ||
            Boolean(titleError) ||
            titleValidationQuery.isLoading ||
            isLoading;

        return (
            <>
                <Modal.Header
                    title={t`Copy Questionnaire`}
                    closeButtonAriaLabel={t`Close modal`}
                    onClose={handleCancel}
                />
                <Modal.Body>
                    <Stack direction="column" gap="md">
                        <TextField
                            label={t`Questionnaire name`}
                            formId="copy-questionnaire-form"
                            name="questionnaire-name"
                            value={name}
                            disabled={isLoading}
                            feedback={getFeedback()}
                            onKeyDown={handleKeyPress}
                            onChange={(e) => {
                                setName(e.target.value);
                            }}
                        />
                    </Stack>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Discard`,
                            level: 'tertiary',
                            onClick: handleCancel,
                            disabled: isLoading,
                        },
                        {
                            label: t`Save`,
                            level: 'primary',
                            onClick: handleSubmit,
                            disabled: isSubmitDisabled,
                            isLoading,
                        },
                    ]}
                />
            </>
        );
    },
);

export const CopyQuestionnaireModal = ({
    defaultName,
    questionnaireId,
}: CopyQuestionnaireModalProps): React.JSX.Element => {
    return (
        <CopyQuestionnaireModalContent
            key={defaultName}
            defaultName={defaultName}
            questionnaireId={questionnaireId}
            data-testid="CopyQuestionnaireModal"
            data-id="udY6Bvrn"
        />
    );
};
