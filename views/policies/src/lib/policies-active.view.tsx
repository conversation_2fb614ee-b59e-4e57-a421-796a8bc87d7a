import {
    PoliciesDeletedBanner,
    PoliciesExternalPolicyBanner,
    PoliciesOutdatedBanner,
    PoliciesOverviewMetricComponent,
} from '@components/policies';
import { Datatable } from '@cosmos/components/datatable';
import { Grid } from '@cosmos/components/grid';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import { sharedPoliciesActiveViewModel } from './policies-view.controller';

export const PoliciesActiveView = observer((): React.JSX.Element => {
    const navigate = useNavigate();

    const {
        mappedOverviewData,
        activePolicies,
        columns,
        tableActions,
        loadActivePolicies,
        isActivePoliciesLoading,
        activePoliciesTotal,
        overviewFilter,
        filters,
    } = sharedPoliciesActiveViewModel;

    const handleRowClick = ({ row }: { row: { id: number } }) => {
        const { currentWorkspaceId: workspaceId } = sharedWorkspacesController;

        navigate(
            `/workspaces/${workspaceId}/governance/policies/builder/${row.id}/overview`,
        );
    };

    return (
        <Grid gap="lg" data-testid="PoliciesActiveView" data-id="CG_3uqvf">
            <PoliciesExternalPolicyBanner />
            <PoliciesOutdatedBanner />
            <PoliciesDeletedBanner />
            <Stack gap="4x">
                {mappedOverviewData.map((overview) => (
                    <PoliciesOverviewMetricComponent
                        key={overview.label}
                        data-id={overview.label}
                        value={overview.value}
                        label={overview.label}
                        search={overview.search}
                    />
                ))}
            </Stack>
            <Datatable
                key={`datatable-active-policies-${overviewFilter || 'none'}`}
                isLoading={isActivePoliciesLoading}
                tableId="datatable-active-policies"
                data-id="datatable-active-policies"
                data={activePolicies}
                columns={columns}
                total={activePoliciesTotal}
                filterProps={filters}
                tableActions={tableActions}
                emptyStateProps={sharedPoliciesActiveViewModel.getEmptyStateProps()}
                tableSearchProps={{
                    hideSearch: false,
                    placeholder: t({
                        message: 'Search by name...',
                        comment: 'Placeholder text for policy name search',
                    }),
                }}
                filterViewModeProps={{
                    props: {
                        initialSelectedOption: 'pinned',
                        togglePinnedLabel: t({
                            message: 'Pin filters to page',
                            comment: 'Label for pinning filters to page',
                        }),
                        toggleUnpinnedLabel: t({
                            message: 'Move filters to dropdown',
                            comment: 'Label for moving filters to dropdown',
                        }),
                        selectedOption: 'pinned',
                    },
                    viewMode: 'toggleable',
                }}
                onFetchData={loadActivePolicies}
                onRowClick={handleRowClick}
            />
        </Grid>
    );
});
