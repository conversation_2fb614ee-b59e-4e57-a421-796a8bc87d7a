import type { DatatableProps } from '@cosmos/components/datatable';
import type { LibraryTestTemplateMappingsResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { COLUMN_SIZES } from '@helpers/table';
import { LibraryTestMappingsFrameworksCell } from '../lib/cells/library-test-mappings-frameworks-cell';
import { LIBRARY_TEST_MAPPING_NAME_ID } from './library-test-mappings-filter.constants';

export const getLibraryTestMappingsColumns =
    (): DatatableProps<LibraryTestTemplateMappingsResponseDto>['columns'] => [
        {
            id: 'CODE',
            header: () => t`Code`,
            accessorFn: (row) => row.controlCode,
            enableSorting: true,
            minSize: COLUMN_SIZES.LARGE,
        },
        {
            id: LIBRARY_TEST_MAPPING_NAME_ID,
            header: () => t`Name`,
            accessorFn: (row) => row.controlName,
            enableSorting: true,
            minSize: COLUMN_SIZES.XLARGE,
        },
        {
            id: 'TYPE',
            header: () => t`Type`,
            accessorFn: () => 'Control',
            enableSorting: false,
            minSize: COLUMN_SIZES.LARGE,
        },
        {
            id: 'FRAMEWORKS',
            header: () => t`Frameworks`,
            accessorKey: 'frameworks',
            cell: LibraryTestMappingsFrameworksCell,
            enableSorting: false,
            minSize: COLUMN_SIZES.LARGE,
        },
    ];
