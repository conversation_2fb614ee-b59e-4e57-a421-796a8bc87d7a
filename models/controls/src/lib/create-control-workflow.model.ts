import { isEmpty, isNumber } from 'lodash-es';
import {
    sharedControlCustomFieldsController,
    sharedControlCustomFieldsListController,
    sharedCreateControlController,
} from '@controllers/controls';
import { sharedCustomFieldsSubmissionController } from '@controllers/custom-fields';
import { snackbarController } from '@controllers/snackbar';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { getParentRoute } from '@helpers/path';
import { sharedCustomFieldsValuesModel } from '@models/custom-fields';
import type { Location, NavigateFunction } from '@remix-run/react';
import { transformCustomFieldName } from '../helpers/create-control-custom-field.helper';
import { sharedControlInfoFormModel } from './control-info-form-model';

type CreateControlFunction = (
    storedValues: Record<string, unknown>,
    onSuccess: () => void,
    onError: (errorMessage: string) => void,
) => void;

interface CreateControlMutation {
    response?: {
        id?: number;
        [key: string]: unknown;
    } | null;
}

class CreateControlWorkflowModel {
    isCompletingWorkflow = false;

    constructor() {
        makeAutoObservable(this);
    }

    get isWorkflowBusy(): boolean {
        return (
            sharedCreateControlController.isSubmitting ||
            this.isCompletingWorkflow
        );
    }

    handleCompleteControlCreation = (
        navigate: (path: string) => void,
        location: { pathname: string },
        createControlFn: CreateControlFunction,
        createControlMutation: CreateControlMutation,
    ): void => {
        const { storedValues } = sharedControlInfoFormModel;

        createControlFn(
            storedValues,
            () => {
                const createdControl = createControlMutation.response;
                const controlId = createdControl?.id;

                const { customFieldValues } = sharedCustomFieldsValuesModel;

                if (
                    controlId &&
                    isNumber(controlId) &&
                    !isEmpty(customFieldValues)
                ) {
                    const { customFieldsList } =
                        sharedControlCustomFieldsListController;
                    const controlInfoFields =
                        customFieldsList.find(
                            (section) => section.section === 'CONTROL_INFO',
                        )?.customFields ?? [];

                    const customFieldSubmissions = Object.entries(
                        customFieldValues,
                    ).map(([fieldName, value]) => {
                        const fieldDefinition = controlInfoFields.find(
                            (field) => field.name === fieldName,
                        );

                        return {
                            fieldName: transformCustomFieldName(fieldName),
                            actualFieldName: fieldName,
                            customFieldId: fieldDefinition?.customFieldId,
                            customFieldLocationId:
                                fieldDefinition?.customFieldLocationId,
                            fieldType: fieldDefinition?.fieldType,
                            value,
                        };
                    });

                    sharedCustomFieldsSubmissionController.submitCustomFields(
                        controlId,
                        customFieldSubmissions,
                        () => {
                            sharedControlCustomFieldsController.controlCustomFieldsQuery.invalidate();
                            sharedCustomFieldsValuesModel.reset();
                        },
                        (error: string) => {
                            snackbarController.addSnackbar({
                                id: 'custom-fields-submit-error',
                                props: {
                                    title: t`Control created, but custom fields failed to save`,
                                    description: error || t`Please try again`,
                                    severity: 'critical',
                                    closeButtonAriaLabel: t`Close`,
                                },
                            });
                            sharedCustomFieldsValuesModel.reset();

                            const parentPath = getParentRoute(
                                location.pathname,
                            );

                            navigate(parentPath);
                        },
                    );
                }
            },
            (errorMessage: string) => {
                snackbarController.addSnackbar({
                    id: 'control-create-error',
                    props: {
                        title: t`Failed to create control`,
                        description: errorMessage,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    handleComplete = (
        navigate: NavigateFunction,
        location: Location,
        createControlFn: CreateControlFunction,
        createControlMutation: CreateControlMutation,
    ): void => {
        if (this.isWorkflowBusy) {
            return;
        }

        this.isCompletingWorkflow = true;

        this.handleCompleteControlCreation(
            navigate,
            location,
            createControlFn,
            createControlMutation,
        );
    };

    handleCancel = (navigate: NavigateFunction, location: Location): void => {
        const parentPath = getParentRoute(location.pathname);

        navigate(parentPath);
    };
}

export const sharedCreateControlWorkflowModel =
    new CreateControlWorkflowModel();
