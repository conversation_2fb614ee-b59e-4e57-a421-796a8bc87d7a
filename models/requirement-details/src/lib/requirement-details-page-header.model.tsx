import { isNil } from 'lodash-es';
import { OutOfScopeModal } from '@components/out-of-scope-modal';
import { sharedFrameworkDetailsController } from '@controllers/frameworks';
import { modalController } from '@controllers/modal';
import {
    sharedRequirementDetailsController,
    sharedRequirementsController,
} from '@controllers/requirements';
import { type Action, ActionStack } from '@cosmos/components/action-stack';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import { Metadata } from '@cosmos/components/metadata';
import type { SchemaDropdownItems } from '@cosmos/components/schema-dropdown';
import { dimensionSm } from '@cosmos/constants/tokens';
import type { Breadcrumb } from '@cosmos-lab/components/breadcrumbs';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable, when } from '@globals/mobx';

const OUT_OF_SCOPE_CONTROLS_MODAL_ID = 'out-of-scope-controls-modal';

export class RequirementDetailsPageHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    get breadcrumbs(): Breadcrumb[] {
        const { frameworkDetails } = sharedFrameworkDetailsController;

        if (!frameworkDetails) {
            return [
                {
                    label: t`Frameworks`,
                    /**
                     * Example:
                     * from - /workspaces/1/compliance/frameworks/all/current/3/requirements/202/overview
                     * to   - /workspaces/1/compliance/frameworks/all/current.
                     */ pathname: window.location.pathname
                        .split('/')
                        .slice(0, -4)
                        .join('/'),
                },
            ];
        }

        return [
            {
                label: t`Frameworks`,
                /**
                 * Example:
                 * from - /workspaces/1/compliance/frameworks/all/current/3/requirements/202/overview
                 * to   - /workspaces/1/compliance/frameworks/all/current.
                 */
                pathname: window.location.pathname
                    .split('/')
                    .slice(0, -4)
                    .join('/'),
            },
            {
                label: frameworkDetails.slug.toUpperCase(),
                /**
                 * Example:
                 * from - /workspaces/1/compliance/frameworks/all/current/3/requirements/202/overview
                 * to   - /workspaces/1/compliance/frameworks/all/current/3/requirements.
                 */
                pathname: window.location.pathname
                    .split('/')
                    .slice(0, -2)
                    .join('/'),
            },
        ];
    }

    get isLoading(): boolean {
        const { isRequirementLoading, isRequirementFetching } =
            sharedRequirementDetailsController;
        const { areRequirementsLoading, isUpdatingScope } =
            sharedRequirementsController;

        return (
            isUpdatingScope ||
            isRequirementLoading ||
            areRequirementsLoading ||
            isRequirementFetching
        );
    }

    get title(): string {
        const { requirement } = sharedRequirementDetailsController;

        if (!requirement) {
            return '';
        }

        return requirement.description;
    }

    get slot(): React.JSX.Element | null {
        const { requirement } = sharedRequirementDetailsController;

        if (!requirement) {
            return null;
        }

        const { isReady, archivedAt } = requirement;

        if (archivedAt) {
            return (
                <Metadata
                    colorScheme="neutral"
                    iconName="OutOfScope"
                    label={t`Out of scope`}
                    type="tag"
                    data-id="sfJW27Us"
                />
            );
        }

        const label = isReady ? t`Ready` : t`Not Ready`;

        return (
            <Metadata
                colorScheme={isReady ? 'success' : 'critical'}
                iconName={isReady ? 'CheckCircle' : 'Cancel'}
                label={label}
                type="tag"
                data-id="sfJW27Us"
            />
        );
    }

    get keyValuePairs(): KeyValuePairProps[] {
        const { requirement } = sharedRequirementDetailsController;

        if (!requirement) {
            return [];
        }

        return [
            {
                id: 'code',
                'data-id': 'ddfEErop',
                label: t`Code`,
                value: requirement.name,
                type: 'TEXT',
            },
        ];
    }

    get actionStack(): React.JSX.Element {
        const { requirement, requirementDetailsQuery } =
            sharedRequirementDetailsController;

        if (!requirement) {
            return (
                <ActionStack
                    gap={dimensionSm}
                    stacks={[
                        {
                            actions: [],
                            id: 'map-controls-action-stack',
                        },
                    ]}
                />
            );
        }

        const isOutOfScope = !isNil(requirement.archivedAt);

        const actions: Action[] = [];

        const handleInScope = action(() => {
            sharedRequirementsController.updateRequirementsScope({
                requirementIds: [requirement.id],
                isInScope: true,
            });
            when(
                () => !sharedRequirementsController.isUpdatingScope,
                () => {
                    requirementDetailsQuery.invalidate();
                },
            );
        });

        const handleOutOfScope = action((reason: string) => {
            sharedRequirementsController.updateRequirementsScope({
                requirementIds: [requirement.id],
                isInScope: false,
                rationale: reason,
            });
            when(
                () => !sharedRequirementsController.isUpdatingScope,
                () => {
                    requirementDetailsQuery.invalidate();
                },
            );
            modalController.closeModal(OUT_OF_SCOPE_CONTROLS_MODAL_ID);
        });

        const dropdownItems: SchemaDropdownItems = [];
        const { isRequirementLoading } = sharedRequirementDetailsController;
        const { areRequirementsLoading, isUpdatingScope } =
            sharedRequirementsController;

        const isLoadingData =
            isUpdatingScope || isRequirementLoading || areRequirementsLoading;

        if (isOutOfScope) {
            dropdownItems.push({
                id: 'in-scope-item',
                label: isLoadingData
                    ? t`Loading...`
                    : t`Mark requirement in scope`,
                onSelect: handleInScope,
            });
        } else {
            dropdownItems.push({
                id: 'out-of-scope-item',
                label: isLoadingData ? t`Loading...` : t`Mark out of scope`,
                onSelect: () => {
                    modalController.openModal({
                        id: OUT_OF_SCOPE_CONTROLS_MODAL_ID,
                        content: () => (
                            <OutOfScopeModal
                                data-id="disable-requirement-modal"
                                title={t`Mark requirements out of scope?`}
                                onConfirm={handleOutOfScope}
                                onCancel={() => {
                                    modalController.closeModal(
                                        OUT_OF_SCOPE_CONTROLS_MODAL_ID,
                                    );
                                }}
                            />
                        ),
                        centered: true,
                        disableClickOutsideToClose: true,
                        size: 'md',
                    });
                },
            });
        }

        actions.push({
            actionType: 'dropdown',
            id: 'requirement-actions-dropdown',
            typeProps: {
                label: t`More Actions`,
                isIconOnly: true,
                startIconName: 'HorizontalMenu',
                level: 'tertiary',
                colorScheme: 'primary',
                align: 'end',
                items: dropdownItems,
            },
        });

        return (
            <ActionStack
                gap={dimensionSm}
                stacks={[
                    {
                        actions,
                        id: 'map-controls-action-stack',
                    },
                ]}
            />
        );
    }
}
