import type { ComponentProps } from 'react';
import {
    sharedVendorsProfileReportsAndDocumentsMutationController,
    sharedVendorsQuestionnairesController,
} from '@controllers/vendors';
import type { ActionStack } from '@cosmos/components/action-stack';
import { action } from '@globals/mobx';

export const SECURITY_REVIEW_QUESTIONNAIRE_HEADER_KEY =
    'vendor-security-review-questionnaire-header';

export const getSecurityReviewQuestionnaireHeaderActionValues = (
    navigateAfterDeleteToRoute: string,
): ComponentProps<typeof ActionStack>['stacks'] => [
    {
        actions: [
            {
                actionType: 'button',
                id: `${SECURITY_REVIEW_QUESTIONNAIRE_HEADER_KEY}-delete-action`,
                typeProps: {
                    'data-id': `${SECURITY_REVIEW_QUESTIONNAIRE_HEADER_KEY}-delete-action`,
                    label: 'Delete file',
                    colorScheme: 'danger',
                    isIconOnly: true,
                    startIconName: 'Trash',
                    level: 'tertiary',
                    onClick: action(() => {
                        const { firstVendorQuestionnaire } =
                            sharedVendorsQuestionnairesController;

                        sharedVendorsQuestionnairesController.deleteQuestionnaire(
                            firstVendorQuestionnaire?.id || 0,
                            navigateAfterDeleteToRoute,
                        );
                    }),
                },
            },
            {
                actionType: 'button',
                id: `${SECURITY_REVIEW_QUESTIONNAIRE_HEADER_KEY}-download-action`,
                typeProps: {
                    'data-id': `${SECURITY_REVIEW_QUESTIONNAIRE_HEADER_KEY}-download-action`,
                    label: 'Download',
                    colorScheme: 'neutral',
                    level: 'tertiary',
                    startIconName: 'Download',
                    isIconOnly: true,
                    onClick: action(() => {
                        const { firstVendorQuestionnaire } =
                            sharedVendorsQuestionnairesController;
                        const { downloadQuestionnaire } =
                            sharedVendorsProfileReportsAndDocumentsMutationController;

                        downloadQuestionnaire(
                            firstVendorQuestionnaire?.responseId || 0,
                        );
                    }),
                },
            },
        ],
        id: `${SECURITY_REVIEW_QUESTIONNAIRE_HEADER_KEY}-actions-stack`,
    },
];
