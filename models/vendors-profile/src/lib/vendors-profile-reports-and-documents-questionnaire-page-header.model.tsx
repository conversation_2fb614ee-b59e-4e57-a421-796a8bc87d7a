import {
    sharedVendorsDetails<PERSON>ontroller,
    sharedVendorsQuestionnairesController,
} from '@controllers/vendors';
import { ActionStack } from '@cosmos/components/action-stack';
import { Banner } from '@cosmos/components/banner';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import { dimension3x } from '@cosmos/constants/tokens';
import { makeAutoObservable } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { AppLink } from '@ui/app-link';
import { buildKeyValuePairs } from './config/vendor-security-review-questionnaire-header.config';
import { getSecurityReviewQuestionnaireHeaderActionValues } from './constants/vendor-security-review-questionnaire.constants';

const VENDOR_PROFILE_REPORTS_AND_DOCUMENTS_QUESTIONNAIRES_SUBMISSION_PAGE_ID = `vendors-profile-reports-and-documents-questionnaires-page`;

export class VendorsProfileReportsAndDocumentsQuestionnairePageHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    pageId =
        VENDOR_PROFILE_REPORTS_AND_DOCUMENTS_QUESTIONNAIRES_SUBMISSION_PAGE_ID;

    get title(): string {
        return (
            sharedVendorsQuestionnairesController.firstVendorQuestionnaire
                ?.title || 'Untitled questionnaire'
        );
    }

    get keyValuePairs(): KeyValuePairProps[] {
        const { firstVendorQuestionnaire, firstQuestionnaireAnswersData } =
            sharedVendorsQuestionnairesController;

        return buildKeyValuePairs(
            firstVendorQuestionnaire,
            firstQuestionnaireAnswersData,
        );
    }

    get backLink(): React.JSX.Element {
        const { vendorDetails } = sharedVendorsDetailsController;
        const { currentWorkspace } = sharedWorkspacesController;

        const vendorType =
            vendorDetails?.status === 'PROSPECTIVE' ? 'prospective' : 'current';

        const backUrl = `/workspaces/${currentWorkspace?.id}/vendors/${vendorType}/${vendorDetails?.id}/reports-and-documents`;

        return (
            <AppLink
                data-id="vendors-profile-reports-and-documents-questionnaires-page-back-link"
                href={backUrl}
                label="Back to Reports and documents"
            />
        );
    }

    get actionStack(): React.JSX.Element {
        const { currentWorkspace } = sharedWorkspacesController;
        const { vendorDetails } = sharedVendorsDetailsController;

        const vendorType =
            vendorDetails?.status === 'PROSPECTIVE' ? 'prospective' : 'current';

        return (
            <ActionStack
                data-id="vendors-profile-security-review-questionnaire-page-action-stack"
                gap={dimension3x}
                stacks={getSecurityReviewQuestionnaireHeaderActionValues(
                    `/workspaces/${currentWorkspace?.id}/vendors/${vendorType}/${vendorDetails?.id}/reports-and-documents`,
                )}
            />
        );
    }

    get banner(): React.JSX.Element | undefined {
        const { firstVendorQuestionnaire } =
            sharedVendorsQuestionnairesController;

        if (
            firstVendorQuestionnaire?.isManualUpload &&
            firstVendorQuestionnaire.isCompleted
        ) {
            return (
                <Banner
                    displayMode="section"
                    title="Uploaded responses can't be viewed in Drata. Please download to view the file."
                />
            );
        }

        return undefined;
    }
}
