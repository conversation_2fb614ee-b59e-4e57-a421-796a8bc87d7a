import type { QuestionnaireAnswerDate } from '@controllers/vendors';
import type {
    KeyValuePairProps,
    Value,
} from '@cosmos/components/key-value-pair';
import type { QuestionnaireSentResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { formatDate } from '@helpers/date-time';

export const buildKeyValuePairs = (
    vendorQuestionnaire: QuestionnaireSentResponseDto | null,
    questionnaireAnswersData: QuestionnaireAnswerDate | null,
): KeyValuePairProps[] => {
    const {
        completedAt,
        isManualUpload,
        isCompleted,
        dateSent,
        recipientEmail,
    } = vendorQuestionnaire ?? {};

    const isUploaded = isManualUpload && isCompleted;

    const metadata: Value = isUploaded
        ? {
              label: t`Uploaded`,
              type: 'status',
              colorScheme: 'neutral',
          }
        : {
              label: t`Completed`,
              type: 'status',
              colorScheme: 'success',
          };

    const uploadedKVP: KeyValuePairProps[] = [
        {
            id: 'vendor-security-review-questionnaire-header-status',
            'data-id': 'vendor-security-review-questionnaire-header-status-id',
            label: t`Status`,
            value: metadata,
            type: 'TAG',
        },
        {
            id: 'vendor-security-review-questionnaire-header-email',
            'data-id': 'vendor-security-review-questionnaire-header-email-id',
            label: t`Email`,
            value: recipientEmail,
            type: 'TEXT',
        },
        {
            id: 'vendor-security-review-questionnaire-header-sent-upload-date',
            'data-id':
                'vendor-security-review-questionnaire-header-sent-upload-date-id',
            label: isUploaded ? t`Uploaded date` : t`Sent date`,
            value: formatDate('table', dateSent),
            type: 'TEXT',
        },
    ];

    if (!isUploaded) {
        return [
            ...uploadedKVP,
            {
                id: 'vendor-security-review-questionnaire-header-completion-date',
                'data-id':
                    'vendor-security-review-questionnaire-header-completion-date-id',
                label: t`Completion date`,
                value: completedAt ? formatDate('table', completedAt) : '-',
                type: 'TEXT',
            },
            {
                id: 'vendor-security-review-questionnaire-questions-answered',
                'data-id':
                    'vendor-security-review-questionnaire-questions-answered-id',
                label: t`Questions answered`,
                value:
                    questionnaireAnswersData &&
                    `${questionnaireAnswersData.answered} of ${questionnaireAnswersData.total}`,
                type: 'TEXT',
            },
        ];
    }

    return uploadedKVP;
};
