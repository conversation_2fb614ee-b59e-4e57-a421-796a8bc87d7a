import type React from 'react';
import {
    sharedVendorsDetailsController,
    sharedVendorsQuestionnairesController,
    sharedVendorsSecurityReviewDetailsController,
} from '@controllers/vendors';
import { ActionStack } from '@cosmos/components/action-stack';
import { Banner } from '@cosmos/components/banner';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import { Loader } from '@cosmos/components/loader';
import { dimension3x } from '@cosmos/constants/tokens';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { AppLink } from '@ui/app-link';
import { buildKeyValuePairs } from './config/vendor-security-review-questionnaire-header.config';
import { getSecurityReviewQuestionnaireHeaderActionValues } from './constants/vendor-security-review-questionnaire.constants';

export class VendorsSecurityReviewQuestionnairePageHeaderModel {
    constructor(vendorType: 'current' | 'prospective') {
        this.vendorType = vendorType;
        makeAutoObservable(this);
    }

    pageId = 'vendors-profile-security-review-questionnaire-page-header';
    vendorType: 'current' | 'prospective';

    get title(): string {
        return (
            sharedVendorsQuestionnairesController.firstVendorQuestionnaire
                ?.title || t`Untitled questionnaire`
        );
    }

    get keyValuePairs(): KeyValuePairProps[] {
        const { firstVendorQuestionnaire, firstQuestionnaireAnswersData } =
            sharedVendorsQuestionnairesController;

        return buildKeyValuePairs(
            firstVendorQuestionnaire,
            firstQuestionnaireAnswersData,
        );
    }

    get backLink(): React.JSX.Element {
        const { vendorDetails, isLoading: isVendorDetailsLoading } =
            sharedVendorsDetailsController;
        const { currentWorkspace } = sharedWorkspacesController;

        const {
            securityReviewDetails,
            isLoading: isSecurityReviewDetailsLoading,
        } = sharedVendorsSecurityReviewDetailsController;

        if (isVendorDetailsLoading || isSecurityReviewDetailsLoading) {
            return <Loader isSpinnerOnly size="sm" label={t`Loading...`} />;
        }

        const backLinkTitle =
            securityReviewDetails?.title ?? t`Security review`;

        return (
            <AppLink
                data-id="vendors-security-review-page-back-link"
                href={`/workspaces/${currentWorkspace?.id}/vendors/${this.vendorType}/${vendorDetails?.id}/security-reviews/${securityReviewDetails?.id}`}
                label={t`Back to ${backLinkTitle}`}
            />
        );
    }

    get actionStack(): React.JSX.Element {
        const { currentWorkspace } = sharedWorkspacesController;
        const { vendorDetails } = sharedVendorsDetailsController;

        const { securityReviewDetails } =
            sharedVendorsSecurityReviewDetailsController;

        return (
            <ActionStack
                data-id="vendors-security-review-questionnaire-page-action-stack"
                gap={dimension3x}
                stacks={getSecurityReviewQuestionnaireHeaderActionValues(
                    `/workspaces/${currentWorkspace?.id}/vendors/${this.vendorType}/${vendorDetails?.id}/security-reviews/${securityReviewDetails?.id}`,
                )}
            />
        );
    }

    get banner(): React.JSX.Element | undefined {
        const { firstVendorQuestionnaire } =
            sharedVendorsQuestionnairesController;

        if (
            firstVendorQuestionnaire?.isManualUpload &&
            firstVendorQuestionnaire.isCompleted
        ) {
            return (
                <Banner
                    displayMode="section"
                    title={t`Uploaded responses can't be viewed in Drata. Please download to view the file.`}
                />
            );
        }

        return undefined;
    }
}
