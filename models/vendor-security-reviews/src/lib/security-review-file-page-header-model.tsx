import { getVendorSecurityReviewLabelByType } from '@components/vendors-security-reviews';
import { sharedVendorsSecurityReviewFileController } from '@controllers/vendors';
import { ActionStack } from '@cosmos/components/action-stack';
import { dimension3x } from '@cosmos/constants/tokens';
import { makeAutoObservable } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { AppLink } from '@ui/app-link';
import {
    getVendorsProfileSecurityReviewFileActions,
    VENDORS_PROFILE_SECURITY_REVIEW_FILE_PAGE_HEADER_KEY,
} from '@views/vendors-profile-security-review-files';

export class SecurityReviewFilePageHeaderModel {
    constructor(vendorType: 'current' | 'prospective') {
        this.vendorType = vendorType;
        makeAutoObservable(this);
    }

    pageId = 'vendors-security-review-files-page';
    securityReviewDocumentId: number | undefined = undefined;
    vendorType: 'current' | 'prospective';

    get title(): string {
        const { securityReviewDocument } =
            sharedVendorsSecurityReviewFileController;

        this.securityReviewDocumentId = securityReviewDocument?.documentId;

        return securityReviewDocument?.name ?? '';
    }

    get backLink(): React.JSX.Element {
        const { vendorId, securityReviewId, securityReviewDocument } =
            sharedVendorsSecurityReviewFileController;
        const { currentWorkspace } = sharedWorkspacesController;

        return (
            <AppLink
                data-id="vendors-security-review-files-page-back-link"
                href={`/workspaces/${currentWorkspace?.id}/vendors/${this.vendorType}/${vendorId}/security-reviews/${securityReviewId}`}
                label={`Back to ${getVendorSecurityReviewLabelByType({ type: 'SECURITY', requestedAt: securityReviewDocument?.vendorDocument?.createdAt ?? '' })}`}
            />
        );
    }

    actionStack = (
        <ActionStack
            data-id="vendors-security-review-files-page-action-stack"
            gap={dimension3x}
            stacks={[
                {
                    actions: getVendorsProfileSecurityReviewFileActions(),
                    id: `${VENDORS_PROFILE_SECURITY_REVIEW_FILE_PAGE_HEADER_KEY}-actions-stack`,
                },
            ]}
        />
    );
}
