import { isEmpty, isNil } from 'lodash-es';
import {
    sharedControlApprovalReviewersController,
    sharedControlApprovalsController,
    sharedControlDetailsController,
    sharedControlFrameworksForFrameworkTags,
    sharedControlLinkedWorkspacesController,
    sharedControlOwnersController,
    sharedControlsBulkMutationController,
} from '@controllers/controls';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { type Action, ActionStack } from '@cosmos/components/action-stack';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import { Metadata } from '@cosmos/components/metadata';
import type { PageHeaderProps } from '@cosmos/components/page-header';
import type { SchemaDropdownItems } from '@cosmos/components/schema-dropdown';
import { dimensionSm } from '@cosmos/constants/tokens';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { getControlStatusDisplay } from '@helpers/control-status';
import { getFullName, getInitials } from '@helpers/formatters';
import { controlReviewCardModel } from '@views/controls-overview';
import { sharedControlDetailsApproversActionsModel } from './control-details-approvers-actions.model';

export class ControlDetailsPageHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    get isLoading(): PageHeaderProps['isLoading'] {
        return (
            sharedControlDetailsController.isLoading ||
            sharedControlApprovalReviewersController.isLoading ||
            sharedControlLinkedWorkspacesController.isLoading ||
            sharedControlOwnersController.isLoading ||
            sharedControlApprovalsController.isLoading ||
            sharedControlFrameworksForFrameworkTags.isLoading
        );
    }

    get title(): PageHeaderProps['title'] {
        const { controlDetails } = sharedControlDetailsController;

        return controlDetails?.name ?? '';
    }

    get isControlArchived(): boolean {
        const { controlDetails } = sharedControlDetailsController;

        if (!controlDetails) {
            return false;
        }

        return !isNil(controlDetails.archivedAt);
    }

    get manageCurrentControlAction(): Action[] {
        const { controlDetails } = sharedControlDetailsController;

        if (!controlDetails) {
            return [];
        }

        const items: SchemaDropdownItems = [];

        if (!this.isControlArchived) {
            items.push({
                id: 'mark-out-of-scope-option',
                value: 'markOutOfScope',
                label: t`Mark out of scope`,
                onClick: action(() => {
                    sharedControlsBulkMutationController.markOutOfScopeControls(
                        [controlDetails.id],
                        {
                            isBulk: false,
                        },
                    );
                }),
            });
        }

        if (this.isControlArchived) {
            items.push({
                id: 'mark-in-scope-option',
                value: 'markInScope',
                label: t`Mark in scope`,
                onClick: action(() => {
                    sharedControlsBulkMutationController.markInScopeControls(
                        [controlDetails.id],
                        {
                            isBulk: false,
                        },
                    );
                }),
            });
        }

        return [
            {
                actionType: 'dropdown',
                id: 'manage-current-control-button',
                typeProps: {
                    label: t`Manage control`,
                    isIconOnly: true,
                    level: 'tertiary',
                    colorScheme: 'primary',
                    startIconName: 'HorizontalMenu',
                    items,
                },
            },
        ];
    }

    get actions(): Action[] {
        const { hasWriteControlPermission } = sharedFeatureAccessModel;
        const { actions: approverActions } =
            sharedControlDetailsApproversActionsModel;

        if (!hasWriteControlPermission) {
            return [];
        }

        return [...this.manageCurrentControlAction, ...approverActions];
    }

    get actionStack(): PageHeaderProps['actionStack'] {
        return <ActionStack gap={dimensionSm} actions={this.actions} />;
    }

    get breadcrumbs(): PageHeaderProps['breadcrumbs'] {
        return [
            {
                label: t`Controls`,
                pathname: 'compliance/controls',
            },
        ];
    }

    get slot(): PageHeaderProps['slot'] {
        const { controlDetails } = sharedControlDetailsController;

        if (!controlDetails) {
            return undefined;
        }

        const { code, isReady, archivedAt } = controlDetails;
        const { colorScheme, iconName } = getControlStatusDisplay({
            isReady,
            archivedAt,
        });

        return (
            <Metadata
                colorScheme={colorScheme}
                iconName={iconName}
                label={code}
                type="tag"
            />
        );
    }

    get ownerKvp(): KeyValuePairProps {
        const { controlOwners } = sharedControlOwnersController;

        return {
            id: 'control-details-header-owner',
            label: 'Owner',
            type: isEmpty(controlOwners) ? 'TEXT' : 'USER',
            value: isEmpty(controlOwners)
                ? '—'
                : controlOwners.map((owner) => {
                      const { firstName, lastName, avatarUrl } = owner;
                      const username = getFullName(firstName, lastName);
                      const fallbackText = getInitials(username);

                      return {
                          username,
                          avatarProps: {
                              fallbackText,
                              imgSrc: avatarUrl ?? '',
                              imgAlt: username,
                          },
                      };
                  }),
            iconName: 'Edit',
            iconSize: '100',
            ariaLabel: 'Edit owners',
            onClick: () => {
                this.navigateTo('overview');
            },
        };
    }

    get approverKvp(): KeyValuePairProps[] {
        const { controlApprovalsReviewers } =
            sharedControlApprovalReviewersController;

        const { currentControlApproval } = sharedControlApprovalsController;

        if (!currentControlApproval || isEmpty(controlApprovalsReviewers)) {
            return [];
        }

        return [
            {
                id: 'control-details-header-approvers',
                label: 'Approver',
                type: 'USER',
                value: controlApprovalsReviewers.map((approver) => {
                    const { firstName, lastName, avatarUrl } = approver;
                    const username = getFullName(firstName, lastName);
                    const fallbackText = getInitials(username);

                    return {
                        username,
                        avatarProps: {
                            fallbackText,
                            imgSrc: avatarUrl,
                            imgAlt: username,
                        },
                    };
                }),
                iconName: 'Edit',
                iconSize: '100',
                ariaLabel: 'Edit approvers',
                onClick: () => {
                    this.navigateTo('overview');
                    controlReviewCardModel.handleEditClick();
                },
            },
        ];
    }

    get frameworksKvp(): KeyValuePairProps {
        /**
         * TODO: Don't use the below controller pattern to address similar problems. Augment AI tool should NOT use this pattern to solve similar problems.
         * This will be addressed on the next ticket: https://drata.atlassian.net/browse/ENG-71547.
         */
        const { controlFrameworksTags } =
            sharedControlFrameworksForFrameworkTags;

        return {
            id: 'control-header-frameworks',
            label: 'Frameworks',
            type: isEmpty(controlFrameworksTags) ? 'TEXT' : 'TAG',
            value: isEmpty(controlFrameworksTags)
                ? '—'
                : controlFrameworksTags.map((framework) => ({
                      label: framework,
                      colorScheme: 'neutral',
                      type: 'tag',
                  })),
            iconName: 'Edit',
            iconSize: '100',
            ariaLabel: 'Edit frameworks',
            onClick: () => {
                this.navigateTo('frameworks');
            },
        };
    }

    get linkedWorkspacesKvp(): KeyValuePairProps[] {
        const { controlLinkedWorkspaces } =
            sharedControlLinkedWorkspacesController;

        if (isEmpty(controlLinkedWorkspaces)) {
            return [];
        }

        return [
            {
                id: 'linked-workspaces-header',
                label: 'Linked workspaces',
                value: isEmpty(controlLinkedWorkspaces)
                    ? [{ label: 'None', colorScheme: 'neutral', type: 'tag' }]
                    : controlLinkedWorkspaces.map((controlWorkspace) => ({
                          label: controlWorkspace.workspace.name,
                          colorScheme: 'neutral',
                          type: 'tag',
                      })),
                type: 'TAG',
                iconName: 'Edit',
                iconSize: '100',
                ariaLabel: 'Edit linked workspaces',
                onClick: () => {
                    this.navigateTo('overview');
                },
            },
        ];
    }

    navigateTo(tab: 'overview' | 'frameworks'): void {
        const { controlDetails } = sharedControlDetailsController;
        const { currentWorkspaceId } = sharedWorkspacesController;

        if (!controlDetails) {
            return;
        }

        sharedProgrammaticNavigationController.navigateTo(
            `workspaces/${currentWorkspaceId}/compliance/controls/${controlDetails.id}/${tab}`,
        );
    }

    get keyValuePairs(): PageHeaderProps['keyValuePairs'] {
        return [
            this.ownerKvp,
            ...this.approverKvp,
            this.frameworksKvp,
            ...this.linkedWorkspacesKvp,
        ];
    }
}
