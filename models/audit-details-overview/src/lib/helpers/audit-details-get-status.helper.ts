import type { ButtonColorScheme } from '@cosmos/components/button';
import type { CustomerRequestDetailsWithFrameworkResponseDto } from '@globals/api-sdk/types';
import { getStatusMap } from '@models/request-details-page-header';

export function getStatusLabel(
    status: CustomerRequestDetailsWithFrameworkResponseDto['status'],
): string {
    const statusMap = getStatusMap();

    switch (status) {
        case 'OUTSTANDING': {
            return statusMap.OUTSTANDING.label; // "New"
        }
        case 'ACCEPTED': {
            return statusMap.ACCEPTED.label; // "Completed"
        }
        case 'IN_REVIEW':
        default: {
            return statusMap.IN_REVIEW.label; // "Prepared"
        }
    }
}

export function getStatusColor(
    status: CustomerRequestDetailsWithFrameworkResponseDto['status'],
): ButtonColorScheme {
    switch (status) {
        case 'OUTSTANDING': {
            return 'neutral';
        }
        case 'ACCEPTED': {
            return 'primary';
        }
        case 'IN_REVIEW':
        default: {
            return 'neutral';
        }
    }
}
