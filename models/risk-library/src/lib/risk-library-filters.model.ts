import type { ComponentProps } from 'react';
import { sharedRiskLibraryCategoriesController } from '@controllers/risk';
import type { Datatable } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';

export type RiskLibraryFilterProps = ComponentProps<
    typeof Datatable
>['filterProps'];

class RiskLibraryFiltersModel {
    constructor() {
        makeAutoObservable(this);
    }

    get filters(): RiskLibraryFilterProps {
        const { categories } = sharedRiskLibraryCategoriesController;

        return {
            clearAllButtonLabel: t`Reset`,
            filters: [
                {
                    isMultiSelect: true,
                    filterType: 'combobox',
                    id: 'categories',
                    label: t`Categories`,
                    placeholder: t`Search`,
                    options: categories.map((category) => ({
                        id: category.id.toString(),
                        label: category.name,
                        value: category.id.toString(),
                    })),
                },
            ],
        };
    }
}

export const sharedRiskLibraryFiltersModel = new RiskLibraryFiltersModel();
