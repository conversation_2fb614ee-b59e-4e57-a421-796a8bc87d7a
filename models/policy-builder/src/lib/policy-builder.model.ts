import { isEmpty } from 'lodash-es';
import { sharedConnectionsController } from '@controllers/connections';
import {
    type PolicyVersionStatus,
    sharedPolicyBuilderController,
} from '@controllers/policy-builder';
import type { FeedbackProps } from '@cosmos/components/feedback';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import type { PolicyWithReplaceResponseDto } from '@globals/api-sdk/types';
import { sharedCurrentUserController } from '@globals/current-user';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { formatDate, isDateWithinNextMonths } from '@helpers/date-time';
import { formatAssignedToText } from './helpers/format-assigned-to-text.helper';

class PolicyBuilderModel {
    /**
     * ===== EDITOR STATE =====.
     * CKEditor state management.
     */
    _isCkEditorInEditMode = false;

    constructor() {
        makeAutoObservable(this);
    }

    /**
     * ===== PRIVATE PROPERTIES =====.
     * Internal properties used only within this model.
     */

    private get policy(): PolicyWithReplaceResponseDto | null {
        return sharedPolicyBuilderController.policy;
    }

    private get currentVersion() {
        return sharedPolicyBuilderController.currentVersion;
    }

    private get renewalDate(): string | null {
        return this.currentVersion?.renewalDate ?? null;
    }

    private get hasExpiredRenewalDate(): boolean {
        return this.currentVersion?.hasExpiredRenewalDate ?? false;
    }

    private get assignedTo(): string {
        return this.policy?.assignedTo ?? '';
    }

    private get groups() {
        return this.policy?.groups ?? [];
    }

    private get policyGracePeriodSLAs() {
        return this.policy?.policyGracePeriodSLAs ?? [];
    }

    private get policyWeekTimeFrameSLAs() {
        return this.policy?.policyWeekTimeFrameSLAs ?? [];
    }

    private get policyP3MatrixSLAs() {
        return this.policy?.policyP3MatrixSLAs ?? [];
    }

    private get linkedFrameworks() {
        return sharedPolicyBuilderController.policyFrameworksAssociated;
    }

    private get hasLinkedFrameworks(): boolean {
        return !isEmpty(this.linkedFrameworks);
    }

    private get replacedPolicies() {
        return this.policy?.replacedPolicies ?? [];
    }

    /**
     * ===== PUBLIC PROPERTIES =====.
     * Properties exposed to external components.
     */

    get policyName(): string {
        return this.policy?.name ?? '';
    }

    get policyDescription(): string {
        return this.policy?.currentDescription ?? '';
    }

    get assignedToText(): string {
        return formatAssignedToText(this.assignedTo, this.groups);
    }

    get shouldDisplaySLA(): boolean {
        return (
            !isEmpty(this.policyGracePeriodSLAs) ||
            !isEmpty(this.policyWeekTimeFrameSLAs) ||
            !isEmpty(this.policyP3MatrixSLAs)
        );
    }

    /**
     * ===== POLICY TYPE AND STATE PROPERTIES =====.
     * Business logic properties for determining policy state and type.
     */

    get isNewDrataTemplatePolicy(): boolean {
        return (
            this.policy?.templateId !== null &&
            this.policy?.currentPublishedPolicyVersion === null
        );
    }

    get isAuthoredPolicy(): boolean {
        return this.currentVersion?.type === 'BUILDER';
    }

    get isUploadedPolicy(): boolean {
        return this.currentVersion?.type === 'UPLOADED';
    }

    get hasNotionOrConfluenceConnection(): boolean {
        const externalPolicyConnections =
            sharedConnectionsController.loadConfiguredConnectionsByProviderType(
                'EXTERNAL_POLICY',
            );

        return externalPolicyConnections.some(
            (connection) =>
                (connection.clientType === 'CONFLUENCE' ||
                    connection.clientType === 'NOTION') &&
                connection.state === 'ACTIVE',
        );
    }

    get renewalDateDisplay(): {
        value: KeyValuePairProps['value'];
        feedbackProps?: FeedbackProps;
    } {
        if (!this.renewalDate) {
            return {
                value: '-',
            };
        }
        const shouldShowFeedback = isDateWithinNextMonths(2, this.renewalDate);

        if (shouldShowFeedback) {
            const title = formatDate('sentence', this.renewalDate);

            let severity: FeedbackProps['severity'];
            let description: string;

            if (this.hasExpiredRenewalDate) {
                severity = 'critical';
                description = t`This policy is past its renewal date. Update the renewal date and if necessary renew the policy to send for approval and/or acknowledgement by personnel.`;
            } else {
                severity = 'warning';
                description = t`The renewal date for this policy is coming up soon. Update the renewal date and if necessary renew the policy to send for approval and/or acknowledgement by personnel.`;
            }

            return {
                value: null,
                feedbackProps: {
                    severity,
                    title,
                    description,
                },
            };
        }

        return {
            value: formatDate('sentence', this.renewalDate),
        };
    }

    get frameworksDisplay(): {
        type: KeyValuePairProps['type'];
        value: KeyValuePairProps['value'];
    } {
        if (this.hasLinkedFrameworks) {
            return {
                type: 'TAG',
                value: this.linkedFrameworks.map((framework) => ({
                    label: framework.pill,
                    colorScheme: 'primary' as const,
                })),
            };
        }

        return {
            type: 'TEXT',
            value: '-',
        };
    }

    get disclaimer(): string {
        return this.policy?.disclaimer ?? '';
    }

    get replacedPoliciesCount(): string {
        return this.replacedPolicies.length.toString();
    }

    get isDraft(): boolean {
        return this.currentVersion?.policyVersionStatus === 'DRAFT';
    }

    get isApproved(): boolean {
        return this.currentVersion?.policyVersionStatus === 'APPROVED';
    }

    get isPublished(): boolean {
        return this.currentVersion?.policyVersionStatus === 'PUBLISHED';
    }

    get hasPublishedVersion(): boolean {
        return Boolean(this.publishedVersionId);
    }

    get hasDraftVersion(): boolean {
        return this.draftVersionId !== this.publishedVersionId;
    }

    get publishedVersionId(): number | null {
        return this.policy?.currentPublishedPolicyVersion?.id ?? null;
    }

    get draftVersionId(): number | null {
        return this.policy?.latestPolicyVersion?.id ?? null;
    }

    get latestPolicyVersion() {
        return this.policy?.latestPolicyVersion ?? null;
    }

    get policyId(): number {
        return this.policy?.id ?? 0;
    }

    get currentStatus(): PolicyVersionStatus | undefined {
        return this.currentVersion?.policyVersionStatus;
    }

    get currentVersionId(): number | null {
        return this.currentVersion?.id ?? null;
    }

    get hasTemplate(): boolean {
        return Boolean(this.policy?.templateId);
    }

    get currentMajorVersion(): number {
        return this.policy?.currentPublishedPolicyVersion?.version ?? 1;
    }

    get currentMinorVersion(): number {
        return this.policy?.currentPublishedPolicyVersion?.subVersion ?? 0;
    }

    get nextMajorVersion(): number {
        return this.currentMajorVersion + 1;
    }

    get nextMinorVersion(): number {
        return this.currentMinorVersion + 1;
    }

    get htmlContent(): string {
        return this.currentVersion?.html ?? '';
    }

    get hasHtmlContent(): boolean {
        return !isEmpty(this.htmlContent);
    }

    /**
     * ===== APPROVAL DATA =====.
     * Approval-related properties.
     */

    get latestApprovalId(): number | null {
        return sharedPolicyBuilderController.latestApprovalId;
    }

    get currentUserReviewId(): number | null {
        return sharedPolicyBuilderController.currentUserReviewId;
    }

    /**
     * Check if there are changes requested in the approval process.
     */
    get hasChangesRequested(): boolean {
        return sharedPolicyBuilderController.hasChangesRequested;
    }

    /**
     * Get the name of the user who requested changes.
     */
    get changesRequestedBy(): string | null {
        return sharedPolicyBuilderController.changesRequestedBy;
    }

    get isCurrentUserApprover(): boolean {
        return sharedPolicyBuilderController.isCurrentUserApprover;
    }

    /**
     * ===== PUBLISHED STATUS BUSINESS LOGIC =====.
     * Logic for published status actions.
     */
    get isTheLastPolicyVersionInDraftStatus(): boolean {
        // Check if current version is published and there's a newer draft version
        const isPublished = this.currentStatus === 'PUBLISHED';
        const hasNewerVersion =
            this.policy?.latestPolicyVersion?.id !== this.currentVersion?.id;

        return Boolean(isPublished && hasNewerVersion);
    }

    get showPublishButtonActions(): boolean {
        return this.isTheLastPolicyVersionInDraftStatus;
    }

    get latestVersionId(): number | null {
        return this.policy?.latestPolicyVersion?.id ?? null;
    }

    get hasExternalConnection(): boolean {
        // Check if policy has external connections (Notion, Confluence, etc.)
        return this.hasNotionOrConfluenceConnection;
    }

    get isExternalPolicy(): boolean {
        // Check if this is an external policy type
        return this.currentVersion?.type === 'EXTERNAL' || false;
    }

    get isPolicyVersionOwner(): boolean {
        // Check if current user is the policy version owner
        const { currentVersion } = sharedPolicyBuilderController;

        if (!currentVersion?.owner) {
            return false;
        }

        const currentUserId = sharedCurrentUserController.entryId;

        return currentVersion.owner.entryId === currentUserId;
    }

    /**
     * ===== EDITOR STATE PROPERTIES =====.
     * CKEditor state management.
     */
    get isCkEditorInEditMode(): boolean {
        return this._isCkEditorInEditMode;
    }

    setIsCkEditorInEditMode(value: boolean): void {
        this._isCkEditorInEditMode = value;
    }
}

export const sharedPolicyBuilderModel = new PolicyBuilderModel();
