import { z } from 'zod';
import { sharedPersonnelGroupController } from '@controllers/personnel-group-controller';
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import type { FormSchema } from '@ui/forms';
import { PoliciesDetailsStepRenewalDateFieldComponent } from '@views/policies-add';
import {
    getAllEmployeesLabel,
    getApplicablePersonnelHelpText,
    getApplicablePersonnelLabel,
    getAtLeastOneGroupError,
    getLoadingGroupsLabel,
    getNoPersonnelGroupsFoundLabel,
    getNotApplicableLabel,
    getNotifyNewMembersLabel,
    getPolicyDescriptionLabel,
    getPolicyDescriptionMaxLengthError,
    getPolicyDescriptionRequiredError,
    getRemoveAllSelectedPersonnelGroupsLabel,
    getRenewalDateLabel,
    getRenewalIntervalLabel,
    getRenewalScheduleOptions,
    getSearchGroupsPlaceholder,
    getSelectPersonnelError,
    getSelectRenewalDateError,
    getSpecificGroupsFieldLabel,
    getSpecificGroupsHelpText,
    getSpecificGroupsLabel,
} from './policy-form.constants';

export class PolicyBuilderDetailsFormModel {
    constructor() {
        makeAutoObservable(this);
    }

    /**
     * Handler for fetching personnel group options with search support.
     * Reused from PolicyFormModel.
     */
    handleFetchPersonnelGroupOptions = ({
        search,
        increasePage,
    }: {
        search?: string;
        increasePage?: boolean;
    }): void => {
        if (increasePage) {
            sharedPersonnelGroupController.loadNextPage({ search });

            return;
        }

        sharedPersonnelGroupController.loadPersonnelGroups({
            q: search?.trim(),
        });
    };

    /**
     * Group options for the personnel groups combobox.
     * Reused from PolicyFormModel.
     */
    get groupOptions(): ListBoxItemData[] {
        return sharedPersonnelGroupController.personnelGroups.map((group) => {
            const memberCount = String(group.membersCount);

            return {
                id: group.id.toString(),
                label: group.name,
                value: group.id.toString(),
                description: t`${memberCount} members`,
            };
        });
    }

    /**
     * Get current policy data from the policy builder model.
     */
    get currentPolicyData(): {
        description: string;
        renewalDate: string | null;
        assignedTo: 'ALL' | 'GROUP' | 'NONE';
        groups: { id: number; name: string }[];
        notifyGroups: boolean;
    } {
        const { policy, currentVersion } = sharedPolicyBuilderController;

        return {
            description: policy?.currentDescription ?? '',
            renewalDate: currentVersion?.renewalDate ?? null,
            assignedTo: policy?.assignedTo ?? 'ALL',
            notifyGroups: policy?.notifyGroups ?? false,
            groups: policy?.groups ?? [],
        };
    }

    /**
     * Form schema for editing policy details.
     */
    get editDetailsSchema(): FormSchema {
        const { isPersonnelGroupsEnabled, isInfiniteLoading, hasNextPage } =
            sharedPersonnelGroupController;

        const currentData = this.currentPolicyData;

        return {
            description: {
                type: 'textarea',
                label: getPolicyDescriptionLabel(),
                initialValue: currentData.description,
                validator: z
                    .string()
                    .min(1, getPolicyDescriptionRequiredError())
                    .max(30000, getPolicyDescriptionMaxLengthError())
                    .trim(),
            },
            renewalDate: {
                type: 'custom',
                label: getRenewalDateLabel(),
                render: PoliciesDetailsStepRenewalDateFieldComponent,
                customType: 'object',
                fields: {
                    renewalFrequency: {
                        type: 'select',
                        label: getRenewalIntervalLabel(),
                        shouldHideLabel: true,
                        options: getRenewalScheduleOptions(),
                        initialValue: getRenewalScheduleOptions()[0], // Default to 1 Month
                        validator: z.object({
                            id: z.string(),
                            label: z.string(),
                            value: z.string(),
                        }),
                    },
                    renewalDate: {
                        type: 'date',
                        label: getRenewalDateLabel(),
                        shouldHideLabel: true,
                        isMulti: false,
                        initialValue: (currentData.renewalDate ??
                            new Date()
                                .toISOString()
                                .split(
                                    'T',
                                )[0]) as `${number}${number}${number}${number}-${number}${number}-${number}${number}`,
                        getIsDateUnavailable: (date) => {
                            return (
                                new Date(date).toISOString().split('T')[0] <
                                new Date().toISOString().split('T')[0]
                            );
                        },
                        validator: z
                            .string()
                            .min(1, getSelectRenewalDateError()),
                    },
                },
            },
            assignedTo: {
                type: 'radioGroup',
                label: getApplicablePersonnelLabel(),
                helpText: getApplicablePersonnelHelpText(),
                cosmosUseWithCaution_forceOptionOrientation: 'vertical',
                initialValue: currentData.assignedTo,
                options: [
                    {
                        label: getAllEmployeesLabel(),
                        value: 'ALL',
                    },
                    ...(isPersonnelGroupsEnabled
                        ? [
                              {
                                  label: getSpecificGroupsLabel(),
                                  value: 'GROUP',
                              },
                          ]
                        : []),
                    {
                        label: getNotApplicableLabel(),
                        value: 'NONE',
                    },
                ],
                validator: z.enum(['ALL', 'GROUP', 'NONE'], {
                    errorMap: () => ({
                        message: getSelectPersonnelError(),
                    }),
                }),
            },
            notifyNewMembers: {
                type: 'checkbox',
                label: getNotifyNewMembersLabel(),
                value: 'notify',
                initialValue: currentData.notifyGroups,
                shownIf: {
                    fieldName: 'assignedTo',
                    operator: 'equals',
                    value: 'GROUP',
                },
            },
            selectedGroups: {
                type: 'combobox',
                label: getSpecificGroupsFieldLabel(),
                helpText: getSpecificGroupsHelpText(),
                isMultiSelect: true,
                initialValue: currentData.groups.map((group) => ({
                    id: group.id.toString(),
                    label: group.name,
                    value: group.id.toString(),
                })),
                placeholder: getSearchGroupsPlaceholder(),
                isOptional: false,
                options: this.groupOptions,
                isLoading: isInfiniteLoading,
                loaderLabel: getLoadingGroupsLabel(),
                onFetchOptions: this.handleFetchPersonnelGroupOptions,
                hasMore: hasNextPage,
                getSearchEmptyState: getNoPersonnelGroupsFoundLabel,
                removeAllSelectedItemsLabel:
                    getRemoveAllSelectedPersonnelGroupsLabel(),
                shownIf: {
                    fieldName: 'assignedTo',
                    operator: 'equals',
                    value: 'GROUP',
                },
                validator: z
                    .array(
                        z.object({
                            id: z.string(),
                            label: z.string(),
                            value: z.string(),
                        }),
                    )
                    .min(1, getAtLeastOneGroupError())
                    .optional(),
            },
        };
    }
}

export const sharedPolicyBuilderDetailsFormModel =
    new PolicyBuilderDetailsFormModel();
