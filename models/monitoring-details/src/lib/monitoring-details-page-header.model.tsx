import { head, isEmpty, isNil } from 'lodash-es';
import { getCategoryLabel } from '@components/monitoring';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { ActionStack } from '@cosmos/components/action-stack';
import { Banner } from '@cosmos/components/banner';
import { Button, type ButtonLevel } from '@cosmos/components/button';
import type {
    ContentType,
    KeyValuePairProps,
} from '@cosmos/components/key-value-pair';
import {
    type ColorScheme,
    Metadata,
    type MetadataProps,
} from '@cosmos/components/metadata';
import { dimensionSm } from '@cosmos/constants/tokens';
import type { MonitorV2ControlTestInstanceOverviewResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { formatDate } from '@helpers/date-time';
import { CheckResultStatus, CheckStatus } from '@helpers/evidence';

const getStatusColorScheme = (status: string): ColorScheme => {
    if (status === 'PASSED') {
        return 'success';
    }
    if (status === 'FAILED') {
        return 'critical';
    }

    return 'neutral';
};

const mapContentTypeValue = (type: string): ContentType => {
    switch (type) {
        case 'TEXT': {
            return 'TEXT';
        }
        case 'BADGE': {
            return 'BADGE';
        }
        case 'USER': {
            return 'USER';
        }
        case 'TAG': {
            return 'TAG';
        }
        case 'EXTERNAL-LINK': {
            return 'EXTERNAL-LINK';
        }
        case 'REACT_NODE': {
            return 'REACT_NODE';
        }
        default: {
            throw new Error(`Unknown content type: ${type}`);
        }
    }
};

const getMonitorDetailsHeaderSlot = ({
    source,
    isNew,
    isDraft,
    isError,
}: {
    source:
        | MonitorV2ControlTestInstanceOverviewResponseDto['source']
        | undefined;
    isNew: boolean;
    isDraft: boolean;
    isError: boolean;
}) => {
    const metadataConfig: {
        condition: boolean;
        config: Pick<MetadataProps, 'colorScheme' | 'label'>;
    }[] = [
        {
            condition: source === 'ACORN',
            config: {
                colorScheme: 'primary',
                label: 'Codebase',
            },
        },
        {
            condition: isNew,
            config: {
                colorScheme: 'primary',
                label: 'New',
            },
        },
        {
            condition: isDraft,
            config: {
                colorScheme: 'neutral',
                label: 'Draft',
            },
        },
        {
            condition: isError,
            config: {
                colorScheme: 'critical',
                label: 'Error',
            },
        },
    ];

    return metadataConfig.find(({ condition }) => condition);
};

export class MonitoringDetailsPageHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    pageId = 'monitoring-details-overview-page';

    get actionStack(): React.JSX.Element {
        const { source } = sharedMonitoringTestDetailsController;

        return (
            <ActionStack
                data-id="monitoring-details-header-action-stack"
                gap={dimensionSm}
                data-testid="MonitoringDetailsHeaderActionStack"
                actions={[
                    ...(source === 'CUSTOM'
                        ? [
                              {
                                  id: 'edit-action-button',
                                  actionType: 'button' as const,
                                  typeProps: {
                                      label: 'Edit',
                                      level: 'secondary' as ButtonLevel,
                                  },
                              },
                          ]
                        : []),
                    {
                        id: 'test-now-action-button',
                        actionType: 'button' as const,
                        typeProps: {
                            label: 'Test now',
                            level: 'secondary' as ButtonLevel,
                        },
                    },
                ]}
            />
        );
    }

    get slot(): React.ReactNode {
        const { testDetails, source } = sharedMonitoringTestDetailsController;

        if (!testDetails) {
            return undefined;
        }

        const slotDetails = getMonitorDetailsHeaderSlot({
            source,
            isNew: testDetails.isNew ?? false,
            isDraft: testDetails.draft,
            isError: testDetails.checkResultStatus === 'ERROR',
        });

        if (!slotDetails) {
            return undefined;
        }

        return (
            <Metadata
                data-testid="monitoring-header-slot-metadata"
                type="tag"
                colorScheme={slotDetails.config.colorScheme}
                label={slotDetails.config.label}
            />
        );
    }

    get banner(): React.JSX.Element | undefined {
        const { testDetails } = sharedMonitoringTestDetailsController;
        const { currentWorkspaceId } = sharedWorkspacesController;

        if (!testDetails) {
            return undefined;
        }

        const { parentTestId, drafts } = testDetails;
        const hasDrafts = drafts && !isEmpty(drafts);

        if (!parentTestId && !hasDrafts) {
            return undefined;
        }

        let headerTitle: string;
        let buttonTitle: string;
        let paramTestId: number;

        if (parentTestId) {
            headerTitle = t`There is a published version of this test`;
            buttonTitle = t`View published test`;
            paramTestId = parentTestId;
        } else {
            headerTitle = t`There is a draft version of this test`;
            buttonTitle = t`View draft test`;
            paramTestId = head(drafts)?.testId as number;
        }

        return (
            <Banner
                closeButtonAriaLabel="Close"
                data-testid="banner-linked=publish-custom-test"
                displayMode="section"
                severity="primary"
                title={headerTitle}
                body={
                    <Button
                        colorScheme="primary"
                        data-testid="view-draft-test-button"
                        label={buttonTitle}
                        level="secondary"
                        size="sm"
                        width="auto"
                        onClick={() => {
                            sharedProgrammaticNavigationController.navigateTo(
                                `workspaces/${currentWorkspaceId}/compliance/monitoring/production/${paramTestId}/overview`,
                            );
                        }}
                    />
                }
            />
        );
    }

    get title(): string {
        return sharedMonitoringTestDetailsController.testName ?? 'LOADING...';
    }

    get keyValuePairs(): KeyValuePairProps[] {
        const { testDetails } = sharedMonitoringTestDetailsController;

        if (isNil(testDetails)) {
            return [];
        }

        const {
            checkResultStatus,
            lastCheck,
            source,
            checkTypes,
            checkStatus,
        } = testDetails;

        const testResultMetadata =
            CheckResultStatus[checkResultStatus] === CheckResultStatus.READY ||
            CheckStatus[checkStatus] === CheckStatus.UNUSED
                ? {
                      value: '-',
                      type: mapContentTypeValue('TEXT'),
                  }
                : {
                      value: {
                          label: checkResultStatus,
                          colorScheme: getStatusColorScheme(checkResultStatus),
                          type: 'status' as const,
                      },
                      type: mapContentTypeValue('BADGE'),
                  };

        return [
            {
                id: 'monitoring-header-latest-test-result',
                'data-id': 'monitoring-header-status',
                label: 'Latest test result',
                ...testResultMetadata,
            },
            {
                id: 'monitoring-header-latest-test-run',
                'data-id': 'monitoring-header-latest-test-run',
                label: 'Latest test run',
                value:
                    CheckStatus[checkStatus] === CheckStatus.UNUSED
                        ? '-'
                        : formatDate('field_time', lastCheck || undefined),
                type: mapContentTypeValue('TEXT'),
            },
            {
                id: 'monitoring-header-test-lifecycle',
                'data-id': 'monitoring-header-test-lifecycle',
                label: 'Test lifecycle',
                value: source === 'DRATA' ? 'Production' : 'Custom',
                type: mapContentTypeValue('TEXT'),
            },
            {
                id: 'monitoring-header-category',
                'data-id': 'monitoring-header-category',
                label: 'Category',
                value: checkTypes[0] ? getCategoryLabel(checkTypes[0]) : '-',
                type: mapContentTypeValue('TEXT'),
            },
        ];
    }
}
