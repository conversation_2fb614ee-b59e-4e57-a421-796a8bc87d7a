import type { IconName } from '@cosmos/components/icon';
import type { ColorScheme } from '@cosmos/components/metadata';
import { getStatusMap } from '../constants/status.map.constants';

export const getStatusMetadata = (
    status: string,
): {
    label: string;
    colorScheme: ColorScheme;
    iconName: IconName;
} => {
    const statusMap = getStatusMap();

    return (
        statusMap[status] ?? {
            label: 'Unknown',
            colorScheme: 'neutral',
            iconName: 'Circle',
        }
    );
};
