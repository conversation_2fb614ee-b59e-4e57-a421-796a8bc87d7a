import { describe, expect, test } from 'vitest';
import { getStatusMap } from '../constants/status.map.constants';
import { getStatusMetadata } from './status.metadata.helper';

describe('getStatusMetadata', () => {
    test('should return correct metadata for valid status', () => {
        const validStatuses = ['OUTSTANDING', 'IN_REVIEW', 'ACCEPTED'] as const;
        const statusMap = getStatusMap();

        validStatuses.forEach((status) => {
            const result = getStatusMetadata(status);
            const expected = statusMap[status];

            expect(result).toStrictEqual(expected);
        });
    });

    test('should return fallback metadata for unknown status', () => {
        const result = getStatusMetadata('INVALID_STATUS');

        expect(result).toStrictEqual({
            label: 'Unknown',
            colorScheme: 'neutral',
            iconName: 'Circle',
        });
    });

    test('should handle empty string status', () => {
        const result = getStatusMetadata('');

        expect(result).toStrictEqual({
            label: 'Unknown',
            colorScheme: 'neutral',
            iconName: 'Circle',
        });
    });
});
