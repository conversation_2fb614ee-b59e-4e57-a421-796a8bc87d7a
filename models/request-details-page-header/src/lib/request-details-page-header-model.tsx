import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import { SchemaDropdown } from '@cosmos/components/schema-dropdown';
import type { Breadcrumb } from '@cosmos-lab/components/breadcrumbs';
import { sharedCurrentCompanyController } from '@globals/current-company';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { getStatusOptionsDropdown } from './constants/status.map.constants';
import { getStatusMetadata } from './helpers/status.metadata.helper';

export class RequestDetailsPageHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    get title(): string {
        const { customerRequestDetails, isLoading } =
            sharedCustomerRequestDetailsController;

        if (isLoading || !customerRequestDetails) {
            return t`Loading...`;
        }

        return customerRequestDetails.title;
    }

    get breadcrumbs(): Breadcrumb[] {
        const { customerRequestDetails, auditorFrameworkId, clientId } =
            sharedCustomerRequestDetailsController;

        if (!clientId) {
            console.warn(
                'clientId is missing from sharedCustomerRequestDetailsController',
            );

            return [];
        }

        const { company } = sharedCurrentCompanyController;

        return [
            {
                label: t`Client List`,
                pathname: '/audit-hub/clients',
            },
            {
                label: `${company?.name}`,
                pathname: `/audit-hub/clients/${clientId}/audits`,
            },
            {
                label: `${customerRequestDetails?.framework.label}`,
                pathname: `/audit-hub/clients/${clientId}/audits/${auditorFrameworkId}/details`,
            },
        ];
    }

    get actionStack(): React.JSX.Element | undefined {
        const { customerRequestDetails, isLoading, requestId } =
            sharedCustomerRequestDetailsController;

        if (isLoading || !customerRequestDetails || !requestId) {
            return undefined;
        }

        const { status } = customerRequestDetails;

        const statusInfo = getStatusMetadata(status);

        return (
            <SchemaDropdown
                endIconName="ChevronDown"
                label={statusInfo.label}
                items={getStatusOptionsDropdown().filter((option) => {
                    return option.id !== status;
                })}
                onSelectGlobalOverride={({ id }) => {
                    sharedCustomerRequestDetailsController.updateCustomerRequestStatusWithNavigation(
                        id,
                        requestId,
                        false,
                    );
                }}
            />
        );
    }

    get keyValuePairs(): KeyValuePairProps[] {
        const { customerRequestDetails, isLoading } =
            sharedCustomerRequestDetailsController;

        if (isLoading || !customerRequestDetails) {
            return [];
        }

        const { status, framework, code } = customerRequestDetails;

        const statusInfo = getStatusMetadata(status);

        return [
            {
                id: 'request-id-kv',
                label: t`ID`,
                type: 'TEXT',
                value: code,
            },
            {
                id: 'framework-kv',
                label: t`Framework`,
                type: 'TEXT',
                value: framework.label,
            },
            {
                id: 'status-kv',
                label: t`Status`,
                type: 'TAG',
                value: {
                    iconName: statusInfo.iconName,
                    label: statusInfo.label,
                    type: 'status',
                    colorScheme: statusInfo.colorScheme,
                },
            },
        ];
    }
}
