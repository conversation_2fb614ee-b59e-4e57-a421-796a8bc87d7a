import type { IconName } from '@cosmos/components/icon';
import type { ColorScheme } from '@cosmos/components/metadata';
import type { SchemaDropdownItems } from '@cosmos/components/schema-dropdown';
import { t } from '@globals/i18n/macro';

export const getStatusOptions = (): Record<
    string,
    {
        id: string;
        label: string;
        colorScheme?: ColorScheme;
        startIconName?: IconName;
    }
> => ({
    OUTSTANDING: { id: 'OUTSTANDING', label: t`New` },
    IN_REVIEW: { id: 'IN_REVIEW', label: t`Prepared` },
    ACCEPTED: { id: 'ACCEPTED', label: t`Completed` },
    DELETE: {
        id: 'DELETE-REQUEST',
        label: t`Delete request`,
        colorScheme: 'critical' as ColorScheme,
        startIconName: 'Trash' as IconName,
    },
});

export const getStatusOptionsDropdown = (): SchemaDropdownItems =>
    Object.values(getStatusOptions()).map((option) => ({
        ...option,
        type: 'item' as const,
    }));

export const getStatusOptionsDropdownLazy = (): SchemaDropdownItems =>
    getStatusOptionsDropdown();

export const getStatusMap = (): Record<
    string,
    { label: string; colorScheme: ColorScheme; iconName: IconName }
> => ({
    IN_REVIEW: {
        label: t`Prepared`,
        colorScheme: 'warning',
        iconName: 'InProgress',
    },
    OUTSTANDING: {
        label: t`New`,
        colorScheme: 'neutral',
        iconName: 'Circle',
    },
    REJECTED: {
        label: t`Rejected`,
        colorScheme: 'critical',
        iconName: 'WarningDiamond',
    },
    ACCEPTED: {
        label: t`Completed`,
        colorScheme: 'success',
        iconName: 'CheckCircle',
    },
});

export const getStatusMapLazy = (): Record<
    string,
    { label: string; colorScheme: ColorScheme; iconName: IconName }
> => getStatusMap();
