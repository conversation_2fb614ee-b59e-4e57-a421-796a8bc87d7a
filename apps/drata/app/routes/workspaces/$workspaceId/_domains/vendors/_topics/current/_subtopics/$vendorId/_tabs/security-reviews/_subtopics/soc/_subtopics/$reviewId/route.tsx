import type { ClientLoader } from '@app/types';
import {
    sharedVendorsProfileQuestionnaireAISummaryController,
    sharedVendorsProfileReportsAndDocumentsController,
    sharedVendorsSecurityReviewDetailsController,
    sharedVendorsSecurityReviewDocumentsController,
} from '@controllers/vendors';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { getParentRoute } from '@helpers/path';
import { SocReportPageHeaderModel } from '@models/vendor-security-reviews';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { VendorsProfileSecurityReviewSocView } from '@views/vendors-profile-security-review-soc';

export const meta: MetaFunction = () => [
    { title: t`Vendors Current Security Soc Review` },
];
export const clientLoader = action(
    ({ params, request }: LoaderFunctionArgs): ClientLoader => {
        const { vendorId, reviewId } = params;

        if (
            !reviewId ||
            isNaN(Number(reviewId)) ||
            !vendorId ||
            isNaN(Number(vendorId))
        ) {
            throw new Error('reviewId or vendorId are invalid');
        }

        sharedVendorsSecurityReviewDetailsController.loadSecurityReviewDetails({
            path: { id: Number(reviewId) },
        });

        sharedVendorsSecurityReviewDocumentsController.loadSecurityReviewSOCDocument(
            {
                path: { id: Number(reviewId) },
            },
        );

        sharedVendorsSecurityReviewDocumentsController.setVendorId(
            Number(vendorId),
        );

        sharedVendorsProfileReportsAndDocumentsController.loadDocuments({});

        sharedVendorsProfileQuestionnaireAISummaryController.saveSocSummary();

        const parentHref = new URL(getParentRoute(request.url, 2)).pathname;
        const pageHeaderModel = new SocReportPageHeaderModel();

        pageHeaderModel.setParentUrl(parentHref);

        return {
            pageHeader: pageHeaderModel,
            contentNav: {
                tabs: [],
            },
        };
    },
);

const VendorsCurrentSecurityReviewsSoc = (): React.JSX.Element => {
    return (
        <VendorsProfileSecurityReviewSocView
            data-testid="VendorsCurrentSecurityReviewsSoc"
            data-id="XQSZpMMh"
        />
    );
};

export default VendorsCurrentSecurityReviewsSoc;
