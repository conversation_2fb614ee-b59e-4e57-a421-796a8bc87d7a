import type { <PERSON><PERSON><PERSON>oader } from '@app/types';
import {
    sharedVendorsQuestionnaireAddController,
    sharedVendorsTypeformQuestionnaireController,
} from '@controllers/vendors';
import { ActionStack } from '@cosmos/components/action-stack';
import { dimension3x } from '@cosmos/constants/tokens';
import type { QuestionnaireVendorResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, observer, runInAction } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { AppLink } from '@ui/app-link';
import { ErrorBoundaryComponent } from '@ui/error-boundary';
import {
    getQuestionnairesAddHeaderActions,
    QUESTIONNAIRES_ADD_HEADER_PAGE_ID,
    VendorsQuestionnairesAddView,
} from '@views/vendors-questionnaires-add';

export const meta: MetaFunction = () => [{ title: t`Questionnaire Details` }];

export class VendorsQuestionnairesEditPageHeaderModel {
    questionnaireId: number | null = null;

    constructor(questionnaireId?: number) {
        makeAutoObservable(this);
        this.questionnaireId = questionnaireId || null;
    }

    pageId = QUESTIONNAIRES_ADD_HEADER_PAGE_ID;
    isCentered = true;

    get actionStack(): React.JSX.Element {
        const actions = getQuestionnairesAddHeaderActions(
            false,
            this.questionnaireId ?? undefined,
        );

        return (
            <ActionStack
                data-id="vendors-questionnaires-add-page-action-stack"
                gap={dimension3x}
                stacks={[
                    {
                        actions,
                        id: `${QUESTIONNAIRES_ADD_HEADER_PAGE_ID}-actions-stack-0`,
                    },
                ]}
            />
        );
    }

    get backLink(): React.JSX.Element {
        const { currentWorkspace } = sharedWorkspacesController;
        const questionnairesPath = `/workspaces/${currentWorkspace?.id}/vendors/questionnaires`;

        return (
            <AppLink href={questionnairesPath} size="sm">
                {t`Back to Questionnaires`}
            </AppLink>
        );
    }

    get title(): string {
        return (
            sharedVendorsTypeformQuestionnaireController.title ||
            'Untitled questionnaire'
        );
    }
}

export const ErrorBoundary = (): React.JSX.Element => {
    return (
        <ErrorBoundaryComponent
            data-testid="ErrorBoundary"
            data-id="PJCtli7m"
            backLink={{
                href: '/vendors/questionnaires',
                label: 'Back to Questionnaires',
            }}
        />
    );
};
export const clientLoader = ({ params }: LoaderFunctionArgs): ClientLoader => {
    const { questionnaireId } = params;

    runInAction(() => {
        // Initialize questionnaire for editing (validates permissions and loads data)
        sharedVendorsTypeformQuestionnaireController.initializeForEdit(
            questionnaireId,
        );

        // Setup form data when questionnaire is loaded
        sharedVendorsTypeformQuestionnaireController.setupFormData(
            (data: QuestionnaireVendorResponseDto) => {
                sharedVendorsQuestionnaireAddController.loadQuestionnaireData(
                    data,
                );
            },
        );
    });

    return {
        pageHeader: new VendorsQuestionnairesEditPageHeaderModel(
            Number(questionnaireId),
        ),
    };
};

const VendorsQuestionnairesEdit = observer((): React.JSX.Element => {
    return (
        <VendorsQuestionnairesAddView
            data-testid="VendorsQuestionnairesEdit"
            data-id="hBxXw1W2"
        />
    );
});

export default VendorsQuestionnairesEdit;
