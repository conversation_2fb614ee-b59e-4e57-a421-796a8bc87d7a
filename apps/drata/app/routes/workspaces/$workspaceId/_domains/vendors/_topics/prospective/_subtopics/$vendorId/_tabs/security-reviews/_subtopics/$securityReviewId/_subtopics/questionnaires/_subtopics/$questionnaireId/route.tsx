import type { ClientLoader } from '@app/types';
import { sharedUtilitiesObservationsController } from '@controllers/utilities';
import {
    sharedVendorsProfileQuestionnaireAISummaryController,
    sharedVendorsQuestionnairesController,
    sharedVendorsSecurityReviewDetailsController,
    sharedVendorsSecurityReviewObservationsController,
} from '@controllers/vendors';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { VendorsSecurityReviewQuestionnairePageHeaderModel } from '@models/vendors-profile';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { VendorsSecurityReviewQuestionnaireView } from '@views/vendors-security-review-questionnaire';

export const meta: MetaFunction = () => [
    { title: t`Vendors Prospective Security Review Questionnaire` },
];
export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { vendorId, securityReviewId, questionnaireId } = params;

        if (!vendorId) {
            throw new Error('Vendor ID is required');
        }
        if (!securityReviewId) {
            throw new Error('Review ID is required');
        }
        if (!questionnaireId) {
            throw new Error('Questionnaire ID is required');
        }

        sharedVendorsProfileQuestionnaireAISummaryController.loadQuestionnaireSummary(
            Number(questionnaireId),
        );

        sharedVendorsSecurityReviewDetailsController.loadSecurityReviewDetails({
            path: { id: Number(securityReviewId) },
        });
        sharedVendorsQuestionnairesController.loadAll(
            Number(vendorId),
            Number(questionnaireId),
        );

        sharedVendorsSecurityReviewObservationsController.loadSecurityReviewObservations(
            {
                path: { id: Number(securityReviewId) },
            },
        );

        sharedUtilitiesObservationsController.openUtility();

        return {
            pageHeader: new VendorsSecurityReviewQuestionnairePageHeaderModel(
                'prospective',
            ),
            contentNav: {
                tabs: [],
            },
            utilities: {
                utilitiesList: ['observations'],
            },
        };
    },
);

const VendorsCurrentSecurityReviewQuestionnaire = (): React.JSX.Element => {
    return (
        <VendorsSecurityReviewQuestionnaireView
            data-testid="VendorsCurrentSecurityReviewQuestionnaire"
            data-id="ExP8wMs1"
        />
    );
};

export default VendorsCurrentSecurityReviewQuestionnaire;
