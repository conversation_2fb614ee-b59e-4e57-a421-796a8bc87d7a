import type { ClientLoader } from '@app/types';
import {
    sharedVendorsDetailsController,
    sharedVendorsProfileQuestionnaireAISummaryController,
    sharedVendorsSecurityReviewDetailsController,
    sharedVendorsSecurityReviewDocumentsController,
} from '@controllers/vendors';
import { action } from '@globals/mobx';
import { getParentRoute } from '@helpers/path';
import { VendorsSecurityReviewSOCCompletedPageHeaderModel } from '@models/vendors-profile';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { VendorsSecurityReviewSOCCompletedView } from '@views/vendors-security-review-completed';

export const meta: MetaFunction = () => [
    { title: 'Vendors Prospective Security Reviews SOC Completed' },
];
export const clientLoader = action(
    ({ params, request }: LoaderFunctionArgs): ClientLoader => {
        const { vendorId, securityReviewId } = params;

        if (
            !vendorId ||
            isNaN(Number(vendorId)) ||
            !securityReviewId ||
            isNaN(Number(securityReviewId))
        ) {
            throw new Error('vendorId or securityReviewId are invalid');
        }

        sharedVendorsDetailsController.loadVendorDetails(Number(vendorId));

        sharedVendorsSecurityReviewDetailsController.loadSecurityReviewDetails({
            path: { id: Number(securityReviewId) },
        });

        sharedVendorsSecurityReviewDocumentsController.loadSecurityReviewSOCDocument(
            {
                path: { id: Number(securityReviewId) },
            },
        );

        sharedVendorsSecurityReviewDocumentsController.setVendorId(
            Number(vendorId),
        );

        sharedVendorsProfileQuestionnaireAISummaryController.saveSocSummary();

        const pageHeaderModel =
            new VendorsSecurityReviewSOCCompletedPageHeaderModel();

        pageHeaderModel.setParentRoute(getParentRoute(request.url, 3));

        return {
            pageHeader: pageHeaderModel,
            contentNav: {
                tabs: [],
            },
        };
    },
);

const VendorsProspectiveSecurityReviewCompleted = (): React.JSX.Element => {
    return (
        <VendorsSecurityReviewSOCCompletedView
            data-testid="VendorsProspectiveSecurityReviewCompleted"
            data-id="ES4y_Dns"
        />
    );
};

export default VendorsProspectiveSecurityReviewCompleted;
