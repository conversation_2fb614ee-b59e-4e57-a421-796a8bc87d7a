import { useEffect } from 'react';
import type { ClientLoader } from '@app/types';
import { sharedFrameworkCreateController } from '@controllers/frameworks';
import { t } from '@globals/i18n/macro';
import { action, reaction } from '@globals/mobx';
import { getParentRoute } from '@helpers/path';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { useLocation, useNavigate } from '@remix-run/react';
import { AppLink } from '@ui/app-link';
import { CreateFrameworkView } from '@views/create-framework';

export const meta: MetaFunction = () => {
    return [{ title: t`Create custom framework` }];
};
export const clientLoader = action(
    ({ request }: LoaderFunctionArgs): ClientLoader => {
        const parentHref = new URL(
            request.url.split('/').slice(0, -1).join('/'),
        ).pathname;

        sharedFrameworkCreateController.load();

        return {
            pageHeader: {
                title: t`Create framework`,
                backLink: (
                    <AppLink href={parentHref} data-testid="BackLink" size="sm">
                        {t`Back to frameworks`}
                    </AppLink>
                ),
            },
        };
    },
);

const CreateFramework = (): React.JSX.Element => {
    const location = useLocation();
    const { pathname } = location;
    const parentRoute = getParentRoute(pathname);
    const navigate = useNavigate();

    useEffect(() => {
        const disposer = reaction(
            () => sharedFrameworkCreateController.hasReachedLimit,
            (hasReachedLimit) => {
                if (hasReachedLimit) {
                    navigate(parentRoute);
                }
            },
        );

        return () => {
            disposer();
        };
    }, [navigate, parentRoute]);

    return (
        <CreateFrameworkView data-testid="CreateFramework" data-id="COUgDp03" />
    );
};

export default CreateFramework;
