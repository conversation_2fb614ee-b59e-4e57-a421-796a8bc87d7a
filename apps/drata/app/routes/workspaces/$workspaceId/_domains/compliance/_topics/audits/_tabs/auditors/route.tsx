import type { ClientLoader } from '@app/types';
import { sharedAuditorsController } from '@controllers/audits';
import { action } from '@globals/mobx';
import { AuditsAuditorsListView } from '@views/audits-auditors-list';

export const clientLoader = action((): ClientLoader => {
    sharedAuditorsController.auditorsListQuery.load();
    sharedAuditorsController.auditorFirmsQuery.load();

    return null;
});

const Audits = (): React.JSX.Element => {
    return <AuditsAuditorsListView data-testid="Audits" data-id="lHwpZI3C" />;
};

export default Audits;
