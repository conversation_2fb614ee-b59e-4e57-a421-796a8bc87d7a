import type { ClientLoader } from '@app/types';
import { sharedControlsController } from '@controllers/controls-owners-candidates';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunctionArgs } from '@remix-run/react';
import { AppLink } from '@ui/app-link';
import { CreateControlInfoView } from '@views/create-control-info';

export const meta: MetaFunction = () => {
    return [{ title: t`Create control` }];
};

export const clientLoader = action(
    ({ request }: ClientLoaderFunctionArgs): ClientLoader => {
        sharedControlsController.loadControlOwnersCandidates({
            page: 1,
        });

        const parentHref = new URL(
            request.url.split('/').slice(0, -1).join('/'),
        ).pathname;

        return {
            pageHeader: {
                title: t`Create control`,
                isCentered: true,
                backLink: (
                    <AppLink href={parentHref} data-testid="BackLink" size="sm">
                        {t`Back to requirement`}
                    </AppLink>
                ),
            },
            topicsNav: {},
            contentNav: {
                tabs: [],
            },
        };
    },
);

const CreateFrameworkRequirementControl = (): React.JSX.Element => {
    return (
        <CreateControlInfoView
            data-testid="CreateFrameworkRequirementControl"
            data-id="O_nd5QeE"
        />
    );
};

export default CreateFrameworkRequirementControl;
