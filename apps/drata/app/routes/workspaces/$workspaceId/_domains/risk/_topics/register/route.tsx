import type { ClientLoader } from '@app/types';
import {
    sharedRiskLibraryCategoriesController,
    sharedRiskLibraryController,
    sharedRiskSettingsController,
} from '@controllers/risk';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => {
    return [{ title: 'Risk Register' }];
};

export const clientLoader = action((): ClientLoader => {
    sharedRiskLibraryController.loadInitialData();
    sharedRiskSettingsController.load();
    sharedRiskLibraryCategoriesController.loadCategories();

    return {
        pageHeader: {
            title: t`Register`,
        },
        contentNav: {
            tabs: [
                {
                    id: 'risk.register.library',
                    topicPath: 'risk/register/library',
                    label: t`Library`,
                },
                {
                    id: 'risk.register.management',
                    topicPath: 'risk/register/management',
                    label: t`Management`,
                },
            ],
        },
    };
});

const RiskRegister = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="RiskRegister"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default RiskRegister;
