import type { ClientLoader } from '@app/types';
import { BackLinkComponent } from '@components/access-review';
import {
    sharedAccessReviewApplicationsController,
    sharedAccessReviewController,
    sharedActiveAccessReviewPeriodsController,
    sharedEditReviewPeriodController,
} from '@controllers/access-reviews';
import { sharedUsersController } from '@controllers/users';
import { DEFAULT_PAGE, DEFAULT_PAGE_SIZE } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { EditReviewPeriodView } from '@views/access-review-create-period';

export const meta: MetaFunction = () => [{ title: t`Edit Review Period` }];

export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { periodId } = params;

        if (!periodId) {
            throw new Error('Period ID is required');
        }

        const { loadReviewPeriod } = sharedEditReviewPeriodController;
        const { accessReviewApplicationsQuery } =
            sharedAccessReviewApplicationsController;

        sharedAccessReviewController.accessReview.load();

        sharedActiveAccessReviewPeriodsController.activeAccessReviewPeriods.load();

        accessReviewApplicationsQuery.load({
            query: {
                page: DEFAULT_PAGE,
                limit: DEFAULT_PAGE_SIZE,
            },
        });

        loadReviewPeriod(Number(periodId));

        sharedUsersController.loadReviewerUsers();

        return {
            pageHeader: {
                title: t`Edit Review Period`,
                pageId: 'access-review-edit-period',
                backLink: <BackLinkComponent />,
            },
            layout: {
                centered: true,
            },
            tabs: [],
        };
    },
);

const AccessReviewWorkflowEditPeriod = (): React.JSX.Element => {
    return (
        <EditReviewPeriodView
            data-testid="AccessReviewWorkflowEditPeriod"
            data-id="WF1ii-bq"
        />
    );
};

export default AccessReviewWorkflowEditPeriod;
