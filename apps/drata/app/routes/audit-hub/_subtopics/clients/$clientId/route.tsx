import type { ClientLoader } from '@app/types';
import { sharedAuthController } from '@controllers/auth';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { snackbarController } from '@controllers/snackbar';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { type ClientLoaderFunction, Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { clientId } = params;

        if (!sharedAuthController.email || !sharedAuthController.region) {
            snackbarController.addSnackbar({
                id: 'auditor-settings-update-error',
                props: {
                    title: t`Email and region are required`,
                    description: t`An error occurred while accessing to client's page. Try again later.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });
        }

        if (
            clientId &&
            sharedAuthController.email &&
            sharedAuthController.region
        ) {
            sharedAuthController.attemptLogin(
                sharedAuthController.email,
                sharedAuthController.region,
                { clientId }, // initiate a secondary authentication flow with tenant token
            );

            sharedCustomerRequestDetailsController.clientId = clientId;
        }

        return null;
    },
);

const AuditHubClient = observer((): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="AuditHubClient"
            data-id="CY5rwNFa"
        >
            {sharedAuthController.isAttemptingLogin && (
                // TODO: replace with loading component https://drata.atlassian.net/browse/ENG-69894
                <div data-id="loading">{t`Loading...`}</div>
            )}
            {sharedAuthController.hasAttemptedLogin && <Outlet />}
        </RouteLandmark>
    );
});

export default AuditHubClient;
