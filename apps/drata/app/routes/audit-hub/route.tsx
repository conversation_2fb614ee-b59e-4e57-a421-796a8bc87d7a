import type { ClientLoader } from '@app/types';
import { sharedAuthController } from '@controllers/auth';
import { Box } from '@cosmos/components/box';
import { Icon, type IconName } from '@cosmos/components/icon';
import { SchemaDropdown } from '@cosmos/components/schema-dropdown';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import {
    type ClientLoaderFunction,
    useLocation,
    useNavigate,
} from '@remix-run/react';
import { DomainContent } from '@ui/domain-content';
import { PageAsideUi } from '@ui/page-aside';

export const meta: MetaFunction = () => [{ title: t`Audit Hub` }];

export const clientLoader: ClientLoaderFunction = (): ClientLoader => {
    return {
        subdomainConfig: {
            id: 'audit-hub',
            userPart: `/audit-hub`,
            authRoute: '/auth/login',
        },
    };
};

export interface Tab {
    topicPath: string;
    label: string;
    iconName?: IconName;
}

const AuditHub = observer(() => {
    const navigate = useNavigate();
    const location = useLocation();
    const isSettingsPage = location.pathname.includes('/audit-hub/settings');

    return (
        <Stack
            direction="column"
            display="flex"
            data-id="QqMJifIh"
            data-testid={isSettingsPage ? 'AuditHubSettings' : 'AuditHub'}
            height="100%"
            width="100%"
            minHeight="0"
        >
            <Box
                backgroundColor="neutralBackgroundNone"
                borderColor="neutralBorderFaded"
                borderWidth="borderWidthSm"
                data-id="Header"
                width="100%"
            >
                <Stack
                    direction="row"
                    align="center"
                    justify="between"
                    p="md"
                    data-id="HeaderNav"
                >
                    <Stack direction="row" align="center" gap="2x">
                        <Icon name="DrataFilled" size="500" />
                        <Text type="title">{t`Audit Hub`}</Text>
                    </Stack>
                    <Stack direction="row" align="center" gap="2x">
                        <SchemaDropdown
                            isIconOnly
                            label={t`User menu`}
                            startIconName="UserCircleSingle"
                            level="tertiary"
                            colorScheme="neutral"
                            data-testid="UserMenu"
                            data-id="user-dropdown"
                            items={[
                                {
                                    id: 'setting',
                                    label: t`Setting`,
                                    onSelect: () => {
                                        navigate('/audit-hub/settings/profile');
                                    },
                                },
                                {
                                    id: 'signout',
                                    label: t`Sign out`,
                                    onSelect: () => {
                                        sharedAuthController.logout();
                                    },
                                },
                            ]}
                        />
                    </Stack>
                </Stack>
            </Box>
            <DomainContent />
            <PageAsideUi />
        </Stack>
    );
});

export default AuditHub;
