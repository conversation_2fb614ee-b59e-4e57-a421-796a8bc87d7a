import { isEmpty } from 'lodash-es';
import { sharedControlDetailsController } from '@controllers/controls';
import { Box } from '@cosmos/components/box';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { TagGroup } from '@cosmos/components/tag-group';
import { Text } from '@cosmos/components/text';
import { AvatarStack } from '@cosmos-lab/components/avatar-stack';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { ControlEvidenceSection } from '@views/audit-hub-evidence-request-details-controls';
import { sharedControlPanelModel } from '../model/control-panel.model';

const MAX_VISIBLE_TAGS = 3;

export const DetailsSection = observer((): React.JSX.Element => {
    const { controlDetails } = sharedControlDetailsController;
    const {
        owners,
        frameworksTags,
        shouldDisplayName,
        shouldDisplayCode,
        shouldDisplayFrameworks,
        shouldDisplayEvidences,
    } = sharedControlPanelModel;

    return (
        <Stack
            gap="xl"
            direction="column"
            data-testid="DetailsSection"
            data-id="GEzvmukv"
        >
            <Box pb="md">
                <Text type="title" size="400">
                    <Trans>Controls details</Trans>
                </Text>
            </Box>
            <KeyValuePair
                label={t`Owners`}
                type={'REACT_NODE'}
                value={
                    isEmpty(owners) ? (
                        <EmptyValue label={t`No owners`} />
                    ) : (
                        <AvatarStack
                            maxVisibleItems={MAX_VISIBLE_TAGS}
                            avatarData={owners}
                        />
                    )
                }
            />
            {shouldDisplayName && controlDetails?.name && (
                <KeyValuePair
                    label={t`Name`}
                    type="TEXT"
                    value={controlDetails.name}
                />
            )}
            {shouldDisplayCode && controlDetails?.code && (
                <KeyValuePair
                    label={t`Code`}
                    type="TEXT"
                    value={controlDetails.code}
                />
            )}
            {shouldDisplayFrameworks && (
                <KeyValuePair
                    label={t`Framework`}
                    type="REACT_NODE"
                    value={
                        isEmpty(frameworksTags) ? (
                            <EmptyValue label={t`No frameworks`} />
                        ) : (
                            <TagGroup maxVisibleTags={MAX_VISIBLE_TAGS}>
                                {frameworksTags.map((framework) => (
                                    <Metadata
                                        key={framework.label}
                                        colorScheme="neutral"
                                        label={framework.label}
                                        type="tag"
                                        data-id="DHqUzAfC"
                                    />
                                ))}
                            </TagGroup>
                        )
                    }
                />
            )}
            <KeyValuePair
                label={t`Description`}
                type={
                    isEmpty(controlDetails?.description) ? 'REACT_NODE' : 'TEXT'
                }
                value={
                    isEmpty(controlDetails?.description) ? (
                        <EmptyValue label={t`No description`} />
                    ) : (
                        controlDetails?.description
                    )
                }
            />
            <KeyValuePair
                label={t`Question`}
                type={isEmpty(controlDetails?.question) ? 'REACT_NODE' : 'TEXT'}
                value={
                    isEmpty(controlDetails?.question) ? (
                        <EmptyValue label={t`No question`} />
                    ) : (
                        controlDetails?.question
                    )
                }
            />
            <KeyValuePair
                label={t`Activities`}
                type={isEmpty(controlDetails?.activity) ? 'REACT_NODE' : 'TEXT'}
                value={
                    isEmpty(controlDetails?.activity) ? (
                        <EmptyValue label={t`No activities`} />
                    ) : (
                        controlDetails?.activity
                    )
                }
            />
            {shouldDisplayEvidences && (
                <KeyValuePair
                    label={t`Evidence`}
                    type="REACT_NODE"
                    value={
                        <ControlEvidenceSection data-id="control-evidence-section" />
                    }
                />
            )}
        </Stack>
    );
});
