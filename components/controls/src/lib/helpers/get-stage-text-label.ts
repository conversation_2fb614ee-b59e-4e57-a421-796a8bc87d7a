import type { ApprovalsResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { REVIEW_APPROVAL_STATUS } from '../constants/review-approval-status.constant';

export function getStageTextLabel(
    stageText?: ApprovalsResponseDto['approvalStatus'],
): string {
    switch (stageText) {
        case REVIEW_APPROVAL_STATUS.INITIALIZE: {
            return t`Initialize`;
        }
        case REVIEW_APPROVAL_STATUS.PREPARE_FOR_APPROVERS: {
            return t`Prepare for approvers`;
        }
        case REVIEW_APPROVAL_STATUS.NEEDS_APPROVAL: {
            return t`Needs approval`;
        }
        case REVIEW_APPROVAL_STATUS.COMPLETED: {
            return t`Approved`;
        }
        case REVIEW_APPROVAL_STATUS.CHANGES_REQUESTED: {
            return t`Changes requested`;
        }
        default: {
            return '';
        }
    }
}
