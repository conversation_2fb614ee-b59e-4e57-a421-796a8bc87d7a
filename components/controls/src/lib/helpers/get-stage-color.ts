import type { ColorScheme } from '@cosmos/components/metadata';
import type { ApprovalsResponseDto } from '@globals/api-sdk/types';
import { REVIEW_APPROVAL_STATUS } from '../constants/review-approval-status.constant';

export function getStageColor(
    stageText?: ApprovalsResponseDto['approvalStatus'],
): ColorScheme {
    switch (stageText) {
        case REVIEW_APPROVAL_STATUS.INITIALIZE: {
            return 'neutral';
        }
        case REVIEW_APPROVAL_STATUS.COMPLETED: {
            return 'success';
        }
        case REVIEW_APPROVAL_STATUS.PREPARE_FOR_APPROVERS: {
            return 'warning';
        }
        case REVIEW_APPROVAL_STATUS.NEEDS_APPROVAL:
        case REVIEW_APPROVAL_STATUS.CHANGES_REQUESTED: {
            return 'critical';
        }
        default: {
            return 'neutral';
        }
    }
}
