import type { LibraryTestActiveTestsColumns } from '@components/library-test-active-tests-card';
import type { DatatableProps } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { COLUMN_SIZES } from '@helpers/table';
import { LibraryTestActiveTestsTestManageCell } from './library-test-active-tests-test-manage';
import { LibraryTestActiveTestsTestNameCell } from './library-test-active-tests-test-name';
import { LibraryTestActiveTestsTestWorkspaceCell } from './library-test-active-tests-test-workspace';

export const LIBRARY_TEST_ACTIVE_TESTS_COLUMNS: DatatableProps<LibraryTestActiveTestsColumns>['columns'] =
    [
        {
            id: 'name',
            accessorKey: 'name',
            header: () => t`Name`,
            cell: LibraryTestActiveTestsTestNameCell,
            size: COLUMN_SIZES.LARGE,
        },
        {
            id: 'workspace',
            accessorKey: 'workspace',
            header: () => t`Workspace`,
            cell: LibraryTestActiveTestsTestWorkspaceCell,
            size: COLUMN_SIZES.XS,
        },
        {
            id: 'testId',
            accessorKey: 'testId',
            header: '',
            cell: LibraryTestActiveTestsTestManageCell,
            size: COLUMN_SIZES.XS,
        },
    ];
