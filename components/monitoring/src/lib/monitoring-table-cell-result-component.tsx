import { Metadata } from '@cosmos/components/metadata';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import type { MonitoringTableCellProps } from './types/monitoring.types';

export const MonitoringTableCellResultComponent = ({
    row: { original },
}: MonitoringTableCellProps): React.JSX.Element => {
    const { checkStatus, checkResultStatus } = original;

    if (checkStatus === 'TESTING') {
        return <Text data-id="status-badge">{t`Testing...`}</Text>;
    }

    if (checkStatus === 'UNUSED') {
        return <Text data-id="status-badge">-</Text>;
    }

    switch (checkResultStatus) {
        case 'READY': {
            return (
                <Metadata
                    data-id="status-badge"
                    colorScheme="success"
                    type="status"
                    label={t`Passed`}
                />
            );
        }
        case 'FAILED': {
            return (
                <Metadata
                    data-id="status-badge"
                    colorScheme="critical"
                    type="status"
                    label={t`Failed`}
                />
            );
        }
        case 'ERROR': {
            return (
                <Metadata
                    data-id="status-badge"
                    colorScheme="warning"
                    type="status"
                    label={t`Error`}
                />
            );
        }
        default: {
            return <Text data-id="status-badge">-</Text>;
        }
    }
};
