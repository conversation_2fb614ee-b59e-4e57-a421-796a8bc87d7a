import { useMemo } from 'react';
import { z } from 'zod';
import {
    sharedAccessReviewApplicationsController,
    sharedEditReviewPeriodController,
} from '@controllers/access-reviews';
import { Avatar } from '@cosmos/components/avatar';
import { But<PERSON> } from '@cosmos/components/button';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type {
    AccessReviewApplicationResponseDto,
    AccessReviewPeriodApplicationResponseDto,
} from '@globals/api-sdk/types';
import { t, Trans } from '@globals/i18n/macro';
import { action, observer, toJS } from '@globals/mobx';
import { getInitials } from '@helpers/formatters';
import { Form, type FormSchema, type FormValues } from '@ui/forms';
import { openAccessReviewAddApplicationModal } from '@views/access-review-applications';
import {
    getApplicationLogo,
    getApplicationTypeLabel,
} from '@views/access-review-create-period';

export const ApplicationsStep = observer(
    ({
        formRef,
        formId,
        onSubmit,
    }: {
        formRef: React.ForwardedRef<HTMLFormElement>;
        formId: string;
        onSubmit: () => void;
    }): React.JSX.Element => {
        const {
            accessReviewApplicationsList: applications,
            isLoading,
            hasNextPage,
            handleFetchOptions,
            accessReviewApplicationsQuery,
        } = sharedAccessReviewApplicationsController;

        const { setSelectedApplicationsData, selectedApplicationsData } =
            sharedEditReviewPeriodController;

        const handleSubmit = action((values: FormValues) => {
            const selectedItems =
                values.selectedApplications as AccessReviewPeriodApplicationResponseDto[];

            /**
             * Merge selected items with reviewers data from applications list.
             */
            const mergeSelectedItemsWithReviewers = (
                itemsSelected: AccessReviewPeriodApplicationResponseDto[],
                applicationsList: AccessReviewApplicationResponseDto[],
            ): AccessReviewPeriodApplicationResponseDto[] => {
                return itemsSelected.map((selectedItem) => {
                    const appFromList = applicationsList.find(
                        (app) =>
                            app.accessApplicationId ===
                                selectedItem.accessApplicationId ||
                            app.id === selectedItem.id,
                    );

                    return {
                        ...selectedItem,
                        reviewers:
                            appFromList?.reviewers ?? selectedItem.reviewers,
                    };
                });
            };

            const finalSelectedItems = mergeSelectedItemsWithReviewers(
                selectedItems,
                applications,
            );

            action(() => {
                setSelectedApplicationsData(finalSelectedItems);
            })();

            onSubmit();
        });

        const applicationsAsListBoxItems = useMemo(() => {
            // Create a Map to deduplicate applications by ID
            const uniqueApps = new Map<
                number,
                AccessReviewApplicationResponseDto
            >();

            applications.forEach((app) => {
                uniqueApps.set(app.accessApplicationId, app);
            });

            return [...uniqueApps.values()].map(
                (app): ListBoxItemData => ({
                    id: app.id.toString(),
                    label: app.name,
                    name: app.name,
                    value: app.id.toString(),
                    source: app.source,
                    websiteUrl: app.websiteUrl,
                    description: getApplicationTypeLabel(app.source),
                    reviewers: app.reviewers,
                    logo: getApplicationLogo(app),
                    accessApplicationId: app.accessApplicationId,
                    startSlot: (
                        <Avatar
                            size="sm"
                            imgSrc={getApplicationLogo(app)}
                            fallbackText={getInitials(app.name)}
                        />
                    ),
                }),
            );
        }, [applications]);

        const initialSelectedApplications = useMemo(() => {
            const selectedApps = toJS(selectedApplicationsData);

            // Create a Map to deduplicate selected applications by ID
            const uniqueApps = new Map<
                number,
                AccessReviewPeriodApplicationResponseDto
            >();

            selectedApps.forEach((app) => {
                uniqueApps.set(app.id, app);
            });

            return [...uniqueApps.values()].map(
                (app): ListBoxItemData => ({
                    id: app.id.toString(),
                    label: app.name,
                    name: app.name,
                    value: app.id.toString(),
                    source: app.source,
                    description: getApplicationTypeLabel(app.source),
                    reviewers: app.reviewers,
                    logo: getApplicationLogo(app),
                    websiteUrl: app.websiteUrl,
                    accessApplicationId: app.accessApplicationId,
                    startSlot: (
                        <Avatar
                            size="sm"
                            imgSrc={getApplicationLogo(app)}
                            fallbackText={getInitials(app.name)}
                        />
                    ),
                }),
            );
        }, [selectedApplicationsData]);

        const formSchema: FormSchema = useMemo(
            () => ({
                selectedApplications: {
                    type: 'combobox',
                    label: t`Applications`,
                    placeholder: t`Search by application name`,
                    isMultiSelect: true,
                    initialValue: initialSelectedApplications,
                    options: applicationsAsListBoxItems,
                    hasMore: hasNextPage,
                    isLoading,
                    onFetchOptions: (params: {
                        search?: string;
                        increasePage?: boolean;
                    }) => {
                        handleFetchOptions(params);
                    },
                    validator: z
                        .array(
                            z.object({
                                id: z.string(),
                                label: z.string(),
                                value: z.string(),
                            }),
                        )
                        .min(1, t`At least one application must be selected`),
                    removeAllSelectedItemsLabel: t`Clear all`,
                    getRemoveIndividualSelectedItemClickLabel: ({
                        itemLabel,
                    }: {
                        itemLabel: string;
                    }) => t`Remove ${itemLabel}`,
                    getSearchEmptyState: () => t`No applications found`,
                    loaderLabel: t`Loading applications...`,
                },
            }),
            [
                initialSelectedApplications,
                applicationsAsListBoxItems,
                hasNextPage,
                isLoading,
                handleFetchOptions,
            ],
        );

        const handleAddNewApplication = () => {
            openAccessReviewAddApplicationModal(() => {
                accessReviewApplicationsQuery.invalidate();
            });
        };

        return (
            <Stack
                gap="xl"
                direction="column"
                data-testid="ApplicationsStep"
                data-id="access-review-applications-step"
                style={{ maxWidth: '600px' }}
            >
                <Stack gap="lg" direction="column">
                    <Text type="title">
                        <Trans>Select applications to review</Trans>
                    </Text>
                </Stack>

                <Stack gap="2x" direction="column">
                    <Form
                        hasExternalSubmitButton
                        formId={formId}
                        data-id={formId}
                        ref={formRef}
                        schema={formSchema}
                        key={`applications-create`}
                        onSubmit={handleSubmit}
                    />
                </Stack>

                <Stack gap="sm" direction="column">
                    <Text size="100" colorScheme="neutral" type="body">
                        <Trans>
                            You can manually add a new application to your
                            library if it is missing above. It will
                            automatically be added to this review period.
                        </Trans>
                    </Text>
                    <Stack direction="row" justify="start">
                        <Button
                            label={t`Add new application`}
                            level="secondary"
                            size="sm"
                            data-id="add-new-application-button"
                            onClick={handleAddNewApplication}
                        />
                    </Stack>
                </Stack>
            </Stack>
        );
    },
);
