import { isEmpty } from 'lodash-es';
import { AppDatatable } from '@components/app-datatable';
import { sharedEditReviewPeriodController } from '@controllers/access-reviews';
import { Feedback } from '@cosmos/components/feedback';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { dimension170x } from '@cosmos/constants/tokens';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { editReviewersStepModel } from '@models/access-review';

export const ReviewersStep = observer((): React.JSX.Element => {
    const { columns } = editReviewersStepModel;
    const { selectedApplicationsData } = sharedEditReviewPeriodController;
    const hasApplicationsNeedingReviewers = !isEmpty(
        selectedApplicationsData.filter((app) => isEmpty(app.reviewers)),
    );

    return (
        <Stack
            gap="xl"
            direction="column"
            data-testid="ReviewersStep"
            data-id="access-review-review-step"
            maxWidth={dimension170x}
        >
            <Stack gap="lg" direction="column">
                <Text type="title">
                    <Trans>Review added applications</Trans>
                </Text>
                <Text type="body">
                    <Trans>
                        All applications in a review period must have at least
                        one reviewer. You can update the reviewers for an
                        application by editing its details. Reviewers receive an
                        email when you add or remove them from a review.
                    </Trans>
                </Text>
            </Stack>

            <Stack gap="lg" direction="column">
                <Stack direction="row" gap="sm" align="center">
                    <Text type="title" size="200">
                        <Trans>Added applications:</Trans>
                    </Text>
                    <Metadata
                        label={selectedApplicationsData.length.toString()}
                        type="number"
                        colorScheme="neutral"
                        data-id="added-applications-count"
                    />
                </Stack>

                <AppDatatable
                    isLoading={false}
                    tableId="reviewers-step-table"
                    data-id="reviewers-step-table"
                    columns={columns}
                    data={selectedApplicationsData}
                    total={selectedApplicationsData.length}
                    isSortable={false}
                    emptyStateProps={{
                        title: t`No applications added`,
                        description: t`Go back to add applications to your review period.`,
                    }}
                    tableSearchProps={{
                        hideSearch: true,
                    }}
                />

                {hasApplicationsNeedingReviewers && (
                    <Feedback
                        title={t`All selected applications must have a reviewer assigned`}
                        description={t`Please assign a reviewer to all applications before saving this setup.`}
                        severity="critical"
                        data-id="missing-reviewers-feedback"
                    />
                )}

                {!hasApplicationsNeedingReviewers &&
                    !isEmpty(selectedApplicationsData) && (
                        <Feedback
                            title={t`All applications have reviewers assigned`}
                            description={t`You can proceed to create the access review period.`}
                            severity="success"
                            data-id="all-reviewers-assigned-feedback"
                        />
                    )}
            </Stack>
        </Stack>
    );
});
