import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { AppLink } from '@ui/app-link';

export const BackLinkComponent = observer((): React.JSX.Element => {
    const { currentWorkspaceId } = sharedWorkspacesController;

    return (
        <AppLink
            href={`/workspaces/${currentWorkspaceId}/governance/access-review/active`}
            size="sm"
            data-id="-dSw2-ll"
        >
            {t`Back to Access Review`}
        </AppLink>
    );
});
