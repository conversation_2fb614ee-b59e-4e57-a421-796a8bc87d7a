import { sharedEditReviewPeriodController } from '@controllers/access-reviews';
import { modalController } from '@controllers/modal';
import { Button } from '@cosmos/components/button';
import type { AccessReviewPeriodApplicationResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import type { FormValues } from '@ui/forms';
import { EditApplicationDetailsModal } from '../../modals/edit-application-details-modal';

const handleEditApplication = (
    application: AccessReviewPeriodApplicationResponseDto,
    updateApplicationDetails: (
        application: AccessReviewPeriodApplicationResponseDto,
    ) => void,
) => {
    modalController.openModal({
        id: 'edit-application-modal',
        content: () => (
            <EditApplicationDetailsModal
                application={application}
                data-id="Emp0pZet"
                onConfirm={(values: FormValues) => {
                    action(() => {
                        updateApplicationDetails(
                            values as AccessReviewPeriodApplicationResponseDto,
                        );
                    })();

                    modalController.closeModal('edit-application-modal');
                }}
                onCancel={() => {
                    modalController.closeModal('edit-application-modal');
                }}
            />
        ),
    });
};

export const EditApplicationCell = observer(
    ({
        row: { original },
    }: {
        row: { original: AccessReviewPeriodApplicationResponseDto };
    }): React.JSX.Element => {
        const { updateApplicationDetails } = sharedEditReviewPeriodController;

        return (
            <Button
                isIconOnly
                label={t`Edit`}
                startIconName="Edit"
                level="tertiary"
                size="sm"
                data-id="WpeAZhp5"
                data-testid="EditApplicationCell"
                onClick={() => {
                    handleEditApplication(original, (application) => {
                        action(() => {
                            updateApplicationDetails({
                                id: application.id,
                                name: application.name,
                                websiteUrl: application.websiteUrl ?? undefined,
                                reviewers: application.reviewers,
                                source: application.source,
                            });
                        })();
                    });
                }}
            />
        );
    },
);
