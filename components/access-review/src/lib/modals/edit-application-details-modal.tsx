import { z } from 'zod';
import { sharedUsersInfiniteController } from '@controllers/users';
import { Modal } from '@cosmos/components/modal';
import type { AccessReviewPeriodApplicationResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getFullName } from '@helpers/formatters';
import {
    Form,
    type FormSchema,
    type FormValues,
    useFormSubmit,
} from '@ui/forms';

const URL_REGEX = /^https?:\/\/(?=.*\.[a-z]{2})[^\s$.?#].\S*$/i;
const MAX_FIELD_LENGTH = 191;

const createMaxLengthValidator = (
    fieldName: string,
    isRequired = false,
): z.ZodString | z.ZodOptional<z.ZodString> => {
    const baseValidator = z.string();
    const withLength = baseValidator.max(MAX_FIELD_LENGTH, {
        message: t`${fieldName} must contain at most ${MAX_FIELD_LENGTH} characters`,
    });

    return isRequired
        ? withLength.min(1, { message: t`${fieldName} is required` })
        : withLength.optional();
};

/**
 * Type for reviewer that might have label property or userData nested property.
 */
interface ReviewerWithLabelAndUserData {
    id: number;
    firstName?: string;
    lastName?: string;
    label?: string;
    userData?: {
        firstName?: string;
        lastName?: string;
        avatarUrl?: string | null;
        email?: string;
    };
    /**
     * Allow other properties that might exist on the reviewer object.
     */
    avatarUrl?: string | null;
    email?: string;
    drataTermsAgreedAt?: string;
    createdAt?: string;
    updatedAt?: string;
    entryId?: string;
    identities?: unknown[];
    backgroundChecks?: unknown[];
    roles?: unknown[];
    documents?: unknown[];
    jobTitle?: string;
}

/**
 * Safely extracts reviewer label with fallback to userData when primary values are empty.
 */
const getReviewerLabel = (reviewer: ReviewerWithLabelAndUserData): string => {
    if (reviewer.label) {
        return reviewer.label;
    }

    const primaryFullName = getFullName(reviewer.firstName, reviewer.lastName);

    if (primaryFullName.trim()) {
        return primaryFullName;
    }

    return getFullName(
        reviewer.userData?.firstName,
        reviewer.userData?.lastName,
    );
};

const FORM_ID = 'edit-application-form';

export const EditApplicationDetailsModal = observer(
    ({
        application,
        onConfirm,
        onCancel,
    }: {
        application: AccessReviewPeriodApplicationResponseDto;
        onConfirm: (values: FormValues) => void;
        onCancel: () => void;
    }): React.JSX.Element => {
        const { name, websiteUrl, reviewers, source, id } = application;
        const {
            hasNextPage: hasMoreUsers,
            isFetching: isFetchingUsers,
            isLoading: isLoadingUsers,
            onFetchUsers,
            options,
        } = sharedUsersInfiniteController;

        const { formRef, triggerSubmit } = useFormSubmit();
        const showAdditionalFields = source === 'MANUALLY_ADDED';

        const formSchema: FormSchema = {
            reviewers: {
                type: 'combobox',
                label: t`Reviewers`,
                initialValue: reviewers.map((reviewer) => ({
                    ...reviewer,
                    value: reviewer.id.toString(),
                    id: reviewer.id.toString(),
                    label: getReviewerLabel(
                        reviewer as ReviewerWithLabelAndUserData,
                    ),
                })),
                isMultiSelect: true,
                isLoading: isFetchingUsers && isLoadingUsers,
                removeAllSelectedItemsLabel: t`Clear all`,
                getSearchEmptyState: () => t`No employees found`,
                loaderLabel: t`Loading`,
                placeholder: t`Search by name or email`,
                options,
                hasMore: hasMoreUsers,
                onFetchOptions: onFetchUsers,
                validator: z
                    .array(
                        z.object({
                            value: z.string(),
                        }),
                    )
                    .min(1, {
                        message: t`Reviewers field must have at least 1 item`,
                    }),
            },
            ...(showAdditionalFields && {
                websiteUrl: {
                    type: 'text',
                    label: t`Website URL`,
                    initialValue: websiteUrl || '',
                    validator: createMaxLengthValidator(
                        t`Website URL`,
                        false,
                    ).refine(
                        (value: string | undefined) =>
                            !value || URL_REGEX.test(value),
                        {
                            message: t`Website URL must be a valid URL`,
                        },
                    ),
                },
            }),
            ...(showAdditionalFields && {
                applicationName: {
                    type: 'text',
                    label: t`Application name`,
                    initialValue: name,
                    validator: createMaxLengthValidator(
                        t`Application name`,
                        true,
                    ),
                },
            }),
        };

        const handleSubmit = (values: FormValues) => {
            const formData = {
                name: showAdditionalFields ? values.applicationName : name,
                websiteUrl: values.websiteUrl,
                reviewers: values.reviewers,
                id: Number(id),
                source,
            };

            onConfirm(formData);
        };

        return (
            <>
                <Modal.Header
                    title={t`Edit ${name} details`}
                    description=""
                    closeButtonAriaLabel={t`Close`}
                    size="md"
                    data-id="edit-application-modal-header"
                    onClose={onCancel}
                />
                <Modal.Body size="md">
                    <Form
                        hasExternalSubmitButton
                        formId={FORM_ID}
                        ref={formRef}
                        data-testid="EditApplicationModal"
                        data-id="edit-application-modal-form"
                        schema={formSchema}
                        onSubmit={handleSubmit}
                    />
                </Modal.Body>
                <Modal.Footer
                    size="md"
                    rightActionStack={[
                        {
                            label: t`Cancel`,
                            level: 'secondary',
                            onClick: onCancel,
                        },
                        {
                            label: t`Save`,
                            level: 'primary',
                            onClick: () => {
                                triggerSubmit().catch((error) => {
                                    console.error(
                                        'Failed to submit form >',
                                        error,
                                    );
                                });
                            },
                        },
                    ]}
                />
            </>
        );
    },
);
