import { sharedAccessReviewActiveApplicationNotesController } from '@controllers/access-reviews';
import { snackbarController } from '@controllers/snackbar';
import { t } from '@globals/i18n/macro';
import { observer, runInAction } from '@globals/mobx';
import { UtilitiesNotesBaseComponent } from './utilities-notes-base-component';

export const UtilitiesNotesForActiveApplicationComponent = observer(
    (): React.JSX.Element => {
        const {
            list: notes,
            isReadOnly,
            isUploading,
            createNote,
            updateNote,
            deleteNote,
            downloadNoteAttachment,
        } = sharedAccessReviewActiveApplicationNotesController;

        return (
            <UtilitiesNotesBaseComponent
                notes={notes}
                data-id="ZFuoioNg"
                isLoading={isUploading}
                maxNoteCharacters={191}
                isReadOnly={isReadOnly}
                labels={{
                    title: t`Internal notes`,
                    subtitle: t`Add any feedback or questions you want to track for this application. Reviewers will receive an email notification. These messages are not shared with auditors.`,
                    commentLabel: t`New note`,
                    emptyStateTitle: t`No notes yet`,
                    emptyStateDescription: t`Add notes to track feedback, questions or important details for this application.`,
                    readOnlyEmptyStateDescription: t`Follow any feedback or questions about this application. These messages are not shared with auditors.`,
                }}
                attachments={{
                    showAddAttachment: 'field',
                    acceptedFormats: [
                        'pdf',
                        'docx',
                        'odt',
                        'xlsx',
                        'ods',
                        'pptx',
                        'odp',
                        'gif',
                        'jpeg',
                        'png',
                        'jpg',
                    ],
                    allowMultipleFiles: true,
                }}
                onCreate={(values, onSuccess) => {
                    runInAction(() => {
                        createNote({
                            comment: values.comment || '',
                            files: values['files[]'] ?? [],
                        });
                        onSuccess?.();
                    });
                }}
                onUpdate={(noteId, values) => {
                    runInAction(() => {
                        updateNote(noteId, {
                            comment: values.comment || '',
                            files: values['files[]'] ?? [],
                            filesToDelete: values.filesToDelete,
                        });
                    });
                }}
                onDelete={(noteId) => {
                    runInAction(() => {
                        deleteNote(noteId);
                    });
                }}
                onDownloadAttachment={(noteFileId: string, noteId?: string) => {
                    if (!noteId) {
                        snackbarController.addSnackbar({
                            id: `download-note-attachment-missing-data`,
                            props: {
                                title: t`Missing data to download attachment`,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });

                        throw new Error(t`Missing data to download attachment`);
                    }

                    runInAction(() => {
                        downloadNoteAttachment(noteFileId, noteId);
                    });
                }}
            />
        );
    },
);
