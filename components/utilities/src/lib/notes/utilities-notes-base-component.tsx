import { isEmpty, noop } from 'lodash-es';
import { EmptyState } from '@cosmos/components/empty-state';
import type { SupportedFormat } from '@cosmos/components/file-upload';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { dimension8x, dimension72x } from '@cosmos/constants/tokens';
import type { NoteResponseDto } from '@globals/api-sdk/types';
import { sharedCurrentUserController } from '@globals/current-user';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getFullName } from '@helpers/formatters';
import { UtilitiesCommentComponent } from './comments/utilities-notes-comment-component';
import { NoteCommentForm } from './comments/utilities-notes-comment-form-component';
import type {
    MessageNoteResponseDto,
    NoteCreateDto,
    NoteUpdateDto,
} from './utilities-notes-create-dto-types';

export const UtilitiesNotesBaseComponent = observer(
    ({
        notes = [],
        hasSecondaryInput = false,
        labels = {
            title: t`Notes`,
            subtitle: t`Add any feedback or questions you want to track for this. These messages are not shared with auditors.`,
            commentLabel: t`New note`,
            emptyStateTitle: t`No notes yet`,
            emptyStateDescription: t`Add notes to track feedback, questions or important details.`,
            readOnlyEmptyStateDescription: t`Follow any feedback or questions. These messages are not shared with auditors.`,
            addButton: t`Add note`,
            saveButton: t`Save note`,
            editTooltip: t`Edit note`,
            deleteTooltip: t`Delete note`,
            cancelConfirmationBody: t`If you leave now, your note changes will be lost.`,
        },
        maxNoteCharacters = 768,
        onCreate = noop,
        onUpdate = noop,
        onDelete = noop,
        onCancel = noop,
        onDownloadAttachment = noop,
        onUpdateReadStatus = noop,
        isReadOnly = false,
        attachments = {
            showAddAttachment: 'modal',
            acceptedFormats: ['jpeg', 'png'],
            useSimpleAttachments: false,
            includeAttachmentTitle: true,
            includeAttachmentCreationDate: true,
        },
        enableAuditorOnlyEditing = false,
        showUnreadIndicators = false,
        enableReadStatusTracking = false,
        isLoading = false,
    }: {
        notes: NoteResponseDto[];
        hasSecondaryInput?: boolean;
        maxNoteCharacters?: number;
        isLoading?: boolean;
        onCreate?: (values: NoteCreateDto, onSuccess?: () => void) => void;
        onUpdate?: (noteId: string, values: NoteUpdateDto) => void;
        onDelete?: (noteId: string) => void;
        onCancel?: () => void;
        onDownloadAttachment?: (noteFileId: string, noteId?: string) => void;
        onUpdateReadStatus?: (noteId: string, hasBeenRead: boolean) => void;
        isReadOnly?: boolean;
        enableAuditorOnlyEditing?: boolean;
        showUnreadIndicators?: boolean;
        enableReadStatusTracking?: boolean;
        attachments?: {
            showAddAttachment?: 'modal' | 'field';
            acceptedFormats?: SupportedFormat[];
            useSimpleAttachments?: boolean;
            includeAttachmentTitle?: boolean;
            includeAttachmentCreationDate?: boolean;
            allowMultipleFiles?: boolean;
        };
        labels?: {
            title?: string;
            subtitle?: string;
            commentLabel?: string;
            emptyStateTitle?: string;
            emptyStateDescription?: string;
            readOnlyEmptyStateDescription?: string;
            addButton?: string;
            saveButton?: string;
            editTooltip?: string;
            deleteTooltip?: string;
            cancelConfirmationBody?: string;
        };
    }): React.JSX.Element => {
        const isOwner = (note: NoteResponseDto): boolean => {
            const currentUser = sharedCurrentUserController.user;

            if (!currentUser) {
                return false;
            }

            // For auditor-only editing mode, check if the author is an auditor AND the current user is the owner
            if (enableAuditorOnlyEditing) {
                const messageNote = note as MessageNoteResponseDto;

                // Compare using entryId (string UUID) which matches the authorId from the message
                return (
                    messageNote.owner.authorIsAuditor === true &&
                    note.owner.entryId === currentUser.entryId
                );
            }

            // For regular mode, check if the current user is the owner of the note
            return note.owner.id === currentUser.id;
        };

        const emptyStateTitle = labels.emptyStateTitle || t`No notes yet`;
        const readOnlyEmptyStateDescription =
            labels.readOnlyEmptyStateDescription ||
            t`Follow any feedback or questions. These messages are not shared with auditors.`;
        const emptyStateDescription =
            labels.emptyStateDescription ||
            t`Add notes to track feedback, questions or important details.`;

        return (
            <Stack
                direction="column"
                height="100%"
                width="100%"
                data-testid="UtilitiesNotesComponent"
                data-id="9329QCnO"
                py="xl"
                pl="xl"
                pr="2xl"
                overflowY="auto"
                gap="lg"
            >
                <Stack gap="2x" direction="column" pb="lg">
                    <Text size="300" type="title">
                        {labels.title}
                    </Text>
                    <Text size="100">{labels.subtitle}</Text>
                </Stack>
                <Stack width="100%" py="3xl" direction="column">
                    {isReadOnly || (
                        <NoteCommentForm
                            comment=""
                            isLoading={isLoading}
                            attachments={[]}
                            source=""
                            maxNoteCharacters={maxNoteCharacters}
                            hasSource={false}
                            editMode={false}
                            commentLabel={labels.commentLabel}
                            labels={{
                                confirmationButton: labels.addButton,
                            }}
                            attachmentConfig={{
                                showAddAttachment:
                                    attachments.showAddAttachment,
                                acceptedFormats: attachments.acceptedFormats,
                                useSimpleAttachments:
                                    attachments.useSimpleAttachments,
                                includeAttachmentTitle:
                                    attachments.includeAttachmentTitle,
                                includeAttachmentCreationDate:
                                    attachments.includeAttachmentCreationDate,
                                allowMultipleFiles:
                                    attachments.allowMultipleFiles,
                            }}
                            onCancel={onCancel}
                            onSubmit={(values, onSuccess) => {
                                const fileMetadata = values.files.map(
                                    (fileItem) => ({
                                        name:
                                            fileItem.name ||
                                            fileItem.file.file.name,
                                        originalFile: fileItem.file.file.name,
                                        creationDate:
                                            fileItem.creationDate ||
                                            new Date().toISOString(),
                                    }),
                                );
                                const updatedNote: NoteCreateDto = {
                                    comment: values.comment,
                                    'files[]': values.files.map(
                                        (f) => f.file.file,
                                    ),
                                    fileMetadata,
                                };

                                onCreate(updatedNote, onSuccess);
                            }}
                        />
                    )}
                </Stack>

                {(() => {
                    if (isEmpty(notes) && isLoading) {
                        // is loading and no notes
                        return (
                            <Stack direction="column" gap="lg">
                                <Skeleton
                                    key={`note-skeleton-loading`}
                                    width="100%"
                                    barHeight={dimension8x}
                                    barCount={2}
                                    data-id={`note-skeleton-unique-id`}
                                />
                            </Stack>
                        );
                    }

                    if (isEmpty(notes) && !isLoading) {
                        return (
                            <EmptyState
                                data-id="utilities-notes-empty-state"
                                title={emptyStateTitle}
                                description={
                                    isReadOnly
                                        ? readOnlyEmptyStateDescription
                                        : emptyStateDescription
                                }
                            />
                        );
                    }

                    return (
                        <Stack
                            direction="column"
                            overflow="scroll"
                            minHeight={dimension72x}
                            data-id="y1aOKFuf"
                        >
                            {notes.map((note, index) => {
                                const hasDivider = notes.length - 1 !== index;
                                const canEdit = isOwner(note);

                                return (
                                    <UtilitiesCommentComponent
                                        key={`${note.id}-comment`}
                                        id={note.id}
                                        formId={`${note.id}-note-form-id`}
                                        value={note.comment}
                                        createdAt={note.createdAt}
                                        imgSrc={note.owner.avatarUrl ?? ''}
                                        isReadOnly={isReadOnly || !canEdit}
                                        hasDivider={hasDivider}
                                        data-id={`${note.id}-utility-note-comment`}
                                        hasSecondaryInput={hasSecondaryInput}
                                        attachments={note.noteFiles}
                                        canEdit={canEdit}
                                        commentFieldLabel="Edit Note"
                                        maxNoteCharacters={maxNoteCharacters}
                                        defaultEditLabel={labels.editTooltip}
                                        defaultDeleteLabel={
                                            labels.deleteTooltip
                                        }
                                        labels={{
                                            confirmationButton:
                                                labels.saveButton,
                                            editTooltip: labels.editTooltip,
                                            deleteTooltip: labels.deleteTooltip,
                                            cancelConfirmationBody:
                                                labels.cancelConfirmationBody,
                                        }}
                                        attachmentConfig={{
                                            shouldShowAddAttachment:
                                                attachments.showAddAttachment,
                                            acceptedFormats:
                                                attachments.acceptedFormats,
                                            useSimpleAttachments:
                                                attachments.useSimpleAttachments,
                                            includeAttachmentTitle:
                                                attachments.includeAttachmentTitle,
                                            includeAttachmentCreationDate:
                                                attachments.includeAttachmentCreationDate,
                                            allowMultipleFiles:
                                                attachments.allowMultipleFiles,
                                        }}
                                        enableReadStatusTracking={
                                            showUnreadIndicators
                                        }
                                        identityName={getFullName(
                                            note.owner.firstName,
                                            note.owner.lastName,
                                        )}
                                        hasBeenRead={
                                            enableReadStatusTracking
                                                ? ((
                                                      note as MessageNoteResponseDto
                                                  ).hasBeenRead ?? false)
                                                : true
                                        }
                                        onChange={noop}
                                        onDownloadAttachment={
                                            onDownloadAttachment
                                        }
                                        onSave={(updateNote: NoteUpdateDto) => {
                                            onUpdate(note.id, updateNote);
                                        }}
                                        onDelete={() => {
                                            onDelete(note.id);
                                        }}
                                        onUpdateReadStatus={
                                            enableReadStatusTracking &&
                                            (() => {
                                                // In auditor-only editing mode, only show read status controls for messages from opposite user type
                                                if (enableAuditorOnlyEditing) {
                                                    const messageNote =
                                                        note as MessageNoteResponseDto;
                                                    const currentUserIsAuditor =
                                                        sharedCurrentUserController.hasAuditorToken;

                                                    // Don't show mark as read buttons for messages from same user type
                                                    if (
                                                        messageNote.owner
                                                            .authorIsAuditor ===
                                                        currentUserIsAuditor
                                                    ) {
                                                        return false;
                                                    }
                                                }

                                                return true;
                                            })()
                                                ? (
                                                      noteId: string,
                                                      hasBeenRead: boolean,
                                                  ) => {
                                                      onUpdateReadStatus(
                                                          noteId,
                                                          hasBeenRead,
                                                      );
                                                  }
                                                : undefined
                                        }
                                    />
                                );
                            })}
                        </Stack>
                    );
                })()}
            </Stack>
        );
    },
);
