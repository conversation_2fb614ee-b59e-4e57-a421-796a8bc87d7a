import { activeAccessReviewCompletedDetailsController } from '@controllers/access-reviews';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { UtilitiesNotesBaseComponent } from './utilities-notes-base-component';

export const UtilitiesNotesForCompletedApplicationOnCompletedReviewComponent =
    observer((): React.JSX.Element => {
        const { notes } = activeAccessReviewCompletedDetailsController;

        return (
            <UtilitiesNotesBaseComponent
                isReadOnly
                notes={notes}
                data-id="zBsh_UOr"
                labels={{
                    title: t`Internal notes`,
                    subtitle: t`Follow any feedback or questions about this application. These notes are not shared with auditors.`,
                    commentLabel: t`New note`,
                    emptyStateTitle: t`No notes yet`,
                    emptyStateDescription: t`Add notes to track feedback, questions or important details for this application.`,
                    readOnlyEmptyStateDescription: t`Follow any feedback or questions about this application. These messages are not shared with auditors.`,
                }}
            />
        );
    });
