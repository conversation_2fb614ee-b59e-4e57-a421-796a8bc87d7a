import { sharedControlNotesController } from '@controllers/controls';
import { sharedEventsNotesController } from '@controllers/events-details';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { UtilitiesNotesBaseComponent } from './utilities-notes-base-component';

export const UtilitiesNotesForControlsComponent = observer(
    (): React.JSX.Element => {
        const { list: notes, isLoading } = sharedControlNotesController;

        return (
            <UtilitiesNotesBaseComponent
                notes={notes}
                data-id="zBsh_UOr"
                attachments={{}}
                isLoading={isLoading}
                labels={{
                    title: 'Internal notes',
                    subtitle:
                        'Add any feedback or questions you want to track for controls. Control owners will receive an email notification. These messages are not shared with auditors.',
                    commentLabel: t`New note`,
                    emptyStateTitle: t`No notes yet`,
                    emptyStateDescription: t`Add notes to track feedback, questions or important details for this control.`,
                    readOnlyEmptyStateDescription: t`Follow any feedback or questions about this control. These messages are not shared with auditors.`,
                }}
                onCreate={action((values, onSuccess) => {
                    sharedControlNotesController.createNote(values, onSuccess);
                })}
                onUpdate={action((noteId, values) => {
                    sharedControlNotesController.updateNote(noteId, values);
                })}
                onDelete={action((noteId: string) => {
                    sharedControlNotesController.deleteNote(noteId);
                })}
                onDownloadAttachment={action((noteFileId: string) => {
                    sharedEventsNotesController.downloadNoteAttachment(
                        noteFileId,
                    );
                })}
            />
        );
    },
);
