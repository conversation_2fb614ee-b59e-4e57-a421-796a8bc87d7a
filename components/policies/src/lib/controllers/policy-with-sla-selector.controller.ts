import { isEmpty } from 'lodash-es';
import type {
    IObjectSelectorController,
    ObjectItem,
    ObjectSelectorConfig,
} from '@components/object-selector';
import { DEFAULT_PAGE_SIZE } from '@cosmos/components/datatable';
import { policiesControllerGetActivePoliciesWithSlaInfiniteOptions } from '@globals/api-sdk/queries';
import type { ActivePoliciesDataWithSlaResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedInfiniteQuery } from '@globals/mobx';

/**
 * Controller implementation for policy selection.
 * Implements the IObjectSelectorController interface for POLICY objects.
 */
export class PolicyWithSLASelectorController
    implements IObjectSelectorController<ActivePoliciesDataWithSlaResponseDto>
{
    // ===== OBSERVABLE STATE =====

    _selectedItems: ObjectItem<ActivePoliciesDataWithSlaResponseDto>[] = [];
    _searchTerm = '';
    _config: ObjectSelectorConfig | null = null;

    /**
     * ObservedInfiniteQuery for fetching policies.
     */
    policiesQuery = new ObservedInfiniteQuery(
        policiesControllerGetActivePoliciesWithSlaInfiniteOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    // ===== COMPUTED PROPERTIES =====

    get availableItems(): ObjectItem<ActivePoliciesDataWithSlaResponseDto>[] {
        const policiesData =
            this.policiesQuery.data?.pages
                .flatMap((page) => page?.data ?? [])
                .filter(Boolean) ?? [];

        return policiesData.map(this.mapPolicyToObjectItem);
    }

    get selectedItems(): ObjectItem<ActivePoliciesDataWithSlaResponseDto>[] {
        if (!isEmpty(this.availableItems)) {
            return this._selectedItems.map((selectedItem) => {
                const matchingPolicy = this.availableItems.find(
                    (availableItem) => availableItem.id === selectedItem.id,
                );

                return matchingPolicy ?? selectedItem;
            });
        }

        return this._selectedItems;
    }

    get searchTerm(): string {
        return this._searchTerm;
    }

    get isLoading(): boolean {
        return this.policiesQuery.isLoading;
    }

    get hasError(): boolean {
        return Boolean(this.policiesQuery.error);
    }

    get errorMessage(): string | undefined {
        return this.policiesQuery.error?.message;
    }

    get hasNextPage(): boolean {
        return this.policiesQuery.hasNextPage;
    }

    get selectedCountText(): string {
        const selectedCount = this._selectedItems.length;

        return selectedCount === 1
            ? t`1 Policy selected`
            : t`${selectedCount} Policies selected`;
    }

    get modalTitle(): string {
        if (!this._config) {
            return '';
        }

        return this._config.modal.title;
    }

    get isConfirmDisabled(): boolean {
        if (!this._config) {
            return true;
        }

        return isEmpty(this._selectedItems);
    }

    // ===== ACTIONS =====

    initialize(
        config: ObjectSelectorConfig,
        defaultSelectedItems?: ObjectItem[],
    ): void {
        this._config = config;
        if (defaultSelectedItems) {
            this._selectedItems =
                defaultSelectedItems as ObjectItem<ActivePoliciesDataWithSlaResponseDto>[];
        } else if (config.defaultSelectedItems) {
            this._selectedItems =
                config.defaultSelectedItems as ObjectItem<ActivePoliciesDataWithSlaResponseDto>[];
        } else {
            this._selectedItems = [];
        }
        this._searchTerm = '';

        // Load initial data
        this.loadItems();
    }

    loadItems(): void {
        if (!this._config) {
            return;
        }

        const queryParams = this.buildQueryParams();

        this.policiesQuery.load(queryParams);
    }

    loadNextPage(): void {
        if (this.hasNextPage && !this.isLoading) {
            this.policiesQuery.nextPage();
        }
    }

    search(searchTerm: string): void {
        this._searchTerm = searchTerm;
        // Reload with new search term
        this.loadItems();
    }

    setSelectedItems(
        items: ObjectItem<ActivePoliciesDataWithSlaResponseDto>[],
    ): void {
        // Ensure uniqueness by id
        const uniqueItemsMap = new Map<
            string,
            ObjectItem<ActivePoliciesDataWithSlaResponseDto>
        >();

        for (const item of items) {
            uniqueItemsMap.set(item.id, item);
        }

        this._selectedItems = [...uniqueItemsMap.values()];
    }

    addSelectedItems(
        items: ObjectItem<ActivePoliciesDataWithSlaResponseDto>[],
    ): void {
        const newItems = items.filter(
            (item) =>
                !this._selectedItems.some(
                    (existing) => existing.id === item.id,
                ),
        );

        this._selectedItems = [...this._selectedItems, ...newItems];
    }

    removeSelectedItem(itemId: string): void {
        this._selectedItems = this._selectedItems.filter(
            (item) => item.id !== itemId,
        );
    }

    clearSelectedItems(): void {
        this._selectedItems = [];
    }

    reset(): void {
        this._selectedItems = [];
        this._searchTerm = '';
        this._config = null;
        this.policiesQuery.unload();
    }

    // ===== PRIVATE METHODS =====

    buildQueryParams(): Parameters<typeof this.policiesQuery.load>[0] {
        if (!this._config) {
            return {};
        }

        return {
            query: {
                page: 1,
                limit: DEFAULT_PAGE_SIZE,
                q: this._searchTerm,
                ...(this._config.filters?.excludeIds && {
                    excludeIds: this._config.filters.excludeIds.map(Number),
                }),
                ...this._config.filters?.customFilters,
            },
        };
    }

    mapPolicyToObjectItem = (
        policy: ActivePoliciesDataWithSlaResponseDto,
    ): ObjectItem<ActivePoliciesDataWithSlaResponseDto> => {
        // ActivePoliciesDataWithSlaResponseDto doesn't have version property
        const publishedDateStr = t`Active`;

        // ActivePoliciesDataWithSlaResponseDto doesn't have currentOwner property
        const fallbackText = policy.name.charAt(0).toUpperCase();

        return {
            id: String(policy.id),
            value: String(policy.id),
            label: policy.name,
            description: publishedDateStr,
            objectType: 'POLICY',
            objectData: policy,
            avatar: {
                fallbackText,
                imgAlt: t`Policy`,
            },
        };
    };
}

/**
 * Shared singleton instance of the PolicySelectorController.
 */
export const sharedPolicyWithSLASelectorController =
    new PolicyWithSLASelectorController();
