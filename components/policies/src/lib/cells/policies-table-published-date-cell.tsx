import { isNil } from 'lodash-es';
import { DateTime } from '@cosmos-lab/components/date-time';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import type { PoliciesTableCellProps } from '../types/policies.types';

export const PoliciesTablePublishedDateCell = ({
    row,
}: PoliciesTableCellProps): React.JSX.Element => {
    const { version } = row.original;

    if (isNil(version.publishedAt)) {
        return <EmptyValue label="-" />;
    }

    const { publishedAt } = version;

    return (
        <DateTime
            date={publishedAt}
            format="table"
            data-testid="PoliciesTablePublishedDateCell"
            data-id="qT5meIEu"
        />
    );
};
