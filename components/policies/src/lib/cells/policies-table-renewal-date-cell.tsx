import { isNil } from 'lodash-es';
import { Icon } from '@cosmos/components/icon';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Tooltip } from '@cosmos/components/tooltip';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import { formatDate } from '@helpers/date-time';
import type { PoliciesTableCellProps } from '../types/policies.types';

export const PoliciesTableRenewalDateCell = ({
    row,
}: PoliciesTableCellProps): React.JSX.Element => {
    const { version, versionPolicyStatus } = row.original;
    const { renewalDate } = version;

    if (isNil(version)) {
        return <EmptyValue label="-" />;
    }

    return (
        <span data-testid="PoliciesTableRenewalDateCell" data-id="XAUvkdaV">
            <Tooltip
                text={formatDate('table', renewalDate)}
                data-testid="PoliciesTableRenewalDateCell"
                data-id="Na6d_lWp"
            >
                <Stack gap="xs">
                    {versionPolicyStatus.showIcon && (
                        <Icon
                            name={versionPolicyStatus.name}
                            colorScheme={versionPolicyStatus.colorScheme}
                        />
                    )}
                    <Text
                        type={versionPolicyStatus.allowBold ? 'title' : 'body'}
                        colorScheme={versionPolicyStatus.colorScheme}
                        size={versionPolicyStatus.showIcon ? '100' : '200'}
                    >
                        {formatDate('table', renewalDate)}
                    </Text>
                </Stack>
            </Tooltip>
        </span>
    );
};
