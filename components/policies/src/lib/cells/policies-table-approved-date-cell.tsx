import { isNil } from 'lodash-es';
import { Text } from '@cosmos/components/text';
import { DateTime } from '@cosmos-lab/components/date-time';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import { Trans } from '@globals/i18n/macro';
import type { PoliciesTableCellProps } from '../types/policies.types';

export const PoliciesTableApprovedDateCell = ({
    row,
}: PoliciesTableCellProps): React.JSX.Element => {
    const { version } = row.original;
    const { approvedAt, publishedAt, clientType } = version;

    if (isNil(version)) {
        return <EmptyValue label="-" />;
    }

    if ((publishedAt || approvedAt) && clientType === 'BAMBOO_HR') {
        return (
            <Text>
                <Trans>Approved in Bamboo HR</Trans>
            </Text>
        );
    }

    if (isNil(approvedAt)) {
        return isNil(publishedAt) ? (
            <EmptyValue label="-" />
        ) : (
            <Text>
                <Trans>No approval required</Trans>
            </Text>
        );
    }

    return (
        <DateTime
            date={approvedAt}
            format="table"
            data-testid="PoliciesTableApprovedDateCell"
            data-id="qT5meIEu"
        />
    );
};
