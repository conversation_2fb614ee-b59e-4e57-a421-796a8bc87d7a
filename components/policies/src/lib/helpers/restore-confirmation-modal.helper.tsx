import { isEmpty, isNil } from 'lodash-es';
import { modalController } from '@controllers/modal';
import { Box } from '@cosmos/components/box';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t, Trans } from '@globals/i18n/macro';

const POLICY_RESTORE_CONFIRMATION_MODAL_ID =
    'restore-policy-confirmation-modal';

const defaultBody = (policyName: string) => (
    <Text allowBold data-testid="defaultBody" data-id="7VmCJnfv">
        <Trans>
            Are you sure you want to restore <strong>{policyName}</strong>?
        </Trans>
    </Text>
);

const getRestoreReplacementDirectTestsMessage = (
    policyName: string,
    replacedBy: string | null | undefined,
) =>
    isNil(replacedBy) ? (
        <Text allowBold>
            <Trans>
                Restoring the <strong>{policyName}</strong> will restore its
                automated tests:
            </Trans>
        </Text>
    ) : (
        <Text allowBold>
            <Trans>
                Restoring the <strong>{policyName}</strong> will remove it from{' '}
                <strong>{replacedBy}</strong> list of replaced policies and
                restore its automated tests:
            </Trans>
        </Text>
    );

const getRestoreReplacementIndirectTestsMessage = (policyName: string) => (
    <Text
        data-testid="getRestoreReplacementIndirectTestsMessage"
        data-id="dvO_I97E"
    >
        <Trans>
            These test(s) will be restored due to the SLA attached to $
            {policyName}.
        </Trans>
    </Text>
);

export const closeRestoreConfirmationModal = (): void => {
    modalController.closeModal(POLICY_RESTORE_CONFIRMATION_MODAL_ID);
};

export const openRestoreConfirmationModal = ({
    onConfirm,
    onCancel,
    isCustomPolicy,
    directTests,
    indirectTests,
    policyName,
    replacedBy,
}: {
    onConfirm: () => void;
    onCancel: () => void;
    isCustomPolicy: boolean;
    directTests: string[];
    indirectTests: string[];
    policyName: string;
    replacedBy: string | null | undefined;
}): void => {
    const body = defaultBody(policyName);
    const directTestsMessage = getRestoreReplacementDirectTestsMessage(
        policyName,
        replacedBy,
    );
    const indirectTestsMessage =
        getRestoreReplacementIndirectTestsMessage(policyName);
    const size =
        !isCustomPolicy && (!isEmpty(directTests) || !isEmpty(indirectTests))
            ? 'md'
            : 'sm';

    modalController.openModal({
        id: POLICY_RESTORE_CONFIRMATION_MODAL_ID,
        content: () => (
            <>
                <Modal.Header
                    title={t`Restore policy?`}
                    closeButtonAriaLabel={t`Close modal`}
                    onClose={onCancel}
                />
                <Modal.Body>
                    <Stack gap="lg" direction="column">
                        {body}
                        {!isCustomPolicy && !isEmpty(directTests) && (
                            <>
                                {directTestsMessage}
                                <Box px="8x">
                                    <ul>
                                        {directTests.map(
                                            (directText: string) => (
                                                <li
                                                    key={directText}
                                                    data-id="B6P7qYDV"
                                                >
                                                    <Text type="body">
                                                        {directText}
                                                    </Text>
                                                </li>
                                            ),
                                        )}
                                    </ul>
                                </Box>
                            </>
                        )}
                        {isCustomPolicy && !isEmpty(indirectTests) && (
                            <>
                                {indirectTestsMessage}
                                <Box px="8x">
                                    <ul>
                                        {indirectTests.map(
                                            (indirectText: string) => (
                                                <li
                                                    key={indirectText}
                                                    data-id="S0axDmvX"
                                                >
                                                    <Text type="body">
                                                        {indirectText}
                                                    </Text>
                                                </li>
                                            ),
                                        )}
                                    </ul>
                                </Box>
                            </>
                        )}
                    </Stack>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Cancel`,
                            level: 'secondary',
                            onClick: onCancel,
                        },
                        {
                            label: t`Restore`,
                            level: 'primary',
                            onClick: onConfirm,
                        },
                    ]}
                />
            </>
        ),
        size,
        centered: true,
        disableClickOutsideToClose: false,
        onClose: () => onCancel,
    });
};

export const closeConfirmationModal = (): void => {
    modalController.closeModal(POLICY_RESTORE_CONFIRMATION_MODAL_ID);
};
