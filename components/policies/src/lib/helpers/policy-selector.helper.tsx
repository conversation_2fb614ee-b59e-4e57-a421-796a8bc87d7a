import {
    type IObjectSelectorController,
    type ObjectItem,
    ObjectSelector,
    type ObjectSelectorCallbacks,
    type ObjectSelectorConfig,
} from '@components/object-selector';
import { modalController } from '@controllers/modal';
import { sharedPoliciesExternalFilesController } from '../controllers/policies-external-files.controller';
import { sharedPolicySelectorController } from '../controllers/policy-selector.controller';
import { sharedPolicyWithSLASelectorController } from '../controllers/policy-with-sla-selector.controller';

/**
 * Helper function to open a policy selector modal.
 */
export const openPolicySelector = <T = unknown>(options: {
    config: Omit<ObjectSelectorConfig, 'type'>;
    callbacks: ObjectSelectorCallbacks<T>;
    controller?: IObjectSelectorController<T>;
}): void => {
    const config: ObjectSelectorConfig = {
        ...options.config,
        type: 'POLICY',
    };

    const controller = options.controller ?? sharedPolicySelectorController;

    // Initialize the controller before opening the modal
    // Pass default selected items if provided in config
    controller.initialize(
        config,
        config.defaultSelectedItems as ObjectItem<T>[] | undefined,
    );

    modalController.openModal({
        id: config.modal.id,
        size: config.modal.size ?? 'lg',
        disableClickOutsideToClose: config.modal.disableClickOutsideToClose,
        content: () => (
            <ObjectSelector
                config={config}
                callbacks={options.callbacks as ObjectSelectorCallbacks}
                controller={controller as IObjectSelectorController}
                data-id={`policy-selector-with-la-${config.modal.id}`}
                data-testid="ObjectSelector"
            />
        ),
    });
};

/**
 * Helper function to open a policy with SLA selector modal.
 */
export const openPolicyWithSLASelector = <T = unknown>(options: {
    config: Omit<ObjectSelectorConfig, 'type'>;
    callbacks: ObjectSelectorCallbacks<T>;
    controller?: IObjectSelectorController<T>;
}): void => {
    const config: ObjectSelectorConfig = {
        ...options.config,
        type: 'POLICY',
    };

    const controller =
        options.controller ?? sharedPolicyWithSLASelectorController;

    // Initialize the controller before opening the modal
    // Pass default selected items if provided in config
    controller.initialize(
        config,
        config.defaultSelectedItems as ObjectItem<T>[] | undefined,
    );

    modalController.openModal({
        id: config.modal.id,
        size: config.modal.size ?? 'lg',
        disableClickOutsideToClose: config.modal.disableClickOutsideToClose,
        content: () => (
            <ObjectSelector
                config={config}
                callbacks={options.callbacks as ObjectSelectorCallbacks}
                controller={controller as IObjectSelectorController}
                data-id={`policy-selector-${config.modal.id}`}
                data-testid="ObjectSelector"
            />
        ),
    });
};

/**
 * Helper function to open a policy with SLA selector modal.
 */
export const openPolicyExternalFileSelector = <T = unknown>(options: {
    config: Omit<ObjectSelectorConfig, 'type'>;
    callbacks: ObjectSelectorCallbacks<T>;
    controller?: IObjectSelectorController<T>;
}): void => {
    const config: ObjectSelectorConfig = {
        ...options.config,
        type: 'EXTERNAL_POLICY_FILE',
    };

    const controller =
        options.controller ?? sharedPoliciesExternalFilesController;

    // Initialize the controller before opening the modal
    controller.initialize(config);

    modalController.openModal({
        id: config.modal.id,
        size: config.modal.size ?? 'lg',
        content: () => (
            <ObjectSelector
                config={config}
                callbacks={options.callbacks as ObjectSelectorCallbacks}
                controller={controller as IObjectSelectorController}
                data-id={`policy-external-file-selector-${config.modal.id}`}
                data-testid="ObjectSelectorExternalFile"
            />
        ),
    });
};
