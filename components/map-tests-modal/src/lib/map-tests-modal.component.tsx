import { useCallback, useMemo, useState } from 'react';
import { sharedMonitoringMapTestsMutationController } from '@controllers/monitoring-details';
import { sharedMonitorsInfiniteController } from '@controllers/monitors';
import { Box } from '@cosmos/components/box';
import { ComboboxField } from '@cosmos/components/combobox-field';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t, Trans } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { observer } from '@globals/mobx';
import { sharedMapAutomatedTestsModel } from '@models/controls';
import { closeMapAutomatedTestModal } from '../helpers/map-tests-modal.helper';

export const MapAutomatedTestsModal = observer(
    ({
        formId,
        modalId,
        excludeControlId,
        onSave,
    }: {
        formId: string;
        modalId: string;
        onSave?: () => Promise<void>;
        excludeControlId?: number;
    }): React.JSX.Element => {
        const currentSelectedMonitors = useMemo(() => {
            return sharedMapAutomatedTestsModel.getCurrentSelectedMonitors();
        }, []);

        const [localSelectedMonitors, setLocalSelectedMonitors] = useState<
            ListBoxItemData[]
        >(currentSelectedMonitors);

        const handleGetSearchEmptyState = () => {
            return (
                <Text
                    type="body"
                    size="200"
                    colorScheme="neutral"
                    align="center"
                    data-id="uStWbSZD"
                    data-testid="handleGetSearchEmptyState"
                >
                    <Trans>No tests found matching your search criteria.</Trans>
                </Text>
            );
        };

        const handleOnChange = useCallback(
            (selected: ListBoxItemData[] | ListBoxItemData) => {
                const selectedArray = Array.isArray(selected)
                    ? selected
                    : [selected];

                setLocalSelectedMonitors(selectedArray);
            },
            [],
        );

        const getSelectedTestsDescription = useCallback(() => {
            const count = localSelectedMonitors.length;

            return count === 1
                ? t`1 Test selected`
                : t`${count} Tests selected`;
        }, [localSelectedMonitors]);

        const handleFetchOptions = useCallback(
            ({
                search,
                increasePage,
            }: {
                search?: string;
                increasePage?: boolean;
            }) => {
                if (increasePage) {
                    sharedMonitorsInfiniteController.loadNextPage({
                        search,
                    });
                } else {
                    sharedMonitorsInfiniteController.search(
                        search || '',
                        excludeControlId,
                    );
                }
            },
            [excludeControlId],
        );

        return (
            <>
                <Modal.Header
                    title={t`Map automated test`}
                    closeButtonAriaLabel={t`Close map automated tests modal`}
                    description={getSelectedTestsDescription()}
                    onClose={() => {
                        closeMapAutomatedTestModal(modalId);
                    }}
                />
                <Modal.Body>
                    <Stack direction="column" gap="lg">
                        <Box>
                            <ComboboxField
                                isMultiSelect
                                label={t`Search test`}
                                formId={formId}
                                getSearchEmptyState={handleGetSearchEmptyState}
                                loaderLabel={t`Loading...`}
                                name="automatedTests"
                                removeAllSelectedItemsLabel={t`Clear all`}
                                placeholder={t`Search by test name...`}
                                data-id="map-automated-tests-combobox"
                                defaultSelectedOptions={localSelectedMonitors}
                                options={
                                    sharedMonitorsInfiniteController.monitorsComboboxOptions
                                }
                                isLoading={
                                    sharedMonitorsInfiniteController.isLoading
                                }
                                hasMore={
                                    sharedMonitorsInfiniteController.hasNextPage
                                }
                                getRemoveIndividualSelectedItemClickLabel={({
                                    itemLabel,
                                }) => t`Remove ${itemLabel}`}
                                onChange={handleOnChange}
                                onFetchOptions={handleFetchOptions}
                            />
                        </Box>
                    </Stack>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Close`,
                            level: 'secondary',
                            cosmosUseWithCaution_isDisabled:
                                sharedMonitoringMapTestsMutationController.isMapping,
                            onClick: () => {
                                closeMapAutomatedTestModal(modalId);
                            },
                        },
                        {
                            isLoading:
                                sharedMonitoringMapTestsMutationController.isMapping,
                            label: t`Confirm`,
                            level: 'primary',
                            onClick: () => {
                                sharedMonitorsInfiniteController.addSelectedMonitors(
                                    localSelectedMonitors,
                                );

                                if (!onSave) {
                                    closeMapAutomatedTestModal(modalId);

                                    return;
                                }

                                onSave()
                                    .then(() => {
                                        closeMapAutomatedTestModal(modalId);
                                    })
                                    .catch((error) => {
                                        logger.error({
                                            message: 'Failed to map tests',
                                            errorObject: {
                                                message: error,
                                                statusCode: '500',
                                            },
                                        });
                                    });
                            },
                        },
                    ]}
                />
            </>
        );
    },
);
