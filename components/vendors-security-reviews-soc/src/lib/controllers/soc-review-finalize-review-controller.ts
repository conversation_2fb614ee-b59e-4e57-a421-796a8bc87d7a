import { isNil, uniqueId } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import {
    sharedVendorsDetailsController,
    sharedVendorsSecurityReviewDetailsController,
    sharedVendorsSecurityReviewDocumentsController,
} from '@controllers/vendors';
import {
    vendorsControllerGetSocReportReviewOptions,
    vendorsControllerGetVendorReportViewOptions,
} from '@globals/api-sdk/queries';
import type {
    VendorIntegrationResponseDto,
    VendorModifyRequestDto,
    VendorResponseDto,
    VendorSecurityReviewDocumentResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedQuery, toJS, when } from '@globals/mobx';
import { fileNameDate } from '@helpers/date-time';
import { downloadBlob } from '@helpers/download-file';
import { resetRenewalDate } from '../helpers/reset-renewal-date.helper';
import type { SocReviewFormValuesType } from '../types/soc-review-form-values.type';
import { sharedSOCReviewSaveProgressController } from './soc-review-save-progress-controller';

class SOCReviewFinalizeReviewController {
    #vendorSecurityReviewDocumentTypeDocumentId: number | null = null;
    isExecutingFinalize = false;

    constructor() {
        makeAutoObservable(this);
    }

    getVendorReportReviewQuery = new ObservedQuery(
        vendorsControllerGetVendorReportViewOptions,
    );

    downloadSocReportQuery = new ObservedQuery(
        vendorsControllerGetSocReportReviewOptions,
    );

    get isFinalizing(): boolean {
        return this.isExecutingFinalize;
    }

    get isLoadingVendorReportReview(): boolean {
        return this.getVendorReportReviewQuery.isLoading;
    }

    waitForControllersToLoad = async (): Promise<void> => {
        await when(
            () =>
                !sharedVendorsSecurityReviewDetailsController.isLoading &&
                !sharedVendorsSecurityReviewDocumentsController.isLoading,
        );
    };

    /**
     * Finalizes SOC review using async/await pattern following the API flow
     * Uses await when() to properly wait for each operation to complete.
     */
    finalizeReviewAsync = async (
        formValues: SocReviewFormValuesType,
        decision: 'APPROVED' | 'APPROVED_WITH_CONDITIONS' | 'REJECTED',
        note?: string,
    ): Promise<void> => {
        if (this.isExecutingFinalize) {
            return; // Prevent concurrent executions
        }

        this.isExecutingFinalize = true;

        try {
            await sharedSOCReviewSaveProgressController.saveProgressAsync(
                formValues,
                true,
            );

            const vendorReviewId = this.getVendorReviewId();

            this.getVendorReportReviewQuery.load({
                path: { id: vendorReviewId },
            });

            await when(() => !this.getVendorReportReviewQuery.isLoading);

            const vendorReportReviewData = this.getVendorReportReviewQuery.data;

            const { securityReviewId, vendorId } = this.validateRequiredIds(
                vendorReportReviewData,
            );

            await this.updateSecurityReviewStatusStep(
                securityReviewId,
                decision,
                note,
            );

            await this.updateVendorDetailsStep(vendorId);

            await this.createSecurityReviewDocumentStep(
                securityReviewId,
                vendorReportReviewData?.vendorDocumentId,
            );

            await this.finalizeAndRefreshStep();

            snackbarController.addSnackbar({
                id: uniqueId('soc-finalize-review-success'),
                props: {
                    title: t`Review finalized`,
                    description: t`Your SOC review has been finalized successfully.`,
                    severity: 'success',
                    closeButtonAriaLabel: t`Close`,
                },
            });
        } catch (error) {
            snackbarController.addSnackbar({
                id: uniqueId(`${String(error)}-soc-finalize-review-error`),
                props: {
                    title: t`Failed to finalize review`,
                    description: t`There was an error finalizing your review. Please try again.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });
        } finally {
            this.isExecutingFinalize = false;
        }
    };

    getVendorReviewId = (): number => {
        const vendorReviewId =
            sharedSOCReviewSaveProgressController.saveVendorReportReviewMutation
                .response?.id;

        if (!vendorReviewId) {
            throw new Error('Failed to get review ID from save response');
        }

        return vendorReviewId;
    };

    validateRequiredIds = (
        vendorReportReviewData:
            | { vendorDocumentId?: number }
            | null
            | undefined,
    ) => {
        const securityReviewId =
            sharedVendorsSecurityReviewDetailsController.securityReviewDetails
                ?.id;
        const vendorId = sharedVendorsDetailsController.vendorDetails?.id;

        if (
            !vendorReportReviewData?.vendorDocumentId ||
            !securityReviewId ||
            !vendorId
        ) {
            throw new Error('Failed to get required IDs for finalization');
        }

        return { securityReviewId, vendorId };
    };

    updateSecurityReviewStatusStep = async (
        securityReviewId: number,
        decision: 'APPROVED' | 'APPROVED_WITH_CONDITIONS' | 'REJECTED',
        note?: string,
    ): Promise<void> => {
        await sharedSOCReviewSaveProgressController.updateVendorSecurityReviewStatusMutation.mutateAsync(
            {
                path: { id: securityReviewId },
                body: {
                    status: 'COMPLETED',
                    decision,
                    note: isNil(note) ? undefined : note,
                },
            },
        );
    };

    updateVendorDetailsStep = async (vendorId: number): Promise<void> => {
        const { vendorDetails, updateDetailsMutation, vendorDetailsQuery } =
            sharedVendorsDetailsController;

        vendorDetailsQuery.invalidate();
        await when(() => !vendorDetailsQuery.isLoading);

        if (vendorDetails) {
            const shouldUpdateVendor =
                this.shouldUpdateVendorDetails(vendorDetails);

            if (shouldUpdateVendor) {
                const vendorUpdateData =
                    this.buildVendorUpdateData(vendorDetails);

                await updateDetailsMutation.mutateAsync({
                    path: { id: vendorId },
                    body: vendorUpdateData as VendorModifyRequestDto,
                });
            }
        }
    };

    shouldUpdateVendorDetails = (vendorDetails: VendorResponseDto): boolean => {
        // Check if vendor is prospective or has no renewal status
        const isPotentialVendor = vendorDetails.status === 'PROSPECTIVE';
        const isNoRenewalStatus =
            isNil(vendorDetails.renewalDateStatus) ||
            vendorDetails.renewalDateStatus === 'NO_RENEWAL';

        // Only update vendor if not prospective and has renewal status
        return !isPotentialVendor && !isNoRenewalStatus;
    };

    buildVendorUpdateData = (vendorDetails: VendorResponseDto) => {
        const { shouldRestartRenewalDate } = resetRenewalDate(vendorDetails);

        // Transform vendor response to request format following existing patterns
        return {
            ...vendorDetails,
            shouldRestartRenewalDate,
            integrations:
                vendorDetails.integrations?.map(
                    (integration: VendorIntegrationResponseDto) =>
                        integration.id,
                ) ?? [],
            userId: vendorDetails.user?.id,
            contact: vendorDetails.contact
                ? {
                      id: vendorDetails.contact.id,
                      firstName: vendorDetails.contact.firstName,
                      lastName: vendorDetails.contact.lastName,
                      email: vendorDetails.contact.email,
                      jobTitle: vendorDetails.contact.jobTitle,
                  }
                : null,
            documents: vendorDetails.documents,
        };
    };

    createSecurityReviewDocumentStep = async (
        securityReviewId: number,
        vendorDocumentId: number | undefined,
    ): Promise<void> => {
        const vendorSecurityReviewDocument =
            sharedVendorsSecurityReviewDocumentsController.securityReviewDocumentsQuery.data?.data.find(
                (doc) => doc.type === 'DOCUMENT',
            );

        // Create additional security review document if needed
        if (!vendorSecurityReviewDocument?.documentId && vendorDocumentId) {
            await sharedVendorsSecurityReviewDocumentsController.createSecurityReviewDocumentMutation.mutateAsync(
                {
                    path: { id: securityReviewId },
                    body: {
                        documentId: vendorDocumentId,
                        type: 'DOCUMENT',
                    },
                },
            );

            this.#vendorSecurityReviewDocumentTypeDocumentId =
                toJS(
                    sharedVendorsSecurityReviewDocumentsController
                        .createSecurityReviewDocumentMutation.response
                        ?.documentId,
                ) ?? null;
        }
    };

    finalizeAndRefreshStep = async (): Promise<void> => {
        // Query invalidations and refresh
        sharedVendorsSecurityReviewDocumentsController.securityReviewDocumentsQuery.invalidate();
        sharedVendorsSecurityReviewDetailsController.securityReviewDetailsQuery.invalidate();

        await this.waitForControllersToLoad();
    };

    downloadReportAsync = async (
        securityReviewId?: number,
        vendorId?: number,
    ): Promise<void> => {
        try {
            this.validateDownloadParameters(securityReviewId, vendorId);

            await this.loadSecurityReviewDocuments();

            const documents = this.getSecurityReviewDocuments();

            const document = this.findSocReportDocument(documents);

            const documentId = this.getDocumentId(document);

            await this.downloadDocumentFile(
                vendorId as number,
                documentId,
                document?.name,
            );
        } catch (error) {
            snackbarController.addSnackbar({
                id: uniqueId(`${String(error)}-soc-download-error`),
                props: {
                    title: t`Unable to download Document.`,
                    description: t`There was an error downloading the document. Please try again.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });
        }
    };

    validateDownloadParameters = (
        securityReviewId?: number,
        vendorId?: number,
    ): void => {
        if (!securityReviewId || !vendorId) {
            throw new Error('Missing data ID');
        }
    };

    loadSecurityReviewDocuments = async (): Promise<void> => {
        // TODO: need api endpoint to filter by type and vendor document type
        sharedVendorsSecurityReviewDocumentsController.securityReviewDocumentsQuery.invalidate();

        await when(
            () =>
                !sharedVendorsSecurityReviewDocumentsController
                    .securityReviewDocumentsQuery.isLoading,
        );
    };

    getSecurityReviewDocuments =
        (): VendorSecurityReviewDocumentResponseDto[] => {
            return (
                sharedVendorsSecurityReviewDocumentsController
                    .securityReviewDocumentsQuery.data?.data ?? []
            );
        };

    findSocReportDocument = (
        documents: VendorSecurityReviewDocumentResponseDto[],
    ): VendorSecurityReviewDocumentResponseDto | undefined => {
        return documents.find(
            (doc: VendorSecurityReviewDocumentResponseDto) =>
                doc.type === 'DOCUMENT' &&
                doc.vendorDocument?.type === 'COMPLIANCE_REPORT_REVIEW',
        );
    };

    getDocumentId = (
        document: VendorSecurityReviewDocumentResponseDto | undefined,
    ): number => {
        const documentId =
            document?.documentId ??
            this.#vendorSecurityReviewDocumentTypeDocumentId;

        if (!documentId) {
            throw new Error('Missing document id');
        }

        return documentId;
    };

    downloadDocumentFile = async (
        vendorId: number,
        documentId: number,
        documentName?: string,
    ): Promise<void> => {
        // Get download URL
        this.downloadSocReportQuery.load({
            path: { id: vendorId, docId: documentId },
        });

        await when(() => !this.downloadSocReportQuery.isLoading);

        const downloadData = this.downloadSocReportQuery.data;

        if (!downloadData) {
            throw new Error('Unable to get file');
        }

        const name = isNil(documentName)
            ? `summary-security-review-${fileNameDate()}`
            : documentName;

        downloadBlob(downloadData as unknown as Blob, `${name}.pdf`);
    };
}

export const sharedSOCReviewFinalizeReviewController =
    new SOCReviewFinalizeReviewController();
