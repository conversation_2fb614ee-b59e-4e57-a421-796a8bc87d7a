import { uniqueId } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import {
    sharedVendorsDetailsController,
    sharedVendorsSecurityReviewDetailsController,
    sharedVendorsSecurityReviewDocumentsController,
} from '@controllers/vendors';
import {
    vendorsControllerSaveVendorReportReviewMutation,
    vendorsControllerUpdateVendorSecurityReviewMutation,
    vendorsControllerUpdateVendorSecurityReviewStatusMutation,
} from '@globals/api-sdk/queries';
import type {
    VendorReviewResponseDto,
    VendorSecurityReviewRequestDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { transformSocFormValuesToAPI } from '../helpers/soc-form-data-transformer.helper';
import { buildSocPageTitle } from '../helpers/soc-page-title.helper';
import { socReviewFormSchemaModel } from '../models/soc-review-form-schema.model';
import type { SocReviewFormValuesType } from '../types/soc-review-form-values.type';
import { sharedSOCBridgeLetterMutationController } from './soc-bridge-letter-mutation-controller';

const getShouldUpdateTitle = (
    formValues: SocReviewFormValuesType,
    originalData?: VendorReviewResponseDto | null,
): boolean => {
    const hasReportValueChanged =
        formValues.complianceScope?.certification &&
        formValues.complianceScope.certification.id !== originalData?.socReport;

    const hasReportTypeChanged =
        formValues.complianceScope?.scopeType !==
        socReviewFormSchemaModel.getInitialScopeType();

    return hasReportValueChanged || hasReportTypeChanged;
};

class SOCReviewSaveProgressController {
    constructor() {
        makeAutoObservable(this);
    }

    saveVendorReportReviewMutation = new ObservedMutation(
        vendorsControllerSaveVendorReportReviewMutation,
    );

    updateVendorSecurityReviewMutation = new ObservedMutation(
        vendorsControllerUpdateVendorSecurityReviewMutation,
    );

    updateVendorSecurityReviewStatusMutation = new ObservedMutation(
        vendorsControllerUpdateVendorSecurityReviewStatusMutation,
    );

    get isSaving(): boolean {
        return (
            this.saveVendorReportReviewMutation.isPending ||
            this.updateVendorSecurityReviewMutation.isPending
        );
    }

    /**
     * Saves SOC review form progress using async/await pattern
     * Uses await when() to properly wait for bridge letter operations and controller loading states.
     */
    saveProgressAsync = async (
        formValues: SocReviewFormValuesType,
        isCompleted = false,
    ): Promise<void> => {
        const vendorId = sharedVendorsDetailsController.vendorDetails?.id;

        if (!vendorId) {
            return;
        }

        try {
            await this.saveVendorReportReview(formValues, isCompleted);

            await this.putSOCSecurityReview(formValues);

            // Handle bridge letter changes (this method handles its own async operations)
            sharedSOCBridgeLetterMutationController.handleBridgeLetterChanges(
                formValues.complianceScope?.bridgeLetter ?? [],
                socReviewFormSchemaModel.getInitialBridgeLetterDocuments(),
            );

            // Wait for bridge letter operations to complete
            await when(() => !sharedSOCBridgeLetterMutationController.isBusy);

            sharedVendorsSecurityReviewDocumentsController.securityReviewDocumentsQuery.invalidate();
            sharedVendorsSecurityReviewDetailsController.securityReviewDetailsQuery.invalidate();

            await this.waitForControllersToLoad();

            await this.createSecurityReviewDocument();

            if (!isCompleted) {
                snackbarController.addSnackbar({
                    id: uniqueId('soc-save-progress-success'),
                    props: {
                        title: t`Progress saved`,
                        description: t`Your SOC review progress has been saved successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            }
        } catch (error) {
            snackbarController.addSnackbar({
                id: uniqueId(`${String(error)}-soc-save-progress-error`),
                props: {
                    title: t`Failed to save progress`,
                    description: t`There was an error saving your progress. Please try again.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });
        }
    };

    saveVendorReportReview = async (
        formValues: SocReviewFormValuesType,
        isCompleted = false,
    ): Promise<void> => {
        const vendorId = sharedVendorsDetailsController.vendorDetails?.id;

        if (!vendorId) {
            throw new Error('Failed to get vendor ID');
        }

        const { vendorReview: originalData } =
            sharedVendorsSecurityReviewDocumentsController.reviewDocument ?? {};

        const formattedValues = transformSocFormValuesToAPI(
            formValues,
            originalData,
        );

        await this.saveVendorReportReviewMutation.mutateAsync({
            path: { id: vendorId },
            body: {
                id: originalData?.id,
                isCompleted,
                ...formattedValues,
            },
        });

        if (this.saveVendorReportReviewMutation.hasError) {
            throw new Error('Failed to save vendor report review');
        }
    };

    putSOCSecurityReview = async (
        formValues: SocReviewFormValuesType,
    ): Promise<void> => {
        const { vendorReview: originalData } =
            sharedVendorsSecurityReviewDocumentsController.reviewDocument ?? {};

        const isNewReviewer =
            formValues.reviewerInfo?.reviewer &&
            formValues.reviewerInfo.reviewer.id !==
                socReviewFormSchemaModel.getRequesterUserAsListBoxItem()?.id;

        const shouldUpdateTitle = getShouldUpdateTitle(
            formValues,
            originalData,
        );

        // Update title and requester if needed
        if (isNewReviewer || shouldUpdateTitle) {
            await this.updateSOCSecurityReviewTitleAndRequesterAsync(
                formValues,
                isNewReviewer,
                shouldUpdateTitle,
            );
        }
    };

    updateSOCSecurityReviewTitleAndRequesterAsync = async (
        formValues: SocReviewFormValuesType,
        isNewReviewer?: boolean,
        shouldUpdateTitle?: boolean,
    ): Promise<void> => {
        const securityReview =
            sharedVendorsSecurityReviewDetailsController.securityReviewDetails;

        if (!securityReview) {
            return;
        }

        const reviewData: VendorSecurityReviewRequestDto = {
            requesterUserId: securityReview.requesterUser?.id,
            reviewDeadlineAt: securityReview.reviewDeadlineAt,
            securityReviewType: securityReview.type,
            securityReviewStatus: securityReview.status,
        };

        if (isNewReviewer) {
            reviewData.requesterUserId = Number(
                formValues.reviewerInfo?.reviewer?.id,
            );
        }

        if (shouldUpdateTitle) {
            const newTitle = buildSocPageTitle({
                socReport: formValues.complianceScope?.certification?.value,
                socReportType1:
                    formValues.complianceScope?.scopeType === 'type1',
                socReportType2:
                    formValues.complianceScope?.scopeType === 'type2',
                reviewDate:
                    sharedVendorsSecurityReviewDetailsController
                        .securityReviewDetails?.createdAt ?? undefined,
            });

            reviewData.title = newTitle;
        }

        await this.updateVendorSecurityReviewMutation.mutateAsync({
            path: { id: securityReview.id },
            body: reviewData,
        });
    };

    createSecurityReviewDocument = async (): Promise<void> => {
        const securityReviewId =
            sharedVendorsSecurityReviewDetailsController.securityReviewDetails
                ?.id;

        if (!securityReviewId) {
            throw new Error('Failed to get security review ID');
        }

        const hasDocumentTypeReviewAlready =
            sharedVendorsSecurityReviewDocumentsController.reviewDocument;

        if (hasDocumentTypeReviewAlready) {
            return;
        }

        const vendorReview = this.saveVendorReportReviewMutation.response;

        if (!vendorReview?.id) {
            return;
        }

        await sharedVendorsSecurityReviewDocumentsController.createReviewDocument(
            vendorReview.id,
            securityReviewId,
        );
    };

    waitForControllersToLoad = async (): Promise<void> => {
        await when(
            () =>
                !sharedVendorsSecurityReviewDetailsController.isLoading &&
                !sharedVendorsSecurityReviewDocumentsController.isLoading,
        );
    };
}

export const sharedSOCReviewSaveProgressController =
    new SOCReviewSaveProgressController();
