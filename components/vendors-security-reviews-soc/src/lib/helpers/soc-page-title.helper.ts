import { isEmpty } from 'lodash-es';
import type { VendorReviewResponseDto } from '@globals/api-sdk/types';
import { formatDate } from '@helpers/date-time';
import {
    getReportCertificationLabel,
    getScopeInternalTypeLabel,
} from './soc-constants-helpers.helper';

export const buildSocPageTitle = ({
    socReport,
    socReportType1,
    socReportType2,
    reviewDate,
}: {
    socReport?: VendorReviewResponseDto['socReport'];
    socReportType1?: boolean;
    socReportType2?: boolean;
    reviewDate?: string | Date;
}): string => {
    let reportLabel = '';
    let scopeTypeLabel = '';

    // TODO: translate in the API
    const suffix = 'report review';

    if (socReport) {
        reportLabel = getReportCertificationLabel(socReport);
    }

    // Handle SOC 3 special case - no type specification needed
    if (socReport === 'SOC_3') {
        const formattedDateSoc3 = reviewDate
            ? formatDate('sentence', reviewDate)
            : '';

        return `${formattedDateSoc3} ${reportLabel} ${suffix}`.trim();
    }

    if (socReportType1) {
        scopeTypeLabel = getScopeInternalTypeLabel('type1');
    } else if (socReportType2) {
        scopeTypeLabel = getScopeInternalTypeLabel('type2');
    }

    if (isEmpty(reportLabel)) {
        reportLabel = 'SOC';
    }

    const formattedDate = reviewDate ? formatDate('sentence', reviewDate) : '';

    const titleParts = [
        formattedDate,
        reportLabel,
        scopeTypeLabel,
        suffix,
    ].filter((part) => !isEmpty(part));

    return titleParts.join(' ');
};
