import type { ListBoxItemData } from '@cosmos/components/list-box';
import type { VendorReviewResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import type { YesNoOption } from '../types/soc-review-form-values.type';
import type {
    ComplianceReportOpinion,
    EndUserControlDocumented,
    SubserviceOrgsUseInclusiveMethods,
} from '../types/vendors-security-reviews-soc.types';

/**
 * Converts a labels object to select options format.
 */
export const labelsToOptions = (
    labels: Readonly<Record<string, string>>,
): ListBoxItemData[] =>
    Object.entries(labels).map(([value, label], index) => {
        return {
            id: `${value}-${index}`,
            label,
            value,
        };
    });

/**
 * Converts enum values to select options using a label function.
 */
export const enumValuesToSelectOptions = <T extends string>(
    enumValues: T[],
    getLabelFunction: (value: T) => string,
): ListBoxItemData[] => {
    return enumValues.map((value, index) => {
        return {
            id: `${value}-${index}`,
            label: getLabelFunction(value),
            value,
        };
    });
};

/**
 * Gets the display label for a SOC report certification type.
 */
export const getReportCertificationLabel = (
    label: VendorReviewResponseDto['socReport'],
): string => {
    switch (label) {
        case 'SOC_1': {
            return 'SOC 1';
        }
        case 'SOC_2': {
            return 'SOC 2';
        }
        case 'SOC_3': {
            return 'SOC 3';
        }
        default: {
            return '';
        }
    }
};

/**
 * Gets the display label for trust service criteria based on string value.
 */
export const getTrustServiceCriteriaLabel = (value: string): string => {
    switch (value) {
        case '1': {
            return t`Availability`;
        }
        case '2': {
            return t`Confidentiality`;
        }
        case '3': {
            return t`Security`;
        }
        case '4': {
            return t`Privacy`;
        }
        case '5': {
            return t`Processing Integrity`;
        }
        default: {
            return '';
        }
    }
};

/**
 * Gets the string constant for trust service criteria based on numeric value.
 */
export const getTrustServiceCriteriaString = (value: number): string => {
    switch (value) {
        case 1: {
            return 'AVAILABILITY';
        }
        case 2: {
            return 'CONFIDENTIALITY';
        }
        case 3: {
            return 'SECURITY';
        }
        case 4: {
            return 'PRIVACY';
        }
        case 5: {
            return 'PROCESSING_INTEGRITY';
        }
        default: {
            return '';
        }
    }
};

/**
 * Converts string report opinion value to API enum value.
 */
export const getReportOpinionValue = (
    value: string,
): VendorReviewResponseDto['reportOpinion'] => {
    switch (value) {
        case '1': {
            return 'UNQUALIFIED';
        }
        case '2': {
            return 'QUALIFIED';
        }
        case '3': {
            return 'ADVERSE';
        }
        case '4': {
            return 'DISCLAIMER';
        }
        default: {
            throw new Error('Invalid report opinion value');
        }
    }
};

/**
 * Gets the display label for SOC report scope internal type.
 */
export const getScopeInternalTypeLabel = (value: string): string => {
    switch (value) {
        case 'type1': {
            return 'Type 1';
        }
        case 'type2': {
            return 'Type 2';
        }
        default: {
            return '';
        }
    }
};

/**
 * Gets the display label for Yes/No options.
 */
export const getYesNoLabel = (option: YesNoOption): string => {
    switch (option) {
        case 'YES': {
            return t`Yes`;
        }
        case 'NO': {
            return t`No`;
        }
        default: {
            return '';
        }
    }
};

/**
 * Gets the display label for subservice organizations using inclusive methods.
 */
export const getSubserviceOrgsUseInclusiveMethodsLabel = (
    option: SubserviceOrgsUseInclusiveMethods,
): string => {
    switch (option) {
        case 'YES': {
            return t`Yes`;
        }
        case 'NO': {
            return t`No`;
        }
        case 'NA': {
            return t`N/A`;
        }
        default: {
            return '';
        }
    }
};

/**
 * Gets the display label for end user control documented options.
 */
export const getEndUserControlDocumentedLabel = (
    option: EndUserControlDocumented,
): string => {
    switch (option) {
        case 'YES_TO_ALL': {
            return t`Yes to all`;
        }
        case 'NO_TO_ALL': {
            return t`No to all`;
        }
        default: {
            return '';
        }
    }
};

/**
 * Gets the display label for report opinion options.
 */
export const getReportOpinionLabel = (
    option: ComplianceReportOpinion,
): string => {
    switch (option) {
        case '1': {
            return t`Unqualified`;
        }
        case '2': {
            return t`Qualified`;
        }
        case '3': {
            return t`Adverse`;
        }
        case '4': {
            return t`Disclaimer`;
        }
        default: {
            return '';
        }
    }
};
