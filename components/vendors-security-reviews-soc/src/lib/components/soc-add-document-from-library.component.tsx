import { sharedVendorsProfileReportsAndDocumentsController } from '@controllers/vendors';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { Form, type FormValues } from '@ui/forms';

interface SocAddDocumentFromLibraryProps {
    formId: string;
    formRef: React.RefObject<HTMLFormElement>;
    onSubmit: (values: FormValues) => void;
}

export const SocAddDocumentFromLibrary = observer(
    ({
        formId,
        formRef,
        onSubmit,
    }: SocAddDocumentFromLibraryProps): React.JSX.Element => {
        const { options, hasNextPage, isFetching, onFetchDocuments } =
            sharedVendorsProfileReportsAndDocumentsController;

        return (
            <Form
                hasExternalSubmitButton
                formId={formId}
                data-id="soc-add-document-from-library-form"
                data-testid="soc-add-document-from-library-form"
                ref={formRef}
                schema={{
                    report: {
                        type: 'combobox',
                        options,
                        label: t`Select report`,
                        loaderLabel: t`Loading reports`,
                        getSearchEmptyState: () => t`No reports found`,
                        clearSelectedItemButtonLabel: t`Clear report`,
                        isLoading: isFetching,
                        hasMore: hasNextPage,
                        onFetchOptions: ({ search, increasePage }) => {
                            onFetchDocuments({
                                search,
                                increasePage,
                            });
                        },
                    },
                }}
                onSubmit={onSubmit}
            />
        );
    },
);
