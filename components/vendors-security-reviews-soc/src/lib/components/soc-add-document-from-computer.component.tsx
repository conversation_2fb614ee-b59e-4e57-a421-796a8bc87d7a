import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { Form, type FormValues } from '@ui/forms';
import {
    getUploadFileErrorMessages,
    VENDORS_ADD_SOC_REPORT_FROM_COMPUTER_FILES_ACCEPTED_FORMATS,
} from '@views/vendors-add-soc-report-modal';

interface SocAddDocumentFromComputerProps {
    formId: string;
    formRef: React.RefObject<HTMLFormElement>;
    onSubmit: (values: FormValues) => void;
}

export const SocAddDocumentFromComputer = observer(
    ({
        formId,
        formRef,
        onSubmit,
    }: SocAddDocumentFromComputerProps): React.JSX.Element => {
        return (
            <Form
                hasExternalSubmitButton
                formId={formId}
                data-id="soc-add-document-from-computer-form"
                data-testid="soc-add-document-from-computer-form"
                ref={formRef}
                schema={{
                    fileUpload: {
                        type: 'file',
                        label: t`File`,
                        errorCodeMessages: getUploadFileErrorMessages(),
                        innerLabel: t`Or drop file here`,
                        isMulti: false,
                        oneFileOnly: true,
                        removeButtonText: t`Remove file`,
                        selectButtonText: t`Upload file`,
                        acceptedFormats:
                            VENDORS_ADD_SOC_REPORT_FROM_COMPUTER_FILES_ACCEPTED_FORMATS,
                    },
                }}
                onSubmit={onSubmit}
            />
        );
    },
);
