import { useState } from 'react';
import {
    sharedVendorsSecurityReviewDetailsController,
    sharedVendorsSecurityReviewDocumentsController,
} from '@controllers/vendors';
import { Button } from '@cosmos/components/button';
import { Card } from '@cosmos/components/card';
import { RadioFieldGroup } from '@cosmos/components/radio-field-group';
import { Stack } from '@cosmos/components/stack';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { type FormValues, useFormSubmit } from '@ui/forms';
import { SocAddDocumentFromComputer } from './soc-add-document-from-computer.component';
import { SocAddDocumentFromLibrary } from './soc-add-document-from-library.component';

export const SocAddDocumentEmptyState = observer((): React.JSX.Element => {
    const { formRef: formDocumentsRef, triggerSubmit: triggerSubmitDocuments } =
        useFormSubmit();
    const { formRef: formUploadRef, triggerSubmit: triggerSubmitUpload } =
        useFormSubmit();

    const { isLoading, pdfDownloadUrl } =
        sharedVendorsSecurityReviewDocumentsController;
    const { securityReviewDetails } =
        sharedVendorsSecurityReviewDetailsController;
    const { currentWorkspace } = sharedWorkspacesController;
    const { currentVendorId } = sharedVendorsSecurityReviewDocumentsController;

    const [fileSource, setFileSource] = useState('documents');
    const [isSubmitting, setIsSubmitting] = useState(false);

    const isReadOnly = sharedFeatureAccessModel.isVendorAccessReadOnly;

    const handleSubmitDocuments = action((values: FormValues) => {
        if (!securityReviewDetails?.id || !currentWorkspace?.id) {
            return;
        }

        const document = values.report as FormValues;

        setIsSubmitting(true);

        sharedVendorsSecurityReviewDocumentsController.createNewSecurityReviewWithDocuments(
            Number(document.id),
            securityReviewDetails.id,
            currentWorkspace.id,
            'SOC_REPORT',
            'SOC_REPORT',
            currentVendorId,
        );
    });

    const handleSubmitUpload = action((values: FormValues) => {
        if (!securityReviewDetails?.id || !currentWorkspace?.id) {
            return;
        }

        setIsSubmitting(true);

        sharedVendorsSecurityReviewDocumentsController.uploadFileAndLinkToSecurityReview(
            values.fileUpload as File,
            securityReviewDetails.id,
            currentWorkspace.id,
            'SOC_REPORT',
            'SOC_REPORT',
            () => {
                setIsSubmitting(false);
            },
        );
    });

    const handleConfirm = action(() => {
        if (fileSource === 'documents') {
            triggerSubmitDocuments().catch(() => {
                setIsSubmitting(false);
            });
        } else {
            triggerSubmitUpload().catch(() => {
                setIsSubmitting(false);
            });
        }
    });

    const isProcessing = isLoading || isSubmitting;

    return (
        <>
            {!pdfDownloadUrl && (
                <Card
                    cardHeight="auto"
                    title={t`Upload SOC report file to review`}
                    data-id="i9_3HNuQ"
                    body={
                        <Stack direction="column" gap="xl">
                            <RadioFieldGroup
                                formId="soc-add-document-empty-state"
                                label={t`Select the source of your file`}
                                name="file-source"
                                value={fileSource}
                                cosmosUseWithCaution_forceOptionOrientation="vertical"
                                disabled={isReadOnly}
                                options={[
                                    {
                                        label: t`Reports and documents in Drata`,
                                        value: 'documents',
                                    },
                                    {
                                        label: t`Upload from computer`,
                                        value: 'upload',
                                    },
                                ]}
                                onChange={() => {
                                    if (!isReadOnly) {
                                        fileSource === 'documents'
                                            ? setFileSource('upload')
                                            : setFileSource('documents');
                                    }
                                }}
                            />

                            {fileSource === 'documents' ? (
                                <SocAddDocumentFromLibrary
                                    formId="soc-add-document-empty-state-documents"
                                    formRef={formDocumentsRef}
                                    onSubmit={handleSubmitDocuments}
                                />
                            ) : (
                                <SocAddDocumentFromComputer
                                    formId="soc-add-document-empty-state-upload"
                                    formRef={formUploadRef}
                                    onSubmit={handleSubmitUpload}
                                />
                            )}

                            <Button
                                label={t`Add SOC Report`}
                                level="primary"
                                colorScheme="primary"
                                isLoading={isProcessing}
                                disabled={isReadOnly || isProcessing}
                                style={{ alignSelf: 'center' }}
                                onClick={handleConfirm}
                            />
                        </Stack>
                    }
                />
            )}
        </>
    );
});
