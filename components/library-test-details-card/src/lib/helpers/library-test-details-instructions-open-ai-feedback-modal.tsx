import { modalController } from '@controllers/modal';
import { LibraryTestDetailsInstructionsAiFeedbackModal } from '../library-test-details-instructions-ai-feedback-modal';

export const openLibraryTestDetailsInstructionsAiFeedbackModal = (
    testId: number,
): void => {
    modalController.openModal({
        id: 'library-test-details-instructions-ai-feedback-modal',
        content: () => (
            <LibraryTestDetailsInstructionsAiFeedbackModal
                testId={testId}
                data-id="ys__MoOz"
                onClose={() => {
                    modalController.closeModal(
                        'library-test-details-instructions-ai-feedback-modal',
                    );
                }}
            />
        ),
        centered: true,
        disableClickOutsideToClose: true,
    });
};
