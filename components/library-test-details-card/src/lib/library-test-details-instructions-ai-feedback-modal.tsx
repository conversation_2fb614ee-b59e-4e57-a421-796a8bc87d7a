import type { default as React } from 'react';
import { sharedAIExecutionGroupFeedbackGroupsController } from '@controllers/ai-execution-group';
import { Icon } from '@cosmos/components/icon';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { Form, type FormSchema, useFormSubmit } from '@ui/forms';

interface AiFeedbackModalProps {
    /** The ID of the test to submit feedback for. */
    testId: number;
    /** Callback function to close the modal. */
    onClose: () => void;
}

const buildFormSchema = (): FormSchema => ({
    feedbackStatus: {
        type: 'choiceCardGroup',
        label: t`Please rate your experience`,
        choiceCardInputType: 'radio',
        initialValue: 'USEFUL',
        showDivider: true,
        options: [
            {
                value: 'USEFUL',
                label: t`positive`,
                slot: (
                    <Icon
                        name="ThumbsUp"
                        size="100"
                        colorScheme="success"
                        backgroundType="round"
                    />
                ),
            },
            {
                value: 'NOT_USEFUL',
                label: t`negative`,
                slot: (
                    <Icon
                        name="ThumbsDown"
                        size="100"
                        colorScheme="critical"
                        backgroundType="round"
                    />
                ),
            },
        ],
    },
    feedbackReason: {
        type: 'radioGroup',
        label: t`Why best describes the problem with this content?`,
        cosmosUseWithCaution_forceOptionOrientation: 'vertical',
        initialValue: 'NOT_HELPFUL',
        options: [
            {
                value: 'NOT_ACCURATE',
                label: t`This is not accurate`,
            },
            {
                value: 'NOT_HELPFUL',
                label: t`This does not add value`,
            },
            {
                value: 'OTHER',
                label: t`Other`,
            },
        ],
        shownIf: {
            fieldName: 'feedbackStatus',
            operator: 'equals',
            value: 'NOT_USEFUL',
        },
    },
    feedbackText: {
        type: 'textarea',
        label: t`Feedback`,
        maxCharacters: 400,
        rows: 3,
        isOptional: true,
    },
});

/**
 * Modal component for collecting AI feedback on library test instructions.
 * Allows users to submit positive or negative feedback with optional text comments.
 */
export const LibraryTestDetailsInstructionsAiFeedbackModal = observer(
    ({ onClose, testId }: AiFeedbackModalProps): React.JSX.Element => {
        const { isAnyOperationPending, submitFeedback } =
            sharedAIExecutionGroupFeedbackGroupsController;

        const { formRef, triggerSubmit } = useFormSubmit();

        // Use the controller's computed loading state
        const isLoading = isAnyOperationPending;

        const handleSubmitFeedback = (values: Record<string, unknown>) => {
            if (isLoading) {
                return;
            }

            submitFeedback({
                testId: String(testId),
                feedbackStatus: values.feedbackStatus as
                    | 'USEFUL'
                    | 'NOT_USEFUL',
                feedbackText: (values.feedbackText as string) || '',
                feedbackReason:
                    values.feedbackStatus === 'NOT_USEFUL' &&
                    values.feedbackReason
                        ? [
                              values.feedbackReason as
                                  | 'NOT_ACCURATE'
                                  | 'NOT_HELPFUL'
                                  | 'OTHER',
                          ]
                        : undefined,
                onSuccess: onClose,
            });
        };

        return (
            <Modal
                size="lg"
                data-id="library-test-details-instructions-ai-feedback-modal"
                data-testid="LibraryTestDetailsInstructionsAiFeedbackModal"
            >
                <Modal.Header
                    title={t`Provide feedback`}
                    closeButtonAriaLabel={t`Close AI feedback modal`}
                    onClose={onClose}
                />
                <Modal.Body>
                    <Form
                        hasExternalSubmitButton
                        ref={formRef}
                        formId="ai-feedback-form"
                        schema={buildFormSchema()}
                        data-id="ai-feedback-form"
                        onSubmit={handleSubmitFeedback}
                    />
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Send feedback`,
                            level: 'primary',
                            onClick: () => {
                                triggerSubmit().catch(() => {
                                    console.error('Failed to submit form');
                                });
                            },
                            disabled: isLoading,
                        },
                    ]}
                />
            </Modal>
        );
    },
);
