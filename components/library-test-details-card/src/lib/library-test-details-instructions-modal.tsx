import { useMemo } from 'react';
import { sharedAIExecutionGroupFeedbackGroupsController } from '@controllers/ai-execution-group';
import { activeLibraryTestController } from '@controllers/library-test';
import { Banner } from '@cosmos/components/banner';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { openLibraryTestDetailsInstructionsAiFeedbackModal } from './helpers/library-test-details-instructions-open-ai-feedback-modal';

// Constants
const INSTRUCTIONS_MARKER = 'Set up instructions:\n';

interface LibraryTestDetailsInstructionsModalProps {
    onClose: () => void;
}

/**
 * AI remedy descriptions always start with Set up instructions:
 * So, stripping that part out is safe.
 */
const getInstructionsText = (description: string): string => {
    const index = description.indexOf(INSTRUCTIONS_MARKER);

    return index >= 0
        ? description.slice(index + INSTRUCTIONS_MARKER.length)
        : description;
};

export const LibraryTestDetailsInstructionsModal = observer(
    ({
        onClose,
    }: LibraryTestDetailsInstructionsModalProps): React.JSX.Element => {
        const { id, remedyDescription } = activeLibraryTestController;
        const { loadExecutionGroups } =
            sharedAIExecutionGroupFeedbackGroupsController;

        const instructions = useMemo(
            () => getInstructionsText(remedyDescription),
            [remedyDescription],
        );

        const handleSubmitFeedback = () => {
            if (!id) {
                return;
            }
            // close modal before opening feedback modal
            onClose();
            loadExecutionGroups({
                processFeature: 'MONITOR_TEST_TEMPLATE_INSTRUCTIONS',
                featureIds: [String(id)],
            });
            openLibraryTestDetailsInstructionsAiFeedbackModal(id);
        };

        return (
            <Modal size="lg" data-id="library-test-details-instructions-modal">
                <Modal.Header
                    title={t`View instructions`}
                    closeButtonAriaLabel={t`Close instructions modal`}
                    onClose={onClose}
                />
                <Modal.Body>
                    <Stack gap="4x" direction="column" align="stretch">
                        <Banner
                            severity="ai"
                            title={t`This content was pre-generated by AI.`}
                            body={t`Please review carefully.`}
                            data-id="library-test-details-instructions-ai-banner"
                            action={
                                <Button
                                    label={t`Send feedback`}
                                    size="sm"
                                    level="secondary"
                                    data-id="library-test-details-instructions-ai-feedback-button"
                                    onClick={handleSubmitFeedback}
                                />
                            }
                        />
                        <Text
                            size="300"
                            type="title"
                            data-id="library-test-instructions-permissions-title"
                        >
                            {t`Permissions:`}
                        </Text>
                        {/* TODO: ENG-70882 */}
                        <Text
                            size="200"
                            data-id="library-test-instructions-permissions-placeholder"
                        >
                            {t`TODO`}
                        </Text>
                        <Text
                            size="300"
                            type="title"
                            data-id="library-test-instructions-title"
                        >
                            {t`Set up instructions:`}
                        </Text>
                        <Box
                            style={{ whiteSpace: 'pre-wrap' }}
                            data-id="library-test-instructions-content"
                        >
                            <Text size="200" as="span">
                                {instructions}
                            </Text>
                        </Box>
                    </Stack>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Close`,
                            level: 'primary',
                            onClick: onClose,
                        },
                    ]}
                />
            </Modal>
        );
    },
);
