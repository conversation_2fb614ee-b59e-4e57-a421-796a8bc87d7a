import { modalController } from '@controllers/modal';
import { action } from '@globals/mobx';
import { VENDOR_DELETE_SECURITY_REVIEW_FILE_MODAL_ID } from '../constants/vendor-current-security-review-files.constant';
import { VendorCurrentSecurityReviewDeleteFileConfirmationModal } from '../modals/vendor-current-security-review-delete-file-confirmation.modal';

export const openDeleteSecurityReviewFileModal = action((): void => {
    modalController.openModal({
        id: VENDOR_DELETE_SECURITY_REVIEW_FILE_MODAL_ID,
        content: () => (
            <VendorCurrentSecurityReviewDeleteFileConfirmationModal data-id="9UHeK8Lf" />
        ),
        centered: true,
        disableClickOutsideToClose: false,
        size: 'md',
    });
});
