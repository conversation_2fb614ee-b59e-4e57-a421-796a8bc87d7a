import { modalController } from '@controllers/modal';
import { sharedVendorsSecurityReviewFileController } from '@controllers/vendors';
import { Modal } from '@cosmos/components/modal';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { VENDOR_DELETE_SECURITY_REVIEW_FILE_MODAL_ID } from '../constants/vendor-current-security-review-files.constant';

const handleClose = () => {
    modalController.closeModal(VENDOR_DELETE_SECURITY_REVIEW_FILE_MODAL_ID);
};

export const VendorCurrentSecurityReviewDeleteFileConfirmationModal = observer(
    (): React.JSX.Element => {
        const { securityReviewDocument, deleteSecurityReviewDocument } =
            sharedVendorsSecurityReviewFileController;
        const docName = securityReviewDocument?.name ?? t`this`;

        const handleSubmit = () => {
            deleteSecurityReviewDocument();
        };

        return (
            <>
                <Modal.Header
                    title={t`Delete File`}
                    closeButtonAriaLabel={t`Close modal`}
                    onClose={handleClose}
                />
                <Modal.Body>
                    <Text
                        type="body"
                        data-id={`vendor-delete-security-review-file-modal-warning-text`}
                    >
                        {t`Confirm that you'd like to delete ${docName} file.`}
                    </Text>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Cancel`,
                            level: 'secondary',
                            onClick: handleClose,
                        },
                        {
                            label: t`Delete file`,
                            level: 'primary',
                            colorScheme: 'danger',
                            onClick: handleSubmit,
                        },
                    ]}
                />
            </>
        );
    },
);
