import { Button } from '@cosmos/components/button';
import { Grid } from '@cosmos/components/grid';
import { Stack } from '@cosmos/components/stack';
import { Divider } from '@cosmos-lab/components/divider';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { observer } from '@globals/mobx';

interface Props {
    onLike?: () => void;
    onDislike?: () => void;
    onCopy?: () => void;
}

export const VendorsSecurityReviewsAISummaryFeedbackComponent = observer(
    ({ onLike, onDislike, onCopy }: Props): React.JSX.Element => {
        const isReadOnly = sharedFeatureAccessModel.isVendorAccessReadOnly;

        return (
            <Grid
                /* This <Grid> is needed for the vertical divider */
                data-testid="VendorsSecurityReviewsAISummaryFeedbackComponent"
                data-id="OKaCkFj4"
            >
                <Stack align="center" gap="lg">
                    <Stack>
                        <Button
                            isIconOnly
                            startIconName="ThumbsUp"
                            label="Like"
                            level="tertiary"
                            data-testid="ai-summary-like-button"
                            disabled={isReadOnly}
                            onClick={onLike}
                        />
                        <Button
                            isIconOnly
                            startIconName="ThumbsDown"
                            label="Dislike"
                            level="tertiary"
                            data-testid="ai-summary-dislike-button"
                            disabled={isReadOnly}
                            onClick={onDislike}
                        />
                    </Stack>
                    <Divider orientation="vertical" />
                    <Button
                        isIconOnly
                        startIconName="Copy"
                        label="Copy"
                        level="tertiary"
                        data-testid="ai-summary-copy-button"
                        disabled={isReadOnly}
                        onClick={onCopy}
                    />
                </Stack>
            </Grid>
        );
    },
);
