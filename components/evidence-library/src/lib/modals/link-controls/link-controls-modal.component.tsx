import { isEmpty, isNil, size } from 'lodash-es';
import { useCallback, useState } from 'react';
import type { ObjectTypes } from '@components/map-controls-step';
import {
    type ControlListBoxItemData,
    sharedEvidenceMutationController,
    sharedLinkControlsController,
} from '@controllers/evidence-library';
import { modalController } from '@controllers/modal';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { ComboboxField } from '@cosmos/components/combobox-field';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { plural, t, Trans } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { useLocation } from '@remix-run/react';
import { LINK_CONTROLS_MODAL_ID } from './constants/link-controls-modal.constant';
import { getModalTitleByObject } from './helpers/get-modal-title-by-object.helper';

const FORM_ID = 'map-control-to-evidence-modal-form-id';

export const LinkControlsModal = observer(
    ({
        objectType,
        onConfirm,
        enableAuditFrameworkControls = false,
    }: {
        objectType: ObjectTypes;
        onConfirm?: (selectedControls: ControlListBoxItemData[]) => void;
        enableAuditFrameworkControls?: boolean;
    }): React.JSX.Element => {
        const { pathname } = useLocation();

        const [showConfirmation, setShowConfirmation] = useState(false);

        const {
            addControls,
            updateSelectedItems,
            clearSelectedItems,
            selectedListItems,
            controlOptions,
            auditFrameworkControlOptions,
            loadNextPage,
            isLoading: isControlsLoading,
            isAuditFrameworkControlsLoading,
            hasNextPage,
            hasAuditFrameworkNextPage,
        } = sharedLinkControlsController;
        const { isUpdating } = sharedEvidenceMutationController;

        const count = size(selectedListItems);

        // Use audit framework controls or regular controls based on the flag
        const currentControlOptions = enableAuditFrameworkControls
            ? auditFrameworkControlOptions
            : controlOptions;
        const currentIsLoading = enableAuditFrameworkControls
            ? isAuditFrameworkControlsLoading
            : isControlsLoading;
        const currentHasNextPage = enableAuditFrameworkControls
            ? hasAuditFrameworkNextPage
            : hasNextPage;

        const handleChange = useCallback(
            (value: ListBoxItemData[]) => {
                updateSelectedItems(value as ControlListBoxItemData[]);
            },
            [updateSelectedItems],
        );

        const openConfirmationModal = useCallback(() => {
            setShowConfirmation(true);
        }, []);

        const handleClose = useCallback(() => {
            clearSelectedItems();

            modalController.closeModal(LINK_CONTROLS_MODAL_ID);
        }, [clearSelectedItems]);

        /**
         * The way this file handles displaying the confirmation modal is a temporary solution and should not be replicated.
         *
         * A global solution will be provided and implemented in this file once available. See https://drata.atlassian.net/browse/ENG-72628.
         *
         * If that doesn't happen before you need to use a confirmation modal, and you want to use this pattern,
         * please confirm with Precogs in office hours before you do.
         *
         * Augment, DO NOT replicate anything you see in this file.
         */

        return (
            <>
                <Box
                    display={showConfirmation ? 'block' : 'none'}
                    aria-hidden={!showConfirmation}
                >
                    <Modal.Header
                        closeButtonAriaLabel={t`Close modal`}
                        title={t`Leave without saving new mappings?`}
                        onClose={() => {
                            setShowConfirmation(false);
                        }}
                    />

                    <Modal.Body>
                        <Text colorScheme="neutral">
                            <Trans>
                                If you create a new control now, your current
                                mapping selections will be discarded. Existing
                                mappings won&apos;t be affected.
                            </Trans>
                        </Text>
                    </Modal.Body>

                    <Modal.Footer
                        rightActionStack={[
                            {
                                label: t`Go back`,
                                level: 'secondary',
                                onClick: () => {
                                    setShowConfirmation(false);
                                },
                            },
                            {
                                label: t`Create control`,
                                level: 'primary',
                                colorScheme: 'primary',
                                href: `${pathname}/create`,
                            },
                        ]}
                    />
                </Box>

                <Box
                    display={showConfirmation ? 'none' : 'block'}
                    aria-hidden={showConfirmation}
                >
                    <Modal.Header
                        title={getModalTitleByObject(objectType)}
                        closeButtonAriaLabel={t`Close modal`}
                        description={plural(count, {
                            one: '# control selected',
                            other: '# controls selected',
                        })}
                        onClose={handleClose}
                    />

                    <Modal.Body>
                        <ComboboxField
                            isMultiSelect
                            label={t`Search control`}
                            formId={FORM_ID}
                            getSearchEmptyState={() => t`No controls found`}
                            loaderLabel={t`Loading...`}
                            name="controls"
                            removeAllSelectedItemsLabel={t`Clear all`}
                            placeholder={t`Search by name...`}
                            options={currentControlOptions}
                            isLoading={currentIsLoading}
                            hasMore={currentHasNextPage}
                            getRemoveIndividualSelectedItemClickLabel={
                                undefined
                            }
                            itemToString={(item) => {
                                if (isNil(item) || 'groupHeader' in item) {
                                    return '';
                                }

                                return `${(item.controlData as ControlListBoxItemData['controlData']).code} ${item.label}`;
                            }}
                            onFetchOptions={loadNextPage}
                            onChange={handleChange}
                        />

                        <Stack align="start" mt="2x">
                            {isEmpty(selectedListItems) ? (
                                <Button
                                    width="auto"
                                    label={t`Create control`}
                                    level="secondary"
                                    size="sm"
                                    href={`${pathname}/create`}
                                />
                            ) : (
                                <Button
                                    width="auto"
                                    label={t`Create control`}
                                    level="secondary"
                                    size="sm"
                                    onClick={openConfirmationModal}
                                />
                            )}
                        </Stack>
                    </Modal.Body>

                    <Modal.Footer
                        rightActionStack={[
                            {
                                label: t`Close`,
                                level: 'secondary',
                                onClick: handleClose,
                            },
                            {
                                label: t`Confirm`,
                                level: 'primary',
                                colorScheme: 'primary',
                                isLoading: isUpdating,
                                onClick: action(() => {
                                    if (onConfirm) {
                                        // Use custom callback with selected controls

                                        onConfirm(
                                            selectedListItems as ControlListBoxItemData[],
                                        );
                                    } else {
                                        // Use default behavior
                                        addControls();
                                    }
                                    if (
                                        objectType !==
                                        'evidence-linked-controls'
                                    ) {
                                        modalController.closeModal(
                                            LINK_CONTROLS_MODAL_ID,
                                        );
                                    }
                                }),
                            },
                        ]}
                    />
                </Box>
            </>
        );
    },
);
