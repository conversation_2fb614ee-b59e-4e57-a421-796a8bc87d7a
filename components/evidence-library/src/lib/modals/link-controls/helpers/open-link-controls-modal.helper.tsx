import type { ObjectTypes } from '@components/map-controls-step';
import {
    type ControlListBoxItemData,
    sharedLinkControlsController,
} from '@controllers/evidence-library';
import { modalController } from '@controllers/modal';
import { action } from '@globals/mobx';
import { LINK_CONTROLS_MODAL_ID } from '../constants/link-controls-modal.constant';
import { LinkControlsModal } from '../link-controls-modal.component';

interface OpenLinkControlsModalProps {
    objectType: ObjectTypes;
    onConfirm?: (selectedControls: ControlListBoxItemData[]) => void;
}
export const openLinkControlsModal = action(
    ({ objectType, onConfirm }: OpenLinkControlsModalProps): void => {
        sharedLinkControlsController.loadControls();

        modalController.openModal({
            id: LINK_CONTROLS_MODAL_ID,
            content: () => (
                <LinkControlsModal
                    objectType={objectType}
                    data-id="kqOd_M06"
                    onConfirm={onConfirm}
                />
            ),
            centered: true,
            disableClickOutsideToClose: true,
            size: 'lg',
        });
    },
);

interface OpenLinkControlsModalWithWorkspaceProps {
    objectType: ObjectTypes;
    onConfirm?: (selectedControls: ControlListBoxItemData[]) => void;
    excludeControlIds?: number[];
    workspaceId?: number;
    /**
     * Audit framework specific props.
     */
    auditId?: string;
    productId?: number;
    excludeRequestId?: number;
}
export const openLinkControlsModalWithWorkspace = action(
    ({
        objectType,
        onConfirm,
        excludeControlIds,
        workspaceId,
        auditId,
        productId,
        excludeRequestId,
    }: OpenLinkControlsModalWithWorkspaceProps): void => {
        sharedLinkControlsController.clearSelectedItems();

        // Set excluded control IDs in the controller before opening the modal
        if (excludeControlIds) {
            sharedLinkControlsController.setExcludedControlIds(
                excludeControlIds,
            );
        }

        // Load controls based on the type of request
        if (auditId && productId) {
            // Use audit framework controls endpoint
            sharedLinkControlsController.setAuditFrameworkParams(
                auditId,
                productId,
                excludeRequestId,
            );
            sharedLinkControlsController.loadControlsWithAuditFramework(
                auditId,
                productId,
                excludeRequestId,
            );
        } else if (workspaceId) {
            // Use regular workspace controls endpoint
            sharedLinkControlsController.loadControlsWithWorkspace(workspaceId);
        }

        modalController.openModal({
            id: LINK_CONTROLS_MODAL_ID,
            content: () => (
                <LinkControlsModal
                    objectType={objectType}
                    data-id="kqOd_M06"
                    enableAuditFrameworkControls={Boolean(auditId && productId)}
                    onConfirm={onConfirm}
                />
            ),
            centered: true,
            disableClickOutsideToClose: true,
            size: 'lg',
        });
    },
);
