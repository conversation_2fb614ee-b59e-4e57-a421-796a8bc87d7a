import { size } from 'lodash-es';
import { sharedEvidenceDetailsController } from '@controllers/evidence-library';
import { Accordion } from '@cosmos/components/accordion';
import { Metadata } from '@cosmos/components/metadata';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedEvidenceDetailsModel } from '@models/evidence-library-details';
import { AppLink } from '@ui/app-link';

export const EvidenceLinkedControlsPanelSection = observer(
    (): React.JSX.Element => {
        const { isLoading } = sharedEvidenceDetailsController;
        const { controls } = sharedEvidenceDetailsModel;
        const { currentWorkspaceId } = sharedWorkspacesController;

        const controlAccordion = controls.map((control) => (
            <Accordion
                key={control.id}
                title={control.name}
                titleType="lg"
                data-id={`evidence-details-control-${control.id}`}
                iconSlot={{
                    slotType: 'metadata',
                    typeProps: {
                        type: 'status',
                        label: control.code,
                        iconName: control.isReady ? 'CheckCircle' : 'NotReady',
                        colorScheme: control.isReady ? 'success' : 'critical',
                    },
                }}
                body={
                    <Stack direction="column" gap="xl">
                        <Text type="body" size="100">
                            {control.description}
                        </Text>
                        <AppLink
                            href={`/workspaces/${currentWorkspaceId}/compliance/controls/${control.id}/overview`}
                            label={t`Go to control`}
                        />
                    </Stack>
                }
            />
        ));

        return (
            <Stack direction="column" gap="2xl" data-id="-AAk8z_w">
                <Stack gap="lg">
                    <Text type="title" size="400">
                        <Trans>Mapped controls</Trans>
                    </Text>
                    <Metadata
                        type="number"
                        colorScheme="neutral"
                        label={size(controls).toString()}
                    />
                </Stack>

                <Stack direction="column" gap="xl">
                    {isLoading ? <Skeleton barCount={5} /> : controlAccordion}
                </Stack>
            </Stack>
        );
    },
);
