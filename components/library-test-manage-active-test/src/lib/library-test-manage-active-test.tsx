import { AppDatatable } from '@components/app-datatable';
import { modalController } from '@controllers/modal';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { LIBRARY_TEST_MANAGE_ACTIVE_MODAL_ID } from '@helpers/library-test-manage-active-modal';
import { getLibraryTestManageActiveTableColumns } from '../constants/library-test-manage-active-table.constants';
import type { ControlTestWorkspaceItem } from '../types/control-test-workspace-item.type';

export const LibraryTestModalComponent = (
    controlTestWorkspaceItems: ControlTestWorkspaceItem[],
): React.JSX.Element => {
    return (
        <>
            <Modal.Header
                data-id="library-test-manage-active-test-modal-header"
                title={t`Manage active`}
            />
            <Modal.Body data-id="library-test-manage-active-test-modal-body">
                <AppDatatable
                    hidePagination
                    isSortable={false}
                    isLoading={false}
                    tableId="library-test-manage-active-test-datatable"
                    columns={getLibraryTestManageActiveTableColumns()}
                    data-id="library-test-manage-active-test-datatable"
                    data={controlTestWorkspaceItems}
                    total={0}
                    tableSearchProps={{
                        hideSearch: true,
                    }}
                />
            </Modal.Body>
            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Close`,
                        level: 'secondary',
                        onClick: () => {
                            modalController.closeModal(
                                LIBRARY_TEST_MANAGE_ACTIVE_MODAL_ID,
                            );
                        },
                    },
                ]}
            />
        </>
    );
};
