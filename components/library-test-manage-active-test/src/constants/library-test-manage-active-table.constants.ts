import type { DatatableProps } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { COLUMN_SIZES } from '@helpers/table';
import { LibraryTestManageCell } from '../lib/cells/library-test-manage-cell';
import type { ControlTestWorkspaceItem } from '../types/control-test-workspace-item.type';

export const getLibraryTestManageActiveTableColumns =
    (): DatatableProps<ControlTestWorkspaceItem>['columns'] => {
        return [
            {
                id: 'name',
                accessorKey: 'controlTestInstanceName',
                header: () => t`Name`,
                cell: ({ row }) => row.original.controlTestInstanceName,
                minSize: COLUMN_SIZES.XLARGE,
            },
            {
                id: 'workspace',
                accessorKey: 'workspaceName',
                header: () => t`Workspace`,
                cell: ({ row }) => row.original.workspaceName,
                maxSize: COLUMN_SIZES.LARGE,
            },
            {
                id: 'controlTestInstanceId',
                accessorKey: 'controlTestInstanceTestId',
                header: '',
                cell: LibraryTestManageCell,
                maxSize: COLUMN_SIZES.MEDIUM,
            },
        ];
    };
