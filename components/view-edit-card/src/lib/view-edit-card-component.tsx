import { isNil, noop } from 'lodash-es';
import { useCallback, useEffect, useMemo, useState } from 'react';
import type { Action } from '@cosmos/components/action-stack';
import { Box } from '@cosmos/components/box';
import { Card } from '@cosmos/components/card';
import { useLingui } from '@globals/i18n/macro';

interface Props {
    title: string;
    readOnlyComponent: React.JSX.Element;
    editComponent: React.JSX.Element | null;
    onSave: () => void;
    onEdit?: () => void;
    onCancel?: () => void;
    /**
     * Optional: Pass mutation state from controller for reactive loading.
     * Required for correct behavior: ensures editing state resets when the mutation completes.
     */
    isMutationPending?: boolean;
    /**
     * Optional: Pass mutation error state to detect failures.
     * Required for correct behavior: prevents closing edit mode if the mutation errored.
     */
    hasMutationError?: boolean;
    /**
     * Optional: Pass custom label for edit button.
     */
    editButtonLabel?: string;

    /**
     * Optional: Pass card custom size.
     */
    size?: 'md' | 'lg';
}
export const ViewEditCardComponent = ({
    title,
    readOnlyComponent,
    editComponent,
    onSave,
    onEdit = noop,
    onCancel = noop,
    isMutationPending,
    hasMutationError,
    editButtonLabel,
    size = 'lg',
}: Props): React.JSX.Element => {
    const { t } = useLingui();

    const [isEditing, setIsEditing] = useState(false);

    const handleEdit = useCallback(() => {
        onEdit();
        setIsEditing(true);
    }, [onEdit]);

    /* eslint-disable-next-line -- needed to reset editing state after mutation completes */
    useEffect(() => {
        if (!isMutationPending && !hasMutationError) {
            setIsEditing(false);
        }
    }, [isMutationPending, hasMutationError]);

    const handleSave = useCallback(() => {
        onSave();

        // Close edit mode when mutation props are not provided to prevent staying in edit state
        if (isNil(isMutationPending) && isNil(hasMutationError)) {
            setIsEditing(false);
        }
    }, [hasMutationError, isMutationPending, onSave]);

    const handleCancel = useCallback(() => {
        onCancel();
        setIsEditing(false);
    }, [onCancel]);

    const actions: Action[] = useMemo(() => {
        if (!editComponent) {
            setIsEditing(false);

            return [];
        }

        return isEditing
            ? [
                  {
                      id: 'view-edit-card-save-btn',
                      actionType: 'button',
                      typeProps: {
                          label: t`Save`,
                          'data-id': 'view-edit-card-save-btn',
                          onClick: handleSave,
                          isLoading: isMutationPending,
                      },
                  },
                  {
                      id: 'view-edit-card-cancel-btn',
                      actionType: 'button',
                      typeProps: {
                          label: t`Cancel`,
                          'data-id': 'view-edit-card-cancel-btn',
                          onClick: handleCancel,
                          level: 'secondary',
                          cosmosUseWithCaution_isDisabled: isMutationPending,
                      },
                  },
              ]
            : [
                  {
                      id: 'view-edit-card-edit-btn',
                      actionType: 'button',
                      typeProps: {
                          label: editButtonLabel ?? t`Edit`,
                          'data-id': 'view-edit-card-edit-btn',
                          onClick: handleEdit,
                          level: 'secondary',
                      },
                  },
              ];
    }, [
        editComponent,
        isEditing,
        t,
        handleSave,
        isMutationPending,
        handleCancel,
        handleEdit,
        editButtonLabel,
    ]);

    return (
        <Card
            title={title}
            size={size}
            actions={actions}
            isEditMode={isEditing}
            body={<Box>{isEditing ? editComponent : readOnlyComponent}</Box>}
            data-testid="ViewEditCardComponent"
            data-id="oudFIgCW"
            cardHeight="fit-content"
        />
    );
};
