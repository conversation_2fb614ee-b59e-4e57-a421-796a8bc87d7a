import { styled } from 'styled-components';
import { EarlyAccessDropdown } from '@components/early-access-dropdown';
import { LocationBanners } from '@components/location-banners';
import { BannerLocation } from '@controllers/banner-service';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import {
    borderWidthSm,
    dimensionLg,
    dimensionMd,
    menuBackgroundInitial,
    neutralBorderFaded,
} from '@cosmos/constants/tokens';
import { Divider } from '@cosmos-lab/components/divider';
import { observer } from '@globals/mobx';
import { UserAvatar } from '@ui/user-avatar';
import { WorkspacePicker } from '@ui/workspace-picker';

export const StyledHeader = styled.header`
    display: flex;
    align-items: center;
    box-sizing: border-box;
    justify-content: space-between;
    padding: ${dimensionLg} ${dimensionMd};
    background-color: ${menuBackgroundInitial};
    border-bottom: ${borderWidthSm} solid ${neutralBorderFaded};
`;

export const StyledSectionWrapperDiv = styled.div`
    display: flex;
    align-items: center;
    gap: ${dimensionLg};
`;

export const StyledCenterSectionWrapperDiv = styled(StyledSectionWrapperDiv)`
    flex: 1;
    justify-content: center;
`;

const StyledLogoWrapper = styled.div`
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: start;
    gap: var(--dimension-lg);
`;

export const AppHeader = observer((): React.JSX.Element => {
    return (
        <>
            <LocationBanners
                location={BannerLocation.APP_HEADER}
                dataIdPrefix="app-header"
            />
            <StyledHeader data-testid="AppHeader" data-id="qRXCIolb">
                <StyledSectionWrapperDiv>
                    {/* <ComponentRenderer items={left} /> */}
                    {/* <CompanyLogo /> */}
                    <StyledLogoWrapper
                        data-testid="CompanyLogo"
                        data-id="djVVCJk_"
                    >
                        <Box pl="xs" width="3xl">
                            <svg
                                width="27"
                                height="20"
                                viewBox="0 0 27 20"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                                data-testid="CompanyLogo"
                                data-id="RmvylgWM"
                            >
                                <path
                                    d="M6.8554 19.9979H14.952C21.5799 19.9979 26.3636 16.0728 26.3636 10.001C26.3636 3.92927 21.5789 0 14.952 0H6.8554V0.04408C6.89869 0 12.0146 4.8529 12.0146 4.8529H14.7225C18.414 4.8529 21.3001 6.33726 21.3001 10.0021C21.3001 13.6668 18.414 15.1512 14.7225 15.1512H12.0116L6.85439 20L6.8554 19.9979Z"
                                    fill="#292A33"
                                />
                                <path
                                    d="M6.84535 5.30841C8.10832 7.39344 10.0579 8.9465 12.341 9.68854L13.6907 10.1265L12.341 10.5644C10.0579 11.3054 8.10832 12.8595 6.84535 14.9445C5.58238 12.8595 3.63283 11.3064 1.34969 10.5644L0 10.1265L1.34969 9.68854C3.63283 8.94755 5.58238 7.39344 6.84535 5.30841Z"
                                    fill="#292A33"
                                />
                            </svg>
                        </Box>
                        <Divider orientation="vertical" />
                    </StyledLogoWrapper>

                    <WorkspacePicker />
                </StyledSectionWrapperDiv>

                <StyledCenterSectionWrapperDiv>
                    {/* <ComponentRenderer items={center} /> */}
                </StyledCenterSectionWrapperDiv>

                <StyledSectionWrapperDiv>
                    {/* <ComponentRenderer items={right} /> */}
                    <EarlyAccessDropdown />
                    {/* <SearchButton /> */}
                    <Button
                        isIconOnly
                        label="Search"
                        startIconName="Search"
                        colorScheme="neutral"
                        level="tertiary"
                        size="md"
                        data-testid="SearchButton"
                        data-id="Xl2nJx_u"
                    />

                    {/* <NotificationsButton /> */}
                    <Button
                        isIconOnly
                        label="Notifications"
                        startIconName="Bell"
                        colorScheme="neutral"
                        level="tertiary"
                        size="md"
                        data-testid="NotificationsButton"
                        data-id="Xl2nJx_u"
                    />

                    <UserAvatar />
                </StyledSectionWrapperDiv>
            </StyledHeader>
        </>
    );
});
