import type {
    DatatableProps,
    FetchDataResponseParams,
    RowData,
} from '@cosmos/components/datatable';
import { makeAutoObservable } from '@globals/mobx';
import { convertToStandardQueryParams } from '../helpers/convert-to-standard-query-params.helper';
import type { AppDatatableProps } from '../types/app-datatable-props.type';
import type { ExternalDatatableController } from '../types/external-datatable-controller.type';
import type { StandardQueryParams } from '../types/standard-query-params.type';

/**
 * Internal controller that coordinates between external controllers and useDatatable.
 * This will handle user settings and other shared concerns in the future.
 */
export class InternalAppDatatableController<
    TData extends RowData,
    TQueryParams = StandardQueryParams,
> {
    externalController?: ExternalDatatableController<TData, TQueryParams>;

    constructor({
        externalController,
    }: {
        externalController?: ExternalDatatableController<TData, TQueryParams>;
    }) {
        makeAutoObservable(this);
        this.externalController = externalController;
    }

    handleFetchData = (params: FetchDataResponseParams): void => {
        if (this.externalController) {
            this.externalController.load(
                convertToStandardQueryParams<TQueryParams>(params),
            );
        }
    };

    /** Prepare props for the useDatatable hook. */
    prepareDatatableProps = ({
        props,
    }: {
        props: AppDatatableProps<TData, TQueryParams>;
    }): DatatableProps<TData> => {
        if (this.externalController) {
            return {
                ...props,
                columns: this.externalController.columns,
                filterProps: this.externalController.filterProps,
                emptyStateProps: this.externalController.emptyStateProps,
                tableSearchProps: this.externalController.tableSearchProps,
                filterViewModeProps:
                    this.externalController.filterViewModeProps,
                tableId: this.externalController.tableId,
                data: this.externalController.data,
                total: this.externalController.total,
                isLoading: this.externalController.isLoading,
                onFetchData: this.handleFetchData,
            };
        }

        // Props pattern: props must contain all required properties
        // Type assertion is safe here because TypeScript ensures props has required properties
        // when no controller is provided (PropsPatternProps branch of union)
        return props as DatatableProps<TData>;
    };

    // Future: User settings management will go here
    // getUserSettings = () => { ... }
    // applyUserSettings = (props) => { ... }
}
