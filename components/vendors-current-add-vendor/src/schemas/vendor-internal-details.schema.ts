import { z } from 'zod';
import {
    sharedUsersInfiniteController,
    sharedVendorContactsInfiniteController,
} from '@controllers/users';
import { sharedVendorsIntegrationsInfiniteController } from '@controllers/vendors';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import type { VendorResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { formatCurrencyValue } from '@helpers/formatters';
import type { FormSchema } from '@ui/forms';
import { RiskSelectField } from '../components/risk-select-field.component';
import {
    BUSINESS_UNIT_VALUES,
    getBusinessUnitLabel,
} from '../constants/business-unit.constants';
import { getRiskLabel, RISK_VALUES } from '../constants/risk.constants';
import { SECURITY_OWNER_ROLES } from '../constants/security-owner-roles.constants';
import { VENDOR_CONTACT_ROLES } from '../constants/vendor-contact-roles.constants';
import { VENDOR_STATUS_OPTIONS } from '../constants/vendor-status.constants';
import {
    getDropdownActionLabelVendorType,
    VENDOR_TYPE_VALUES,
} from '../constants/vendor-type.constants';
import {
    findInOptions,
    toFieldOptionsWithLabelFunction,
} from '../helpers/field-option.helpers';
import { getRecommendedImpactLevelOptions } from '../helpers/recommended-impact-level.helper';
import { transformIntegrationsToListBoxItemData } from '../helpers/vendor-add-vendor.helper';
import type { VendorInternalDetailsFormValuesType } from '../types/vendor-internal-details-schema.type';

export const buildVendorInternalDetailsFormSchema = (
    state?: VendorResponseDto | VendorInternalDetailsFormValuesType | null,
): FormSchema => {
    const {
        options: integrationsOptions,
        hasNextPage: hasMoreIntegrations,
        isFetching: isFetchingIntegrations,
        isLoading: isLoadingIntegrations,
        onFetchIntegrations,
    } = sharedVendorsIntegrationsInfiniteController;

    const {
        options: userOptions,
        hasNextPage: hasMoreUsers,
        isFetching: isFetchingUsers,
        isLoading: isLoadingUsers,
        onFetchUsers: onFetchUsers,
    } = sharedUsersInfiniteController;

    const {
        options: contactOptions,
        hasNextPage: hasMoreContacts,
        isFetching: isFetchingContacts,
        isLoading: isLoadingContacts,
        onFetchUsers: onFetchContacts,
    } = sharedVendorContactsInfiniteController;

    const shouldShowImpactLevel =
        !sharedFeatureAccessModel.isVendorRiskManagementProEnabled;

    const status = state?.status as ListBoxItemData | undefined;
    const type = state?.type as ListBoxItemData | undefined;
    const category = state?.category as ListBoxItemData | undefined;
    const risk = state?.risk as ListBoxItemData | undefined;
    const impactLevel = state?.impactLevel as ListBoxItemData | undefined;

    const vendorDetails: VendorInternalDetailsFormValuesType = {
        ...state,
        status: status?.value ?? status,
        type: type?.value ?? type,
        category: category?.value ?? category,
        risk: risk?.value ?? risk,
        impactLevel: impactLevel?.value ?? impactLevel,
    };

    const baseSchema: FormSchema = {
        status: {
            type: 'select',
            options: VENDOR_STATUS_OPTIONS,
            initialValue: vendorDetails.status
                ? findInOptions(
                      vendorDetails.status as string,
                      VENDOR_STATUS_OPTIONS,
                  )
                : undefined,
            label: t`Status`,
            isOptional: true,
            loaderLabel: t`Loading vendor statuses`,
        },
        type: {
            type: 'select',
            options: toFieldOptionsWithLabelFunction(
                VENDOR_TYPE_VALUES,
                getDropdownActionLabelVendorType,
            ),
            initialValue: vendorDetails.type
                ? findInOptions(
                      vendorDetails.type as string,
                      toFieldOptionsWithLabelFunction(
                          VENDOR_TYPE_VALUES,
                          getDropdownActionLabelVendorType,
                      ),
                  )
                : undefined,
            label: t`Type`,
            isOptional: true,
            loaderLabel: t`Loading vendor types`,
        },
        category: {
            type: 'select',
            options: toFieldOptionsWithLabelFunction(
                BUSINESS_UNIT_VALUES,
                getBusinessUnitLabel,
            ),
            initialValue: vendorDetails.category
                ? findInOptions(
                      vendorDetails.category as string,
                      toFieldOptionsWithLabelFunction(
                          BUSINESS_UNIT_VALUES,
                          getBusinessUnitLabel,
                      ),
                  )
                : findInOptions(
                      'NONE',
                      toFieldOptionsWithLabelFunction(
                          BUSINESS_UNIT_VALUES,
                          getBusinessUnitLabel,
                      ),
                  ),
            label: t`Business unit`,
            isOptional: true,
            loaderLabel: t`Loading business units`,
        },
        risk: {
            type: 'custom',
            render: RiskSelectField,
            validateWithDefault: 'select',
            options: toFieldOptionsWithLabelFunction(RISK_VALUES, getRiskLabel),
            initialValue: vendorDetails.risk
                ? (findInOptions(
                      vendorDetails.risk as string,
                      toFieldOptionsWithLabelFunction(
                          RISK_VALUES,
                          getRiskLabel,
                      ),
                  ) ?? undefined)
                : undefined,
            label: t`Risk`,
            isOptional: true,
            loaderLabel: t`Loading risk levels`,
        },
        ...(shouldShowImpactLevel && {
            impactLevel: {
                type: 'select',
                options: getRecommendedImpactLevelOptions(),
                initialValue: vendorDetails.impactLevel
                    ? (getRecommendedImpactLevelOptions().find(
                          (option) =>
                              option.value === vendorDetails.impactLevel,
                      ) ?? undefined)
                    : undefined,
                label: t`Impact Level`,
                isOptional: true,
                loaderLabel: t`Loading impact levels`,
            },
        }),
        dataStored: {
            type: 'textarea',
            initialValue: vendorDetails.dataStored ?? undefined,
            label: t`Stored data`,
            isOptional: true,
        },
        hasPii: {
            type: 'checkbox',
            initialValue: vendorDetails.hasPii ?? false,
            label: t`This vendor stores personally identifiable information (PII)`,
            isOptional: true,
        },
        isSubProcessor: {
            type: 'checkbox',
            initialValue: vendorDetails.isSubProcessor ?? false,
            label: t`This vendor is our sub-processor`,
            isOptional: true,
        },
        location: {
            type: 'text',
            initialValue: vendorDetails.location ?? undefined,
            label: t`Data location`,
            shownIf: {
                fieldName: 'isSubProcessor',
                operator: 'equals',
                value: true,
            },
        },
        integrations: {
            type: 'combobox',
            isMultiSelect: true,
            options: integrationsOptions,
            initialValue: transformIntegrationsToListBoxItemData(
                vendorDetails.fullIntegrations,
            ),
            label: t`Integrations`,
            placeholder: t`Search integrations`,
            isOptional: true,
            getSearchEmptyState: () => {
                return t`No integrations found`;
            },
            removeAllSelectedItemsLabel: t`Remove all integrations`,
            loaderLabel: t`Loading integrations`,
            getRemoveIndividualSelectedItemClickLabel: ({ itemLabel }) =>
                `Remove ${itemLabel}`,
            isLoading: isFetchingIntegrations && isLoadingIntegrations,
            hasMore: hasMoreIntegrations,
            onFetchOptions: onFetchIntegrations,
        },
        // TODO: https://drata.atlassian.net/browse/ENG-70842 - Deletion of selected item not working properly, will be addressed in upcoming ticket
        user: {
            type: 'combobox',
            options: userOptions,
            initialValue:
                userOptions.find(
                    (user) => user.id === String(vendorDetails.user?.id),
                ) ?? undefined,
            label: t`Security owner`,
            isOptional: true,
            loaderLabel: t`Loading security owners`,
            placeholder: t`Search by name`,
            getSearchEmptyState: () => t`No security owners found`,
            clearSelectedItemButtonLabel: t`Clear security owner`,
            isLoading: isFetchingUsers && isLoadingUsers,
            hasMore: hasMoreUsers,
            onFetchOptions: ({ search, increasePage }) => {
                onFetchUsers({
                    search,
                    increasePage,
                    excludeReadOnlyUsers: true,
                    roles: SECURITY_OWNER_ROLES,
                });
            },
            helpText: t`A security owner is responsible for reviewing this vendor’s security posture.`,
        },
        // TODO: https://drata.atlassian.net/browse/ENG-70842 - Deletion of selected item not working properly, will be addressed in upcoming ticket
        contact: {
            type: 'combobox',
            options: contactOptions,
            initialValue:
                contactOptions.find(
                    (user) => user.id === String(vendorDetails.contact?.id),
                ) ?? undefined,
            label: t`Vendor relationship contact`,
            isOptional: true,
            loaderLabel: t`Loading vendor contacts`,
            placeholder: t`Search by name`,
            getSearchEmptyState: () => t`No vendor contacts found`,
            clearSelectedItemButtonLabel: t`Clear contact`,
            isLoading: isFetchingContacts && isLoadingContacts,
            hasMore: hasMoreContacts,
            onFetchOptions: ({ search, increasePage }) => {
                onFetchContacts({
                    search,
                    increasePage,
                    roles: VENDOR_CONTACT_ROLES,
                });
            },
            helpText: t`This is the contact to reach out to if you have questions about this vendor.`,
        },
        cost: {
            type: 'text',
            initialValue: vendorDetails.cost
                ? formatCurrencyValue(vendorDetails.cost)
                : undefined,
            label: t`Annual contract value`,
            isOptional: true,
            validator: z
                .string()
                .refine(
                    (value) => {
                        // Allow empty values for optional fields
                        if (!value || value.trim() === '') {
                            return true;
                        }

                        // Validate format: numbers with up to 2 decimal places
                        return /^\d+(?:\.\d{1,2})?$/.test(value);
                    },
                    {
                        message:
                            'Only numbers with up to 2 decimal places are allowed',
                    },
                )
                .refine(
                    (value) => {
                        // Allow empty values for optional fields
                        if (!value || value.trim() === '') {
                            return true;
                        }

                        // Validate value is zero or positive
                        return Number(value) >= 0;
                    },
                    {
                        message: 'Value must be zero or positive',
                    },
                ),
        },
        notes: {
            type: 'textarea',
            initialValue: vendorDetails.notes ?? undefined,
            label: t`Additional notes`,
            isOptional: true,
        },
    };

    return baseSchema;
};
