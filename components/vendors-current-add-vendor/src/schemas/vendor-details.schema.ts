import { isEmpty } from 'lodash-es';
import { z } from 'zod';
import type { VendorResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { validateUrlWithTLDCheck } from '@globals/zod';
import type { FormSchema, FormValues } from '@ui/forms';
import {
    getPasswordPolicyLabel,
    PASSWORD_POLICY_VALUES,
} from '../constants/password-policy.constants';
import {
    findInOptions,
    toFieldOptionsWithLabelFunction,
} from '../helpers/field-option.helpers';
import {
    buildPasswordMinLengthListBoxItemData,
    transformPasswordMimLengthToListBoxItemData,
} from '../helpers/vendor-add-vendor.helper';
import type { PasswordPolicy } from '../types/password-policy.type';

export const buildVendorDetailsFormSchema = (
    state?: VendorResponseDto | FormValues | null,
): FormSchema => {
    return {
        name: {
            type: 'text',
            initialValue: (state?.name ?? '') as string,
            label: t`Vendor name`,
            validator: z
                .string()
                .min(1, {
                    message: t`Vendor name is required`,
                })
                .refine((val) => !isEmpty(val.trim()), {
                    message: t`Vendor name is required`,
                }),
        },
        url: {
            type: 'text',
            initialValue: (state?.url ?? '') as string,
            isOptional: true,
            label: t`Website URL`,
            validator: validateUrlWithTLDCheck(
                t`Website URL must be a valid URL`,
            ),
        },
        servicesProvided: {
            type: 'textarea',
            initialValue: (state?.servicesProvided ?? '') as string,
            isOptional: true,
            label: t`Provided services`,
        },
        passwordPolicyGroup: {
            header: t`Password policy`,
            type: 'group',
            direction: 'column',
            fields: {
                passwordPolicy: {
                    type: 'select',
                    options: toFieldOptionsWithLabelFunction(
                        PASSWORD_POLICY_VALUES,
                        getPasswordPolicyLabel,
                    ),
                    initialValue:
                        findInOptions(
                            (state?.passwordPolicy ?? 'NONE') as PasswordPolicy,
                            toFieldOptionsWithLabelFunction(
                                PASSWORD_POLICY_VALUES,
                                getPasswordPolicyLabel,
                            ),
                        ) ?? undefined,
                    isOptional: true,
                    label: t`Password policy`,
                    loaderLabel: t`Loading password policies`,
                },
                passwordRequiresMinLength: {
                    type: 'checkbox',
                    initialValue: (state?.passwordRequiresMinLength ??
                        false) as boolean,
                    label: t`Minimum length`,
                    isOptional: true,
                    shownIf: {
                        fieldName: 'passwordPolicyGroup.passwordPolicy',
                        operator: 'equals',
                        value: 'USERNAME_PASSWORD',
                    },
                },
                passwordMinLength: {
                    type: 'select',
                    options: buildPasswordMinLengthListBoxItemData(),
                    initialValue: transformPasswordMimLengthToListBoxItemData(
                        state?.passwordMinLength as number | undefined,
                    ),
                    label: t`Minimum length`,
                    shownIf: {
                        operator: 'and',
                        conditions: [
                            {
                                fieldName: 'passwordPolicyGroup.passwordPolicy',
                                operator: 'equals',
                                value: 'USERNAME_PASSWORD',
                            },
                            {
                                fieldName:
                                    'passwordPolicyGroup.passwordRequiresMinLength',
                                operator: 'equals',
                                value: true,
                            },
                        ],
                    },
                },
                passwordRequiresNumber: {
                    type: 'checkbox',
                    initialValue: (state?.passwordRequiresNumber ??
                        false) as boolean,
                    label: t`Requires number`,
                    isOptional: true,
                    shownIf: {
                        fieldName: 'passwordPolicyGroup.passwordPolicy',
                        operator: 'notEquals',
                        value: 'NONE',
                    },
                },
                passwordRequiresSymbol: {
                    type: 'checkbox',
                    initialValue: (state?.passwordRequiresSymbol ??
                        false) as boolean,
                    label: t`Requires symbol`,
                    isOptional: true,
                    shownIf: {
                        fieldName: 'passwordPolicyGroup.passwordPolicy',
                        operator: 'notEquals',
                        value: 'NONE',
                    },
                },
                passwordMfaEnabled: {
                    type: 'checkbox',
                    initialValue: (state?.passwordMfaEnabled ??
                        false) as boolean,
                    label: t`Two-Factor Authentication Enabled`,
                    isOptional: true,
                    shownIf: {
                        fieldName: 'passwordPolicyGroup.passwordPolicy',
                        operator: 'notEquals',
                        value: 'NONE',
                    },
                },
            },
        },
        privacyUrl: {
            type: 'text',
            initialValue: (state?.privacyUrl ?? '') as string,
            isOptional: true,
            label: t`Privacy policy URL`,
            validator: validateUrlWithTLDCheck(
                t`Privacy policy URL must be a valid URL`,
            ),
        },
        termsUrl: {
            type: 'text',
            initialValue: (state?.termsUrl ?? '') as string,
            isOptional: true,
            label: t`Terms of use URL`,
            validator: validateUrlWithTLDCheck(
                t`Terms of use URL must be a valid URL`,
            ),
        },
        contactAtVendor: {
            type: 'text',
            initialValue: (state?.contactAtVendor ?? '') as string,
            isOptional: true,
            label: t`Vendor contact name`,
        },
        contactsEmail: {
            type: 'text',
            initialValue: (state?.contactsEmail ?? '') as string,
            isOptional: true,
            label: t`Vendor contact email address`,
            validator: z.string().email().or(z.literal('')),
        },
    };
};
