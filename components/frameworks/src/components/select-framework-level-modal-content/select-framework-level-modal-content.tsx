import { useCallback, useMemo, useRef } from 'react';
import type { FrameworkDetailsController } from '@controllers/frameworks';
import type { ButtonProps } from '@cosmos/components/button';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { useLingui } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useNavigate } from '@remix-run/react';
import type { FormValues } from '@ui/forms';
import { SelectFrameworkLevelModel } from '../../models/select-framework-level.model';
import { SelectFrameworkLevelModalContentBody } from './select-framework-level-modal-content-body';
import { SelectFrameworkLevelModalContentController } from './select-framework-level-modal-content-controller';

interface Props {
    frameworksDetailsController: FrameworkDetailsController;
    onCancel?: () => void;
}

export const SelectFrameworkLevelModalContent = observer(
    ({ frameworksDetailsController, onCancel }: Props): React.JSX.Element => {
        const { t } = useLingui();
        const navigate = useNavigate();
        const formRef = useRef<HTMLFormElement & { submitForm: () => void }>(
            null,
        );

        const controller = useMemo(
            () => new SelectFrameworkLevelModalContentController(),
            [],
        );

        const selectFrameworkLevelModel = useMemo(
            () => new SelectFrameworkLevelModel(frameworksDetailsController),
            [frameworksDetailsController],
        );

        const { modalTitle } = selectFrameworkLevelModel;

        const handleCancel = useCallback(() => {
            controller.handleCancel(onCancel);
        }, [onCancel, controller]);

        const handleSave = useCallback(() => {
            formRef.current?.submitForm();
        }, []);

        const handleSubmit = useCallback(
            async (formValues: FormValues) => {
                await controller.handleSubmit(
                    formValues,
                    frameworksDetailsController,
                    navigate,
                );
            },
            [controller, frameworksDetailsController, navigate],
        );

        const isMutationLoading = controller.isLoading;
        const isFrameworkDetailLoading =
            frameworksDetailsController.isFetchingOrLoading;

        const actions = useMemo<ButtonProps[]>(
            () => [
                {
                    label: t`Cancel`,
                    level: 'secondary',
                    onClick: handleCancel,
                },
                {
                    label: t`Save`,
                    level: 'primary',
                    colorScheme: 'primary',
                    onClick: handleSave,
                    isLoading: isMutationLoading || isFrameworkDetailLoading,
                },
            ],
            [
                handleCancel,
                handleSave,
                isMutationLoading,
                isFrameworkDetailLoading,
                t,
            ],
        );

        return (
            <>
                <Modal.Header
                    title={modalTitle}
                    closeButtonAriaLabel={t`Close`}
                    onClose={handleCancel}
                />

                <Modal.Body>
                    <Stack
                        gap="6x"
                        direction="column"
                        data-testid="SelectFrameworkLevelModalContent"
                        data-id="goWnAw83"
                    >
                        <SelectFrameworkLevelModalContentBody
                            formRef={formRef}
                            handleSubmit={handleSubmit}
                            selectFrameworkLevelModel={
                                selectFrameworkLevelModel
                            }
                        />
                    </Stack>
                </Modal.Body>

                <Modal.Footer rightActionStack={actions} />
            </>
        );
    },
);
