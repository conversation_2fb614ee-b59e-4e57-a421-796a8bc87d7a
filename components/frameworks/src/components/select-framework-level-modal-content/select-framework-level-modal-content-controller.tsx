import { isNil } from 'lodash-es';
import {
    type FrameworkDetailsController,
    sharedFrameworkLevelMutationController,
} from '@controllers/frameworks';
import { modalController } from '@controllers/modal';
import { action, makeAutoObservable, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import type { FormValues } from '@ui/forms';
import { SELECT_FRAMEWORK_LEVEL_MODAL_ID } from '../../constants';

export class SelectFrameworkLevelModalContentController {
    constructor() {
        makeAutoObservable(this);
    }

    /**
     * Handle modal cancellation.
     */
    handleCancel(onCancel?: () => void): void {
        if (onCancel) {
            onCancel();
        }
        modalController.closeModal(SELECT_FRAMEWORK_LEVEL_MODAL_ID);
    }

    /**
     * Handle modal close.
     */
    handleClose(): void {
        modalController.closeModal(SELECT_FRAMEWORK_LEVEL_MODAL_ID);
    }

    /**
     * Handle form submission with business logic.
     */
    async handleSubmit(
        formValues: FormValues,
        frameworksDetailsController: FrameworkDetailsController,
        navigate: (path: string) => void,
    ): Promise<void> {
        const { level, privacy } =
            // This is safe as long as the form validation is applied.
            formValues as {
                level: { value: string };
                privacy: boolean;
            };

        await when(() => !isNil(frameworksDetailsController.frameworkDetails));

        // This should be a given at this point, but TypeScript doesn't know that
        if (isNil(frameworksDetailsController.frameworkDetails)) {
            return;
        }

        const frameworkId = frameworksDetailsController.frameworkDetails.id;
        const frameworkTag = frameworksDetailsController.frameworkDetails.tag;

        action(() => {
            sharedFrameworkLevelMutationController.updateFrameworkLevel(
                frameworkId,
                Number(level.value),
                frameworkTag === 'NIST80053' ? privacy : null,
            );
        })();

        // Wait for the mutation to complete
        await when(
            () =>
                !sharedFrameworkLevelMutationController.isLoading &&
                !sharedFrameworkLevelMutationController.hasError,
        );

        // Ensure we have a valid workspace ID before proceeding
        const { currentWorkspaceId } = sharedWorkspacesController;

        if (!currentWorkspaceId) {
            return;
        }

        // Invalidate to force a fresh fetch - this should trigger a new API call
        frameworksDetailsController.frameworkDetailsQuery.invalidate();

        // Wait for the fresh data to be fetched and loaded
        await when(
            () =>
                !frameworksDetailsController.frameworkDetailsQuery.isLoading &&
                !frameworksDetailsController.frameworkDetailsQuery.isFetching,
        );

        this.handleClose();

        navigate(
            `/workspaces/${currentWorkspaceId}/compliance/frameworks/all/current/${frameworkId}/requirements`,
        );
    }

    /**
     * Get loading states.
     */
    get isLoading(): boolean {
        const { isLoading: isMutationLoading } =
            sharedFrameworkLevelMutationController;

        return isMutationLoading;
    }
}
