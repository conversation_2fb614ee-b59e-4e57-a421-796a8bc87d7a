import {
    type FormEvent,
    lazy,
    type LazyExoticComponent,
    Suspense,
    useCallback,
    useMemo,
} from 'react';
import {
    type DisabledFrameworkResponseDto,
    sharedFrameworksUpgradeController,
} from '@controllers/frameworks';
import { modalController } from '@controllers/modal';
import type { ButtonProps } from '@cosmos/components/button';
import { Modal } from '@cosmos/components/modal';
import type { FrameworkResponseDto } from '@globals/api-sdk/types';
import { sharedCurrentUserController } from '@globals/current-user';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { ACTIVATE_FRAMEWORK_MODAL_ID } from '../../constants';

const getBodyLazyComponent = (
    frameworkTag: FrameworkResponseDto['tag'],
): LazyExoticComponent<() => React.JSX.Element | null> =>
    lazy(() => {
        switch (frameworkTag) {
            case 'NISTCSF2': {
                return import(
                    './activate-framework-modal-content-body-nistCsf2'
                ).then((mod) => ({
                    default: mod.ActivateFrameworkModalContentBodyNistCSF2,
                }));
            }
            case 'PCI4': {
                return import(
                    './activate-framework-modal-content-body-pci4'
                ).then((mod) => ({
                    default: mod.ActivateFrameworkModalContentBodyPci4,
                }));
            }
            default: {
                console.warn(`Not implemented frameworkTag: ${frameworkTag}\n`);

                return Promise.resolve({
                    default: () => (
                        <Trans data-id="ayxyyU8-">Not implemented</Trans>
                    ),
                });
            }
        }
    });

interface Props {
    framework: DisabledFrameworkResponseDto;
}

export const ActivateFrameworkModalContent = observer(
    ({ framework }: Props): React.JSX.Element => {
        const { name: frameworkName, tag: frameworkTag } = framework;

        const { isPending } = sharedFrameworksUpgradeController;

        const LazyContentBodyComponent = useMemo(
            () => getBodyLazyComponent(frameworkTag),
            [frameworkTag],
        );

        const handleClose = useCallback(() => {
            if (isPending) {
                return;
            }

            modalController.closeModal(ACTIVATE_FRAMEWORK_MODAL_ID);
        }, [isPending]);

        const canManageFrameworks =
            sharedCurrentUserController.hasUserPermission(
                'Framework',
                'MANAGE',
            );

        const handleSubmit = useCallback(
            async (evt: FormEvent<HTMLFormElement>) => {
                evt.preventDefault();

                if (
                    !sharedFrameworksUpgradeController.isSelfActivateFramework(
                        frameworkTag,
                    )
                ) {
                    throw new Error(
                        `${frameworkTag} is not a self-activate framework`,
                    );
                }

                await sharedFrameworksUpgradeController.upgradeFramework(
                    frameworkTag,
                );

                handleClose();
            },
            [frameworkTag, handleClose],
        );

        const footerActions = useMemo<ButtonProps[]>(() => {
            if (canManageFrameworks) {
                return [
                    {
                        label: t`Cancel`,
                        level: 'secondary',
                        cosmosUseWithCaution_isDisabled: isPending,
                        onClick: handleClose,
                    },
                    {
                        label: t`Save`,
                        level: 'primary',
                        colorScheme: 'primary',
                        type: 'submit',
                        isLoading: isPending,
                    },
                ];
            }

            return [
                {
                    label: t`Close`,
                    level: 'secondary',
                    onClick: handleClose,
                },
            ];
        }, [canManageFrameworks, handleClose, isPending]);

        return (
            <>
                <Modal.Header
                    title={t`Activate ${frameworkName}`}
                    closeButtonAriaLabel={t`Close`}
                    onClose={handleClose}
                />

                <Suspense data-id="0g2zjnNa">
                    <LazyContentBodyComponent />
                </Suspense>

                <form data-id="uasIaVDE" onSubmit={handleSubmit}>
                    <Modal.Footer rightActionStack={footerActions} />
                </form>
            </>
        );
    },
);
