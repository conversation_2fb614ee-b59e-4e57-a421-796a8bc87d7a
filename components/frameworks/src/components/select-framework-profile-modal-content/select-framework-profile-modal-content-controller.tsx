import { isNil } from 'lodash-es';
import {
    type FrameworkDetailsController,
    sharedFrameworkProfileMutationController,
} from '@controllers/frameworks';
import { modalController } from '@controllers/modal';
import { action, makeAutoObservable, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import type { FormValues } from '@ui/forms';
import { SELECT_FRAMEWORK_PROFILE_MODAL_ID } from '../../constants';

export class SelectFrameworkProfileModalContentController {
    constructor() {
        makeAutoObservable(this);
    }

    /**
     * Handle modal cancellation.
     */
    handleCancel(onCancel?: () => void): void {
        if (onCancel) {
            onCancel();
        }
        modalController.closeModal(SELECT_FRAMEWORK_PROFILE_MODAL_ID);
    }

    /**
     * Handle modal close.
     */
    handleClose(): void {
        modalController.closeModal(SELECT_FRAMEWORK_PROFILE_MODAL_ID);
    }

    /**
     * Handle form submission with business logic.
     */
    async handleSubmit(
        formValues: FormValues,
        frameworksDetailsController: FrameworkDetailsController,
        navigate: (path: string) => void,
    ): Promise<void> {
        const profileId =
            // This is safe as long as the form validation is applied.
            (formValues as { profile: { value: string } }).profile.value;

        await when(() => !isNil(frameworksDetailsController.frameworkDetails));

        const frameworkId = frameworksDetailsController.frameworkDetails?.id;

        // This should be a given at this point, but TypeScript doesn't know that
        if (!frameworkId) {
            return;
        }

        action(() => {
            sharedFrameworkProfileMutationController.updateFrameworkProfile(
                frameworkId,
                Number(profileId),
            );
        })();

        // Wait for the mutation to complete
        await when(
            () =>
                !sharedFrameworkProfileMutationController.isLoading &&
                !sharedFrameworkProfileMutationController.hasError,
        );

        // Ensure we have a valid workspace ID before proceeding
        const { currentWorkspaceId } = sharedWorkspacesController;

        if (!currentWorkspaceId) {
            return;
        }

        // Invalidate to force a fresh fetch - this should trigger a new API call
        frameworksDetailsController.frameworkDetailsQuery.invalidate();

        // Wait for the fresh data to be fetched and loaded
        await when(
            () =>
                !frameworksDetailsController.frameworkDetailsQuery.isLoading &&
                !frameworksDetailsController.frameworkDetailsQuery.isFetching,
        );

        this.handleClose();

        navigate(
            `/workspaces/${currentWorkspaceId}/compliance/frameworks/all/current/${frameworkId}/requirements`,
        );
    }

    /**
     * Get loading states.
     */
    get isLoading(): boolean {
        const { isLoading: isMutationLoading } =
            sharedFrameworkProfileMutationController;

        return isMutationLoading;
    }
}
