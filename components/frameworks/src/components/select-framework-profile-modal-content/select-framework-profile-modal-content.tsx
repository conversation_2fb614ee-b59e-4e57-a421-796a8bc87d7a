import { useCallback, useMemo, useRef } from 'react';
import type { FrameworkDetailsController } from '@controllers/frameworks';
import type { ButtonProps } from '@cosmos/components/button';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { useLingui } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useNavigate } from '@remix-run/react';
import type { FormValues } from '@ui/forms';
import { SelectFrameworkProfileModel } from '../../models/select-framework-profile.model';
import { SelectFrameworkProfileModalContentBody } from './select-framework-profile-modal-content-body';
import { SelectFrameworkProfileModalContentController } from './select-framework-profile-modal-content-controller';

interface Props {
    frameworksDetailsController: FrameworkDetailsController;
    onCancel?: () => void;
}

export const SelectFrameworkProfileModalContent = observer(
    ({ frameworksDetailsController, onCancel }: Props): React.JSX.Element => {
        const { t } = useLingui();
        const navigate = useNavigate();
        const formRef = useRef<HTMLFormElement & { submitForm: () => void }>(
            null,
        );

        const controller = useMemo(
            () => new SelectFrameworkProfileModalContentController(),
            [],
        );

        const selectFrameworkProfileModel = useMemo(
            () => new SelectFrameworkProfileModel(frameworksDetailsController),
            [frameworksDetailsController],
        );

        const { modalTitle } = selectFrameworkProfileModel;

        const handleCancel = useCallback(() => {
            controller.handleCancel(onCancel);
        }, [onCancel, controller]);

        const handleSave = useCallback(() => {
            formRef.current?.submitForm();
        }, []);

        const handleSubmit = useCallback(
            async (formValues: FormValues) => {
                await controller.handleSubmit(
                    formValues,
                    frameworksDetailsController,
                    navigate,
                );
            },
            [controller, frameworksDetailsController, navigate],
        );

        const { isLoading: isMutationLoading } = controller;
        const { isFetchingOrLoading: isFrameworkDetailLoading } =
            frameworksDetailsController;

        const actions = useMemo<ButtonProps[]>(
            () => [
                {
                    label: t`Cancel`,
                    level: 'secondary',
                    onClick: handleCancel,
                },
                {
                    label: t`Save`,
                    level: 'primary',
                    colorScheme: 'primary',
                    onClick: handleSave,
                    isLoading: isMutationLoading || isFrameworkDetailLoading,
                },
            ],
            [
                handleCancel,
                handleSave,
                isMutationLoading,
                isFrameworkDetailLoading,
                t,
            ],
        );

        return (
            <>
                <Modal.Header
                    title={modalTitle}
                    closeButtonAriaLabel={t`Close`}
                    onClose={handleCancel}
                />

                <Modal.Body>
                    <Stack
                        gap="6x"
                        direction="column"
                        data-testid="SelectFrameworkProfileModalContent"
                        data-id="goWnAw83"
                    >
                        <SelectFrameworkProfileModalContentBody
                            formRef={formRef}
                            handleSubmit={handleSubmit}
                            selectFrameworkProfileModel={
                                selectFrameworkProfileModel
                            }
                        />
                    </Stack>
                </Modal.Body>

                <Modal.Footer rightActionStack={actions} />
            </>
        );
    },
);
