import { CheckboxField } from '@cosmos/components/checkbox-field';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { PaginationControls } from '@cosmos-lab/components/pagination-controls';
import { Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import {
    EVIDENCE_DEFAULT_PAGE_SIZE,
    EVIDENCE_INITIAL_PAGE,
} from '../constants/request-changes-modal.constants';
import { sharedEvidenceSelectionModel } from '../models/evidence-selection.model';

export const EvidenceSelection = observer((): React.JSX.Element => {
    const { total, paginatedItems, loadPage, isLoading } =
        sharedEvidenceSelectionModel;

    const noEvidence = total === 0;

    if (isLoading) {
        return <Skeleton barCount={6} />;
    }

    if (noEvidence) {
        return (
            <Text type="body">
                <Trans>There is no evidence attached to this control</Trans>
            </Text>
        );
    }

    return (
        <Stack gap="md" direction="column" data-id="kaNxfHg3">
            <Text type="title">
                <strong>
                    <Trans>Select any evidence that needs changes:</Trans>
                </strong>
            </Text>
            <Stack gap="md" direction="column">
                {paginatedItems.map((item) => (
                    <CheckboxField
                        key={item.id}
                        formId={`request-changes-form-${item.id}`}
                        name={item.id}
                        checked={item.checked}
                        label={item.name}
                        data-id={`request-changes-checkbox-${item.id}`}
                        value={item.name}
                        onChange={(checked) => {
                            sharedEvidenceSelectionModel.handleSelectedEvidence(
                                checked,
                                item,
                            );
                        }}
                    />
                ))}
            </Stack>
            <PaginationControls
                hidePageSizeOptions
                total={total}
                pageSize={EVIDENCE_DEFAULT_PAGE_SIZE}
                initialPage={EVIDENCE_INITIAL_PAGE}
                data-id="request-changes-pagination"
                onPageChange={loadPage}
            />
        </Stack>
    );
});
