import { isDate, isString } from 'lodash-es';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Trans } from '@globals/i18n/macro';
import { formatDate, subtractDaysToDate } from '@helpers/date-time';
import { type CustomFieldRenderProps, UniversalFormField } from '@ui/forms';

const CONTROL_DEADLINE_DAYS = 13;

export const NextDeadlineFormField = ({
    'data-id': dataId,
    name,
    formId,
    value,
}: CustomFieldRenderProps): React.JSX.Element => {
    const dateValue = isString(value) || isDate(value) ? value : undefined;
    const pastDateLabel = formatDate(
        'sentence',
        subtractDaysToDate(dateValue, CONTROL_DEADLINE_DAYS),
    );

    return (
        <Stack
            gap="xl"
            direction="column"
            data-testid="NextDeadlineFormField"
            data-id="ouns3XFF"
        >
            <UniversalFormField
                __fromCustomRender
                formId={formId}
                name={name}
                data-id={dataId}
            />
            {value && (
                <Text type="body">
                    <Trans>
                        This control will move back to &quot;not ready&quot; on{' '}
                        {pastDateLabel}. It will need to be approved again for
                        it to be &quot;ready&quot;
                    </Trans>
                </Text>
            )}
        </Stack>
    );
};
