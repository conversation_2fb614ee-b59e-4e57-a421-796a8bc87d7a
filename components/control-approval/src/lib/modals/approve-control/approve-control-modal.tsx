import { sharedControlApprovalsReviewersMutationController as mutationController } from '@controllers/controls';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { ApprovalStatus, ReviewStatus } from '@drata/enums';
import { t, Trans } from '@globals/i18n/macro';
import { action, observer, when } from '@globals/mobx';
import { compareDayIsPast } from '@helpers/date-time';
import { Form, type FormValues, useFormSubmit } from '@ui/forms';
import { MARGIN_DAYS } from '../../constants/margin-days.constant';
import { NextDeadlineFormField } from './components/next-deadline-field.component';
import { closeApproveControlModal } from './helpers/close-approve-control-modal.helper';

const handleSubmit = action((values: FormValues) => {
    const typedValues = values as {
        nextDeadline: string;
    };

    mutationController.updateCurrentControlApproval({
        status: ApprovalStatus.APPROVED,
        nextDeadlineDate: typedValues.nextDeadline,
    });

    mutationController.updateCurrentControlApprovalReview({
        status: ReviewStatus.APPROVED,
        skipNextDeadline: false,
        comment: '',
    });

    when(
        () => !mutationController.isUpdating,
        () => {
            closeApproveControlModal();
        },
    );
});

const handleSkip = action(() => {
    mutationController.updateCurrentControlApproval({
        status: ApprovalStatus.APPROVED,
    });

    mutationController.updateCurrentControlApprovalReview({
        status: ReviewStatus.APPROVED,
        skipNextDeadline: true,
        comment: '',
    });

    when(
        () => !mutationController.isUpdating,
        () => {
            closeApproveControlModal();
        },
    );
});

export const ApproveControlModal = observer(() => {
    const { formRef, triggerSubmit } = useFormSubmit();

    const marginToShow = MARGIN_DAYS - 1;

    return (
        <>
            <Modal.Header
                closeButtonAriaLabel="Close"
                title={t`Set up this control's next approval`}
                onClose={closeApproveControlModal}
            />
            <Modal.Body>
                <Stack gap="2xl" direction="column">
                    <Text type="body">
                        <Trans>
                            Set your next deadline so we can remind you{' '}
                            {marginToShow} days before and add it to your tasks.
                        </Trans>
                    </Text>
                    <Form
                        hasExternalSubmitButton
                        ref={formRef}
                        formId="approve-control-form"
                        data-id="approve-control-form"
                        schema={{
                            nextDeadline: {
                                type: 'custom',
                                label: t`Next approval deadline`,
                                helpText: t`You must choose a date more than ${marginToShow} days out`,
                                getIsDateUnavailable: (date) => {
                                    const futureDate = new Date();

                                    futureDate.setDate(
                                        futureDate.getDate() + MARGIN_DAYS,
                                    );

                                    return compareDayIsPast(date, futureDate);
                                },
                                render: NextDeadlineFormField,
                                validateWithDefault: 'date',
                            },
                        }}
                        onSubmit={handleSubmit}
                    />
                </Stack>
            </Modal.Body>

            <Modal.Footer
                leftActionStack={[
                    {
                        label: t`Cancel`,
                        level: 'secondary',
                        cosmosUseWithCaution_isDisabled:
                            mutationController.isUpdating,
                        onClick: closeApproveControlModal,
                    },
                ]}
                rightActionStack={[
                    {
                        label: t`Skip`,
                        level: 'secondary',
                        isLoading: mutationController.isUpdating,
                        onClick: handleSkip,
                    },
                    {
                        label: t`Save`,
                        level: 'primary',
                        colorScheme: 'primary',
                        type: 'submit',
                        isLoading: mutationController.isUpdating,
                        onClick: () => {
                            triggerSubmit().catch(() => {
                                console.error('Failed to submit form');
                            });
                        },
                    },
                ]}
            />
        </>
    );
});
