import azureLogo from '@assets/img/company-logos/microsoft/azure/azure.svg';
import type { BaseProvider } from '../../types/base-provider.type';

export const AZURE_GCC_HIGH: BaseProvider = {
    id: 'AZURE_GCC_HIGH',
    name: 'Azure GCC High',
    companyName: 'Microsoft',
    companyUrl:
        'https://azure.microsoft.com/en-us/explore/global-infrastructure/government',
    logo: azureLogo,
    description:
        'On-premises, hybrid, multicloud, or at the edge—create secure, future-ready cloud solutions on Azure Government.',
    providerTypes: ['INFRASTRUCTURE', 'USER_ACCESS_REVIEW'],
} as const;
