import { describe, expect, test } from 'vitest';
import { z } from 'zod';
import {
    ensureSecureUrl,
    urlWithValidTLD,
    validateUrlWithTLDCheck,
} from './url-validation';

describe('ensureSecureUrl', () => {
    test('should add https:// to URLs without protocol', () => {
        expect(ensureSecureUrl('example.com')).toBe('https://example.com');
        expect(ensureSecureUrl('www.example.com')).toBe(
            'https://www.example.com',
        );
    });

    test('should not modify URLs that already have protocol', () => {
        expect(ensureSecureUrl('https://example.com')).toBe(
            'https://example.com',
        );
        expect(ensureSecureUrl('http://example.com')).toBe(
            'http://example.com',
        );
    });

    test('should handle empty values', () => {
        expect(ensureSecureUrl('')).toBe('');
    });

    test('should handle null and undefined values', () => {
        // Test the function's behavior with null/undefined by casting the function
        const ensureSecureUrlUnknown = ensureSecureUrl as (
            url: unknown,
        ) => unknown;

        expect(ensureSecureUrlUnknown(null)).toBeNull();
        expect(ensureSecureUrlUnknown(undefined)).toBeUndefined();
    });
});

describe('urlWithValidTLD', () => {
    test('should accept valid URLs', async () => {
        const validator = urlWithValidTLD();

        const validUrls = [
            'https://example.com',
            'http://example.com',
            'example.com',
            'www.example.com',
            'https://www.example.com',
            'subdomain.example.com',
            'example.org',
            'example.net',
        ];

        for (const url of validUrls) {
            const result = await validator.safeParseAsync(url);

            expect(result.success).toBeTruthy();
        }
    });

    test('should accept empty strings', async () => {
        const validator = urlWithValidTLD();
        const result = await validator.safeParseAsync('');

        expect(result.success).toBeTruthy();
    });

    test('should reject invalid URLs', async () => {
        const validator = urlWithValidTLD();

        const invalidUrls = [
            'not-a-url',
            'invalid',
            'http://',
            'https://',
            'ftp://example.com', // Different protocol
            'javascript:alert(1)',
        ];

        for (const url of invalidUrls) {
            const result = await validator.safeParseAsync(url);

            expect(result.success).toBeFalsy();
        }
    });

    test('should handle www prefix correctly', async () => {
        const validator = urlWithValidTLD();

        // Both should be valid as the function removes www prefix for validation
        const withWww = await validator.safeParseAsync('www.example.com');
        const withoutWww = await validator.safeParseAsync('example.com');

        expect(withWww.success).toBeTruthy();
        expect(withoutWww.success).toBeTruthy();
    });
});

describe('urlWithBothValidations', () => {
    test('should replicate web repository chained validation', async () => {
        const validator = validateUrlWithTLDCheck();

        // Should accept valid URLs that pass both validations
        const validUrls = [
            'https://example.com',
            'example.com',
            'www.example.com',
        ];

        for (const url of validUrls) {
            const result = await validator.safeParseAsync(url);

            expect(result.success).toBeTruthy();
        }

        // Should accept empty strings
        const emptyResult = await validator.safeParseAsync('');

        expect(emptyResult.success).toBeTruthy();

        // Should reject URLs that fail either validation
        const invalidUrls = [
            'javascript:alert(1)', // Fails basic URL format validation
            'localhost', // Passes basic URL format but fails urlWithValidTLD
            'asdf', // Fails urlWithValidTLD validation
            'invalid', // Fails urlWithValidTLD validation
        ];

        for (const url of invalidUrls) {
            const result = await validator.safeParseAsync(url);

            expect(result.success).toBeFalsy();
        }
    });

    test('should be more strict than urlWithValidTLD alone', async () => {
        const bothValidator = validateUrlWithTLDCheck();
        const tldValidator = urlWithValidTLD();

        // Test cases that should be rejected by urlWithBothValidations
        const testCases = [
            'localhost', // Invalid TLD
            'asdf', // Invalid domain
            'invalid', // Invalid domain
            'javascript:alert(1)', // Invalid protocol
        ];

        for (const testCase of testCases) {
            const tldResult = await tldValidator.safeParseAsync(testCase);
            const bothResult = await bothValidator.safeParseAsync(testCase);

            // Both should reject these invalid URLs
            expect(tldResult.success).toBeFalsy();
            expect(bothResult.success).toBeFalsy();
        }

        // Test valid URLs that both should accept
        const validUrls = ['example.com', 'https://example.org'];

        for (const url of validUrls) {
            const tldResult = await tldValidator.safeParseAsync(url);
            const bothResult = await bothValidator.safeParseAsync(url);

            expect(tldResult.success).toBeTruthy();
            expect(bothResult.success).toBeTruthy();
        }
    });
});

describe('uRL length validation', () => {
    test('should work with max length validation like in web repository', async () => {
        // In the web repository, they use .max(191)
        // We can combine our validators with Zod's max length
        const urlWithMaxLength = validateUrlWithTLDCheck().and(
            z.string().max(191),
        );

        // Test a very long URL
        const longUrl = `https://example.com/${'a'.repeat(200)}`;
        const result = await urlWithMaxLength.safeParseAsync(longUrl);

        expect(result.success).toBeFalsy();

        // Test a URL within the limit
        const shortUrl = 'https://example.com/short';
        const shortResult = await urlWithMaxLength.safeParseAsync(shortUrl);

        expect(shortResult.success).toBeTruthy();
    });
});
