import { isEmpty } from 'lodash-es';
import isUrl from 'validator/lib/isURL';
import { z } from 'zod';

/**
 * Ensures that a URL contains a valid protocol by adding it if missing.
 * URLs starting with "https://" or "http://" will not be affected.
 *
 * Based on the web repository's ensureSecureUrl function.
 *
 * @param url - The URL that needs to be checked and potentially modified.
 * @returns The modified URL with "https://" prefix if needed.
 */
export function ensureSecureUrl(url: string): string {
    // sanity check if url is null, undefined, or an empty string
    if (!url) {
        return url;
    }

    // Define the prefix we want to check
    const validProtocols = ['http://', 'https://'];

    if (validProtocols.some((protocol) => url.startsWith(protocol))) {
        return url;
    }

    // If the URL doesn't have the prefix, prepend https://
    return `https://${url}`;
}

/**
 * Creates a Zod validator for URLs with valid TLD validation.
 *
 * Based on the web repository's urlWithValidTLD function, adapted for Zod.
 * This function:
 * 1. Allows empty values (returns true for empty strings)
 * 2. Ensures the URL has a secure protocol using ensureSecureUrl
 * 3. Extracts the hostname and removes www prefix
 * 4. Validates the hostname using the validator library's isURL function for stricter TLD validation.
 *
 * Use urlWithValidTLD for stricter domain validation with proper TLD checking.
 * Use urlWithBothValidations for validation that replicates web repository's chained validation.
 *
 * @param errorMessage - Custom error message for validation failure.
 * @returns Zod string schema with URL validation.
 */
export function urlWithValidTLD(
    errorMessage?: string,
): z.ZodEffects<z.ZodString, string, string> {
    return z.string().refine(
        (value) => {
            try {
                if (isEmpty(value)) {
                    return true;
                }

                // Use ensureSecureUrl to add protocol if missing
                const secureUrl = ensureSecureUrl(value);

                // Extract hostname and remove www prefix
                const hostname = new URL(secureUrl).hostname.replace(
                    /^www\./i,
                    '',
                );

                // Validate using validator library's isURL function for stricter TLD validation
                return isUrl(hostname);
            } catch {
                return false;
            }
        },
        {
            message: errorMessage ?? 'Please enter a valid URL',
        },
    );
}

/**
 * Creates a Zod validator that combines both basic URL format validation and urlWithValidTLD validations.
 * This exactly replicates the web repository's chained validation:
 * .customUrl(...).urlWithValidTLD(...).
 *
 * This function:
 * 1. Allows empty values (returns true for empty strings)
 * 2. Ensures the URL has a secure protocol using ensureSecureUrl
 * 3. Uses Zod's native URL validation for basic format checking
 * 4. Validates the hostname using the validator library's isURL function for stricter TLD validation.
 *
 * Note: This function duplicates the logic from urlWithValidTLD instead of
 * reusing them directly to avoid complexities with Zod's ZodEffects composition, maintain
 * better control flow (early exit on first validation failure).
 *
 * @param errorMessage - Custom error message for validation failure.
 * @returns Zod string schema with both validations.
 */
export function validateUrlWithTLDCheck(
    errorMessage?: string,
): z.ZodEffects<z.ZodString, string, string> {
    return z.string().refine(
        async (value) => {
            try {
                if (isEmpty(value)) {
                    return true;
                }

                // First validation: basic URL format validation
                const secureUrl = ensureSecureUrl(value);
                const urlSchema = z.string();
                const basicUrlResult =
                    await urlSchema.safeParseAsync(secureUrl);

                if (!basicUrlResult.success) {
                    return false; // Failed basic URL format validation
                }

                // Second validation: urlWithValidTLD logic
                const hostname = new URL(secureUrl).hostname.replace(
                    /^www\./i,
                    '',
                );

                return isUrl(hostname); // Must pass both validations
            } catch {
                return false;
            }
        },
        {
            message: errorMessage ?? 'Please enter a valid URL',
        },
    );
}
