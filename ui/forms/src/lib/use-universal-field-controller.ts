import { useCallback, useMemo } from 'react';
import { useController, useFormState } from 'react-hook-form';
import { getFieldSchemaByPath } from './helpers/get-field-schema-by-path.helper';
import { useFormContext } from './hooks/use-form-context.hook';
import { useIsShownWithRegistration } from './hooks/use-is-shown-with-registration.hook';
import type { FieldMetaByType } from './types/field-meta-by-type.type';
import type { FieldSchema } from './types/field-schema.type';
import type { FieldType } from './types/field-type.type';
import type { UniversalFieldControllerWithValue } from './types/universal-field-controller-with-value.type';

export function useUniversalFieldController<T extends FieldType>(
    name: string,
): [
    UniversalFieldControllerWithValue<T>,
    ReturnType<typeof useController>['formState'],
] {
    const { control, schema, setValue: setFormValue } = useFormContext();
    const { disabled } = useFormState();
    const fieldSchema = useMemo(() => {
        return getFieldSchemaByPath(schema, name);
    }, [schema, name]);

    if (!fieldSchema) {
        throw new Error(`No field schema found for: ${name}`);
    }

    const { type, isOptional, shownIf, readOnly } = fieldSchema;

    const fieldProps = useController({
        name,
        control,
        rules: { required: !isOptional },
        disabled,
    });

    const isShown = useIsShownWithRegistration(name, shownIf);

    const setValue = useCallback(
        (value: FieldMetaByType[T]['initialValue']) => {
            setFormValue(name, value, {
                shouldValidate: true,
                shouldDirty: true,
                shouldTouch: true,
            });
        },
        [name, setFormValue],
    );

    return [
        {
            ...fieldProps.field,
            ...fieldProps.fieldState,
            value: fieldProps.field.value as T,
            setValue,
            fieldSchemaProps: {
                ...fieldSchema,
                readOnly: readOnly || disabled,
            } as FieldSchema,
            type,
            isShown,
            isOptional,
        },
        fieldProps.formState,
    ];
}
