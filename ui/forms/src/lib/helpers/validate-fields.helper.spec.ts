import { describe, expect, test, vi } from 'vitest';
import { z } from 'zod';
import type { FormSchema } from '../types/form-schema.type';
import * as getDefaultValidatorModule from './get-default-validator.helper';
import * as isConditionallyShownModule from './is-conditionally-shown.helper';
import { validateFields } from './validate-fields.helper';

describe('validateFields', () => {
    test('should validate a simple field with default validator', async () => {
        const schema: FormSchema = {
            name: {
                type: 'text',
                label: 'Name',
            },
        };

        const data = { name: '<PERSON>' };
        const ctx = { addIssue: vi.fn() } as unknown as z.RefinementCtx;

        vi.spyOn(
            getDefaultValidatorModule,
            'getDefaultValidator',
        ).mockReturnValue(z.string().min(3));

        vi.spyOn(
            isConditionallyShownModule,
            'isConditionallyShown',
        ).mockReturnValue(true);

        await validateFields(schema, data as unknown as z.Zod<PERSON>awShape, ctx);

        expect(ctx.addIssue).not.toHaveBeenCalled();
    });

    test('should add issue when validation fails', async () => {
        const schema: FormSchema = {
            name: {
                type: 'text',
                label: 'Name',
            },
        };

        const data = { name: 'Jo' };
        const ctx = { addIssue: vi.fn() } as unknown as z.RefinementCtx;

        vi.spyOn(
            getDefaultValidatorModule,
            'getDefaultValidator',
        ).mockReturnValue(z.string().min(3));

        vi.spyOn(
            isConditionallyShownModule,
            'isConditionallyShown',
        ).mockReturnValue(true);

        await validateFields(schema, data as unknown as z.ZodRawShape, ctx);

        expect(ctx.addIssue).toHaveBeenCalled();
    });

    test('should skip validation for hidden fields', async () => {
        const schema: FormSchema = {
            name: {
                type: 'text',
                label: 'Name',
                shownIf: {
                    fieldName: 'showName',
                    operator: 'equals',
                    value: true,
                },
            },
        };

        const data = { name: 'Jo' };
        const ctx = { addIssue: vi.fn() } as unknown as z.RefinementCtx;

        vi.spyOn(
            getDefaultValidatorModule,
            'getDefaultValidator',
        ).mockReturnValue(z.string().min(3));

        vi.spyOn(
            isConditionallyShownModule,
            'isConditionallyShown',
        ).mockReturnValue(false);

        await validateFields(schema, data as unknown as z.ZodRawShape, ctx);

        expect(ctx.addIssue).not.toHaveBeenCalled();
    });

    test('should validate nested group fields', async () => {
        const schema: FormSchema = {
            user: {
                type: 'group',
                header: 'User',
                fields: {
                    name: {
                        type: 'text',
                        label: 'Name',
                    },
                    age: {
                        type: 'text',
                        label: 'Age',
                    },
                },
            },
        };

        const data = { user: { name: 'John', age: '17' } };
        const ctx = { addIssue: vi.fn() } as unknown as z.RefinementCtx;

        const getDefaultValidatorMock = vi.spyOn(
            getDefaultValidatorModule,
            'getDefaultValidator',
        );

        getDefaultValidatorMock.mockImplementation((field) => {
            if (field.type === 'text' && field.label === 'Name') {
                return z.string().min(3);
            }
            if (field.type === 'text' && field.label === 'Age') {
                return z.string().refine((val) => parseInt(val) >= 18, {
                    message: 'Age must be at least 18',
                });
            }

            return null;
        });

        vi.spyOn(
            isConditionallyShownModule,
            'isConditionallyShown',
        ).mockReturnValue(true);

        await validateFields(schema, data as unknown as z.ZodRawShape, ctx);

        expect(ctx.addIssue).toHaveBeenCalled();
    });

    test('should skip optional fields without validators', async () => {
        const schema: FormSchema = {
            name: {
                type: 'text',
                label: 'Name',
                isOptional: true,
            },
        };

        const data = { name: 'Jo' };
        const ctx = { addIssue: vi.fn() } as unknown as z.RefinementCtx;

        vi.spyOn(
            getDefaultValidatorModule,
            'getDefaultValidator',
        ).mockReturnValue(z.string().min(3));

        vi.spyOn(
            isConditionallyShownModule,
            'isConditionallyShown',
        ).mockReturnValue(true);

        await validateFields(schema, data as unknown as z.ZodRawShape, ctx);

        expect(ctx.addIssue).not.toHaveBeenCalled();
    });

    test('should validate optional fields with validators', async () => {
        const schema: FormSchema = {
            url: {
                type: 'text',
                label: 'URL',
                isOptional: true,
                validator: z.string().url().or(z.literal('')),
            },
        };

        const data = { url: 'invalid-url' };
        const ctx = { addIssue: vi.fn() } as unknown as z.RefinementCtx;

        vi.spyOn(
            getDefaultValidatorModule,
            'getDefaultValidator',
        ).mockReturnValue(null);

        vi.spyOn(
            isConditionallyShownModule,
            'isConditionallyShown',
        ).mockReturnValue(true);

        await validateFields(schema, data as unknown as z.ZodRawShape, ctx);

        expect(ctx.addIssue).toHaveBeenCalled();
    });

    test('should allow empty values for optional fields with validators', async () => {
        const schema: FormSchema = {
            url: {
                type: 'text',
                label: 'URL',
                isOptional: true,
                validator: z.string().url().or(z.literal('')),
            },
        };

        const data = { url: '' };
        const ctx = { addIssue: vi.fn() } as unknown as z.RefinementCtx;

        vi.spyOn(
            getDefaultValidatorModule,
            'getDefaultValidator',
        ).mockReturnValue(null);

        vi.spyOn(
            isConditionallyShownModule,
            'isConditionallyShown',
        ).mockReturnValue(true);

        await validateFields(schema, data as unknown as z.ZodRawShape, ctx);

        expect(ctx.addIssue).not.toHaveBeenCalled();
    });

    test('should combine default and custom validators', async () => {
        const schema: FormSchema = {
            email: {
                type: 'text',
                label: 'Email',
                validator: z.string().email(),
            },
        };

        const data = { email: 'not-an-email' };
        const ctx = { addIssue: vi.fn() } as unknown as z.RefinementCtx;

        vi.spyOn(
            getDefaultValidatorModule,
            'getDefaultValidator',
        ).mockReturnValue(z.string().min(3));

        vi.spyOn(
            isConditionallyShownModule,
            'isConditionallyShown',
        ).mockReturnValue(true);

        await validateFields(schema, data as unknown as z.ZodRawShape, ctx);

        expect(ctx.addIssue).toHaveBeenCalled();
    });

    test('should skip validation for readOnly fields', async () => {
        const schema: FormSchema = {
            name: {
                type: 'text',
                label: 'Name',
                readOnly: true,
            },
        };

        const data = { name: 'Jo' };
        const ctx = { addIssue: vi.fn() } as unknown as z.RefinementCtx;

        vi.spyOn(
            getDefaultValidatorModule,
            'getDefaultValidator',
        ).mockReturnValue(z.string().min(3));

        vi.spyOn(
            isConditionallyShownModule,
            'isConditionallyShown',
        ).mockReturnValue(true);

        await validateFields(schema, data as unknown as z.ZodRawShape, ctx);

        expect(ctx.addIssue).not.toHaveBeenCalled();
    });
});
