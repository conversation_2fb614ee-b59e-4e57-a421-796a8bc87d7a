import { get } from 'lodash-es';
import type { z } from 'zod';
import type { FieldSchema } from '../types/field-schema.type';
import type { FormSchema } from '../types/form-schema.type';
import { getDefaultValidator } from './get-default-validator.helper';
import { isConditionallyShown } from './is-conditionally-shown.helper';

/**
 * Recursively validates a schema against form data, respecting visibility and optionality.
 *
 * @param schema - The form schema definition.
 * @param data - The current subset of form data for the scope.
 * @param ctx - Zod refinement context used to collect validation issues.
 * @param path - Current dot path used to track nested keys.
 * @param rootData - The full root form data object for condition evaluation.
 */
export const validateFields = async (
    schema: FormSchema,
    data: z.ZodRawShape,
    ctx: z.RefinementCtx,
    path: string[] = [],
    rootData: z.ZodRawShape = data,
): Promise<void> => {
    for (const [key, field] of Object.entries(schema)) {
        const currentPath = [...path, key];
        const currentValue = get(rootData, currentPath) as z.ZodRawShape;

        // Skip if field or group is not shown
        if (!isConditionallyShown(rootData, field.shownIf)) {
            continue;
        }

        if (field.type === 'custom') {
            if (field.customType === 'arrayOfObjects') {
                if (!Array.isArray(currentValue)) {
                    ctx.addIssue({
                        code: 'custom',
                        message: `${key} must be an array`,
                        path: currentPath,
                    });
                    continue;
                }

                for (const [index, item] of currentValue.entries()) {
                    await validateFields(
                        field.fields,
                        item as z.ZodRawShape,
                        ctx,
                        [...currentPath, index.toString()],
                        rootData,
                    );
                }

                continue;
            }

            if (field.customType === 'object') {
                await validateFields(
                    field.fields,
                    currentValue,
                    ctx,
                    currentPath,
                    rootData,
                );
                continue;
            }
        }

        // Recurse into nested group
        if (field.type === 'group') {
            await validateFields(
                field.fields,
                currentValue,
                ctx,
                currentPath,
                rootData,
            );
            continue;
        }

        // Skip optional fields without validators
        if ((field.isOptional || field.readOnly) && !field.validator) {
            continue;
        }

        let defaultValidator;

        defaultValidator = getDefaultValidator(field);

        if (field.type === 'custom' && field.validateWithDefault) {
            defaultValidator = getDefaultValidator({
                ...field,
                type: field.validateWithDefault,
            } as FieldSchema);
        }

        const customValidator = field.validator;

        const validator =
            defaultValidator && customValidator
                ? customValidator.and(defaultValidator)
                : (defaultValidator ?? customValidator);

        if (!validator) {
            continue;
        }

        const result = await validator.safeParseAsync(currentValue);

        if (!result.success) {
            for (const issue of result.error.issues) {
                ctx.addIssue({
                    ...issue,
                    path: [...currentPath, ...issue.path],
                });
            }
        }
    }
};
