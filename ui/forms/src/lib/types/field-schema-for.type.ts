import type { CheckboxFieldProps } from '@cosmos/components/checkbox-field';
import type { StackProps } from '@cosmos/components/stack';
import type { ConditionsFor } from './conditions-for.type';
import type { CustomFieldProps } from './custom-field-props.type';
import type { FieldPropsByType } from './field-props-by-type.type';
import type { FieldType } from './field-type.type';
import type { InitialValueByFieldType } from './initial-value-by-field-type.type';
import type { Validator } from './validator.type';

/**
 * **Internal use only** — A generic version of `BaseField` for use with any custom form schema.
 *
 * For most cases, use the alias type `BaseField`, which is the default with `FormSchema` applied.
 *
 * Example:.
 * ```ts
 * import { BaseField } from '@ui/forms';
 * ```
 */
export type BaseFieldFor<TField, TFormSchema> = Omit<
    TField,
    | 'checked'
    | 'data-id'
    | 'defaultSelectedOptions'
    | 'defaultValue'
    | 'feedback'
    | 'formId'
    | 'initialValue'
    | 'name'
    | 'onBlur'
    | 'onChange'
    | 'onClick'
    | 'onFocus'
    | 'onKeyDown'
    | 'optionalText'
    | 'required'
    | 'value'
> & {
    /** Conditions that determine if the field is shown or hidden. */
    shownIf?: ConditionsFor<TFormSchema>;
    /** If `true`, then the field is optional. */
    isOptional?: boolean;
    /** Future arktype validator definitions. */
    validator?: Validator;
    /** The value property for checkbox fields. */
    value?: TField extends CheckboxFieldProps ? string : never;
    /** If `true`, a divider will be rendered below the field. */
    showDivider?: boolean;
    /** How to align the field within its container. */
    alignItems?: StackProps['align'];
    /** If `true`, the field is read-only. */
    readOnly?: boolean;
};

/**
 * **Internal use only** — Creates a union of all possible field schema types.
 *
 * This type maps each field type to its corresponding schema definition, combining
 * the base field properties with type-specific properties and ensuring the correct
 * initialValue type for each field type.
 */
type PrimitiveFieldSchemaUnion<TFormSchema> = {
    [K in FieldType]: BaseFieldFor<FieldPropsByType[K], TFormSchema> & {
        type: K;
        initialValue?: InitialValueByFieldType[K];
    };
}[FieldType];

/**
 * **Internal use only** — Creates a union of all possible field schema types.
 *
 * This type maps each field type to its corresponding schema definition, combining
 * the base field properties with type-specific properties and ensuring the correct
 * initialValue type for each field type.
 */
export type FieldSchemaFor<TFormSchema> =
    | PrimitiveFieldSchemaUnion<TFormSchema>
    | (CustomFieldProps<TFormSchema> & {
          type: 'custom';
          initialValue?: unknown;
      });
