import type { RiskResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export function getRiskTreatmentLabel(
    status: RiskResponseDto['treatmentPlan'],
): string {
    switch (status) {
        case 'UNTREATED': {
            return t`Untreated`;
        }
        case 'ACCEPT': {
            return t`Accept`;
        }
        case 'TRANSFER': {
            return t`Transfer`;
        }
        case 'AVOID': {
            return t`Avoid`;
        }
        case 'MITIGATE': {
            return t`Mitigate`;
        }
        default: {
            return '—';
        }
    }
}

export const getTreatmentIcon = (
    treatmentType: string,
    value: number,
): string => {
    if (treatmentType === 'UNTREATED') {
        return value > 0 ? 'WarningTriangle' : 'CheckCircle';
    }

    return 'CheckCircle';
};

export const getTreatmentColor = (
    treatmentType: string,
    value: number,
): string => {
    if (treatmentType === 'UNTREATED') {
        return value > 0 ? 'warning' : 'success';
    }

    return 'success';
};
