/**
 * Checks if a control code is a DCF (Drata Control Framework) code.
 * DCF codes start with the prefix "DCF".
 *
 * @param code - The control code to check.
 * @returns True if the code starts with "DCF", false otherwise.
 * @example
 * ```typescript
 * isDCFCode('DCF-001') // returns true
 * isDCFCode('DCF-ABC') // returns true
 * isDCFCode('DRA-001') // returns false
 * isDCFCode('CTRL-123') // returns false
 * isDCFCode(null) // returns false
 * isDCFCode(undefined) // returns false
 * ```
 */
export const isDCFCode = (code?: string | null): boolean => {
    return code?.startsWith('DCF') ?? false;
};
