import { omit, uniqueId } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import {
    accessReviewApplicationControllerUpdateAccessApplicationDetailsMutation,
    accessReviewPeriodControllerPostReviewPeriodMutation,
} from '@globals/api-sdk/queries';
import type {
    AccessReviewApplicationRequestDto,
    AccessReviewApplicationResponseDto,
    CreateReviewPeriodRequestDto,
    UserResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    action,
    makeAutoObservable,
    ObservedMutation,
    when,
} from '@globals/mobx';
import { addMonths } from '@helpers/date-time';
import { sharedActiveAccessReviewPeriodsController } from '../access-review-periods-active-controller';

const TODAY_DATE = new Date().toISOString().split('T')[0];
const DEFAULT_ADD_MONTHS_TO_FROM_TODAY = 3;

const INITIAL_FORM_DATA: CreateReviewPeriodRequestDto = {
    startingDate: TODAY_DATE,
    endingDate: addMonths(TODAY_DATE, DEFAULT_ADD_MONTHS_TO_FROM_TODAY)
        .toISOString()
        .split('T')[0],
    applications: [],
};

class CreateReviewPeriodController {
    formData: CreateReviewPeriodRequestDto = {
        startingDate: '',
        endingDate: '',
        applications: [],
    };

    selectedApplicationsData: AccessReviewApplicationResponseDto[] = [];

    _resetCombobox = 0;

    constructor() {
        makeAutoObservable(this);
        this.formData = INITIAL_FORM_DATA;
    }

    createReviewPeriodMutation = new ObservedMutation(
        accessReviewPeriodControllerPostReviewPeriodMutation,
    );

    updateApplicationDetailsMutation = new ObservedMutation(
        accessReviewApplicationControllerUpdateAccessApplicationDetailsMutation,
    );

    get isCreating(): boolean {
        return this.createReviewPeriodMutation.isPending;
    }

    get hasError(): boolean {
        return this.createReviewPeriodMutation.hasError;
    }

    get selectedApplications(): AccessReviewApplicationResponseDto[] {
        return this.selectedApplicationsData;
    }

    resetCombobox = () => {
        this._resetCombobox = this._resetCombobox + 1;
    };

    setDetailsData = (
        data: Pick<CreateReviewPeriodRequestDto, 'startingDate' | 'endingDate'>,
    ) => {
        this.formData.startingDate = data.startingDate;
        this.formData.endingDate = data.endingDate;
    };

    setApplicationsData = (data: AccessReviewApplicationResponseDto[]) => {
        this.formData.applications = data.map((app) => ({
            applicationId: Number(app.id),
            reviewersIds: app.reviewers.map((reviewer) => Number(reviewer.id)),
            accessApplicationId:
                Number(app.accessApplicationId) || Number(app.id),
        }));
        this.selectedApplicationsData = data;
    };

    resetFormData = () => {
        this.formData = INITIAL_FORM_DATA;
        this.selectedApplicationsData = [];
    };

    updateApplicationDetails = ({
        id,
        name,
        websiteUrl,
        reviewers,
        source,
    }: {
        id: number;
        name: string;
        websiteUrl: string;
        source: AccessReviewApplicationResponseDto['source'];
        reviewers: UserResponseDto[];
    }) => {
        const sendData: AccessReviewApplicationRequestDto = {
            name,
            websiteUrl,
            reviewersIds: reviewers.map((reviewer) => reviewer.id),
        };

        if (source !== 'MANUALLY_ADDED') {
            omit(sendData, 'websiteUrl');
        }

        this.updateApplicationDetailsMutation.mutate({
            path: {
                id,
            },
            body: sendData,
        });

        when(
            () => !this.updateApplicationDetailsMutation.isPending,
            () => {
                if (this.updateApplicationDetailsMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: `update-application-details-error-${id}`,
                        props: {
                            title: t`Failed to update application details`,
                            description: t`Unable to update application details`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                this.setApplicationsData(
                    this.selectedApplicationsData.map((app) => {
                        if (Number(app.id) === id) {
                            return {
                                ...app,
                                name,
                                websiteUrl,
                                reviewers,
                            };
                        }

                        return app;
                    }),
                );

                snackbarController.addSnackbar({
                    id: `update-application-details-success-${uniqueId()}`,
                    props: {
                        title: t`Application details updated successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    createReviewPeriod = (): Promise<boolean> => {
        const { activeAccessReviewPeriods } =
            sharedActiveAccessReviewPeriodsController;

        return this.createReviewPeriodMutation
            .mutateAsync({
                body: this.formData,
            })
            .then(() => {
                activeAccessReviewPeriods.invalidate();
            })
            .then(() => {
                snackbarController.addSnackbar({
                    id: `create-review-period-success-${uniqueId()}`,
                    props: {
                        title: t`Review period created successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                action(() => {
                    this.resetFormData();
                })();

                return true;
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: `create-review-period-error-${uniqueId()}`,
                    props: {
                        title: t`Failed to create review period`,
                        description: t`Unable to create review period`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                return false;
            });
    };
}

export const sharedCreateReviewPeriodController =
    new CreateReviewPeriodController();
