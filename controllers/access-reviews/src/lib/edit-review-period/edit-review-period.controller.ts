import { omit, uniqueId } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import {
    accessReviewApplicationControllerUpdateAccessApplicationDetailsMutation,
    accessReviewPeriodControllerGetReviewPeriodDetailsOptions,
    accessReviewPeriodControllerUpdateAccessReviewPeriodMutation,
} from '@globals/api-sdk/queries';
import type {
    AccessReviewApplicationRequestDto,
    AccessReviewPeriodApplicationResponseDto,
    AccessReviewPeriodResponseDto,
    UpdateReviewPeriodRequestDto,
    UserResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';

class EditReviewPeriodController {
    formData: UpdateReviewPeriodRequestDto = {
        startingDate: '',
        endingDate: '',
        applications: [],
        selectAll: false,
    };

    selectedApplicationsData: AccessReviewPeriodApplicationResponseDto[] = [];

    periodId: number | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    reviewPeriodQuery = new ObservedQuery(
        accessReviewPeriodControllerGetReviewPeriodDetailsOptions,
    );

    updateAccessReviewPeriodMutation = new ObservedMutation(
        accessReviewPeriodControllerUpdateAccessReviewPeriodMutation,
    );

    updateApplicationDetailsMutation = new ObservedMutation(
        accessReviewApplicationControllerUpdateAccessApplicationDetailsMutation,
    );

    get reviewPeriod(): AccessReviewPeriodResponseDto | null {
        return this.reviewPeriodQuery.data ?? null;
    }

    get isReviewPeriodLoading(): boolean {
        return this.reviewPeriodQuery.isLoading;
    }

    setPeriodId(id: number) {
        this.periodId = id;
    }

    setFormData(data: UpdateReviewPeriodRequestDto) {
        this.formData = data;
    }

    setPeriodDetailsData = (data: {
        startingDate: string;
        endingDate: string;
    }) => {
        this.formData.startingDate = data.startingDate;
        this.formData.endingDate = data.endingDate;
    };

    setSelectedApplicationsData = (
        data: AccessReviewPeriodApplicationResponseDto[],
    ) => {
        this.formData.applications = data.map((app) => ({
            id: app.accessApplicationId,
            applicationId: app.accessApplicationId,
            reviewersIds: app.reviewers.map((reviewer) => reviewer.id),
            accessApplicationId: app.accessApplicationId,
        }));

        this.selectedApplicationsData = data.map((app) => ({
            ...app,
            id: app.accessApplicationId || app.id,
        }));
    };

    updateApplicationDetails = ({
        id,
        name,
        websiteUrl,
        reviewers,
        source,
    }: {
        id: number;
        name: string;
        websiteUrl?: string;
        source: AccessReviewPeriodApplicationResponseDto['source'];
        reviewers: UserResponseDto[];
    }) => {
        const sendData: AccessReviewApplicationRequestDto = {
            name,
            websiteUrl,
            reviewersIds: reviewers.map((reviewer) => reviewer.id),
        };

        if (source !== 'MANUALLY_ADDED') {
            omit(sendData, 'websiteUrl');
        }

        this.updateApplicationDetailsMutation.mutate({
            path: {
                id,
            },
            body: sendData,
        });

        when(
            () => !this.updateApplicationDetailsMutation.isPending,
            () => {
                if (this.updateApplicationDetailsMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: `update-application-details-error-${id}`,
                        props: {
                            title: t`Failed to update application details`,
                            description: t`Unable to update application details`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                this.setSelectedApplicationsData(
                    this.selectedApplicationsData.map((app) => {
                        if (Number(app.id) === id) {
                            return {
                                ...app,
                                id: app.accessApplicationId,
                                name,
                                websiteUrl,
                                reviewers,
                            };
                        }

                        return app;
                    }),
                );

                snackbarController.addSnackbar({
                    id: `update-application-details-success-${uniqueId()}`,
                    props: {
                        title: t`Application details updated successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    loadReviewPeriod = (periodId: number) => {
        if (periodId) {
            this.setPeriodId(periodId);
        }

        this.reviewPeriodQuery.load({
            path: {
                periodId,
            },
        });

        when(
            () => !this.isReviewPeriodLoading,
            () => {
                if (!this.reviewPeriod) {
                    return;
                }

                const startingDate = new Date(this.reviewPeriod.startDate)
                    .toISOString()
                    .split('T')[0];
                const endingDate = new Date(this.reviewPeriod.endDate)
                    .toISOString()
                    .split('T')[0];

                this.setFormData({
                    startingDate,
                    endingDate,
                    applications: this.reviewPeriod.applications.map((app) => ({
                        id: app.accessApplicationId,
                        applicationId: app.accessApplicationId,
                        reviewersIds: app.reviewers.map(
                            (reviewer) => reviewer.id,
                        ),
                        accessApplicationId: app.id,
                    })),
                });

                this.setSelectedApplicationsData(
                    this.reviewPeriod.applications,
                );
            },
        );
    };

    refreshReviewPeriod = () => {
        if (this.reviewPeriodQuery.query) {
            this.reviewPeriodQuery.query.refetch();
        }
    };

    updateReviewPeriod = () => {
        if (!this.periodId) {
            throw new Error('Period ID is required');
        }

        return this.updateAccessReviewPeriodMutation
            .mutateAsync({
                path: {
                    periodId: this.periodId,
                },
                body: this.formData,
            })
            .then(() => {
                this.refreshReviewPeriod();
            })
            .then(() => {
                snackbarController.addSnackbar({
                    id: `update-review-period-success-${uniqueId()}`,
                    props: {
                        title: t`Review period updated successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            })
            .catch((error) => {
                snackbarController.addSnackbar({
                    id: `update-review-period-error-${uniqueId()}`,
                    props: {
                        title: t`Failed to update review period`,
                        description: t`Unable to update review period`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                throw error;
            });
    };
}

export const sharedEditReviewPeriodController =
    new EditReviewPeriodController();
