import { isEmpty, isNil } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import { accessReviewApplicationControllerUpdateApplicationReviewersMutation } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation } from '@globals/mobx';

class AccessReviewsApplicationsReviewersController {
    constructor() {
        makeAutoObservable(this);
    }

    reviewAppId: number | null = null;
    periodId: number | null = null;

    accessReviewApplicationUpdateReviewersMutation = new ObservedMutation(
        accessReviewApplicationControllerUpdateApplicationReviewersMutation,
    );

    setReviewAppId(id: number) {
        this.reviewAppId = id;
    }

    setPeriodId(id: number) {
        this.periodId = id;
    }

    updateApplicationReviewers = (
        reviewersIds: number[],
        onClose: () => void,
    ): Promise<void> => {
        if (isEmpty(reviewersIds)) {
            throw new Error('Reviewers IDs are required');
        }

        if (isNil(this.periodId) || isNil(this.reviewAppId)) {
            throw new Error('Period ID and App ID are required');
        }

        return this.accessReviewApplicationUpdateReviewersMutation
            .mutateAsync({
                path: {
                    periodId: this.periodId,
                    reviewAppId: this.reviewAppId,
                },
                body: { reviewersIds },
            })
            .then(() => {
                snackbarController.addSnackbar({
                    id: 'access-review-reviewers-update-success',
                    props: {
                        title: t`Reviewers updated successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: 'Close',
                    },
                });
                onClose();
            })
            .catch((error: Error) => {
                snackbarController.addSnackbar({
                    id: 'access-review-reviewers-update-error',
                    props: {
                        title: isEmpty(error)
                            ? t`Unable to update reviewers, please try again later`
                            : error.message,
                        severity: 'critical',
                        closeButtonAriaLabel: 'Close',
                    },
                });
                throw error;
            });
    };
}

export const sharedAccessReviewApplicationReviewersController =
    new AccessReviewsApplicationsReviewersController();
