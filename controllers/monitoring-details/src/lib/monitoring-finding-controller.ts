import { isEmpty, isString } from 'lodash-es';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import { monitorsV2ControllerGetFindingsListOptions } from '@globals/api-sdk/queries';
import type { FindingItemResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { processFiltersForAPI } from '../helpers/filter-processing.helper';

class FindingsController {
    constructor() {
        makeAutoObservable(this);
    }

    findingsListQuery = new ObservedQuery(
        monitorsV2ControllerGetFindingsListOptions,
    );

    get findingsList(): FindingItemResponseDto[] {
        return this.findingsListQuery.data?.data ?? [];
    }

    get findingsListTotal(): number {
        return this.findingsListQuery.data?.total ?? 0;
    }

    get isLoadingFindingsList(): boolean {
        return this.findingsListQuery.isLoading;
    }

    load = (
        params: FetchDataResponseParams & { testId: number | string },
    ): void => {
        when(
            () => Boolean(sharedWorkspacesController.currentWorkspace),
            () => {
                const testId = isString(params.testId)
                    ? parseInt(params.testId)
                    : params.testId;

                if (isNaN(testId)) {
                    throw new TypeError(
                        'Invalid testId: must be a valid number',
                    );
                }
                const { pagination, globalFilter, sorting } = params;
                const { page, pageSize } = pagination;
                const { search, filters } = globalFilter;

                // Process filters generically
                const processedFilters = processFiltersForAPI(filters);

                const query: Record<string, unknown> = {
                    page,
                    limit: pageSize,
                    ...(!isEmpty(search) && { q: search }),
                    ...(!isEmpty(filters) && processedFilters),
                };

                if (isEmpty(sorting)) {
                    query.sortDir = 'DESC';
                } else {
                    query.sort = sorting[0].id;
                    query.sortDir = sorting[0].desc ? 'DESC' : 'ASC';
                }

                this.findingsListQuery.load({
                    path: {
                        testId,
                        workspaceId:
                            sharedWorkspacesController.currentWorkspace?.id ??
                            1,
                    },
                    query,
                });
            },
        );
    };
}

export const sharedFindingsController = new FindingsController();
