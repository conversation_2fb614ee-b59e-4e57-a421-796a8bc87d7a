import { isEmpty, isObject } from 'lodash-es';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import type { FilterState } from '@cosmos/components/filter-field';

/**
 * Helper function to extract array values from filter objects.
 */
const extractFilterArrayValues = (
    filterValue: FilterState['value'],
): string[] => {
    if (!filterValue) {
        return [];
    }

    // Handle arrays (checkbox groups, multi-select combobox)
    if (Array.isArray(filterValue)) {
        return filterValue
            .map((item) =>
                isObject(item) && 'value' in item ? item.value : item,
            )
            .filter(Boolean)
            .map(String);
    }

    // Handle objects with value property (select, radio, single combobox)
    if (isObject(filterValue) && 'value' in filterValue) {
        return filterValue.value ? [String(filterValue.value)] : [];
    }

    // Handle primitive values (text, boolean, number, etc.)
    // Only stringify if it's not an object to avoid '[object Object]'
    if (isObject(filterValue)) {
        return [];
    }

    return [String(filterValue)];
};

/**
 * Process globalFilter.filters into API-compatible query parameters.
 * This generically maps filter keys to query parameters without hardcoding.
 */
export const processFiltersForAPI = (
    filters: FetchDataResponseParams['globalFilter']['filters'],
): Record<string, string[]> => {
    const processedFilters: Record<string, string[]> = {};

    Object.entries(filters).forEach(([key, filter]) => {
        if (!filter.value) {
            return;
        }

        const arrayValues = extractFilterArrayValues(filter.value);

        if (!isEmpty(arrayValues)) {
            processedFilters[key] = arrayValues;
        }
    });

    return processedFilters;
};
