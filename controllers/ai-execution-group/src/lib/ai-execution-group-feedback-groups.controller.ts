import { isEmpty } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import {
    aiExecutionGroupFeedbackGroupsControllerGenerateExecutionMutation,
    aiExecutionGroupFeedbackGroupsControllerGetAiExecutionGroupsOptions,
    aiExecutionGroupFeedbackGroupsControllerSaveAiExecutionGroupFeedbackMutation,
    aiExecutionGroupFeedbackGroupsControllerUpdateAiExecutionGroupFeedbackMutation,
} from '@globals/api-sdk/queries';
import type {
    AiExecutionFeedbackCreateRequestDto,
    AiExecutionFeedbackUpdateRequestDto,
    AiExecutionGroupFeedbackGroupCreateRequestDto,
    AiExecutionGroupResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
} from '@globals/mobx';

class AIExecutionGroupFeedbackGroupsController {
    constructor() {
        makeAutoObservable(this);
    }

    createExecutionGroupMutation = new ObservedMutation(
        aiExecutionGroupFeedbackGroupsControllerGenerateExecutionMutation,
    );

    getExecutionGroupQuery = new ObservedQuery(
        aiExecutionGroupFeedbackGroupsControllerGetAiExecutionGroupsOptions,
    );

    /**
     * Feedback functionality.
     */

    saveFeedbackMutation = new ObservedMutation(
        aiExecutionGroupFeedbackGroupsControllerSaveAiExecutionGroupFeedbackMutation,
    );

    updateFeedbackMutation = new ObservedMutation(
        aiExecutionGroupFeedbackGroupsControllerUpdateAiExecutionGroupFeedbackMutation,
    );

    get isCreating(): boolean {
        return this.createExecutionGroupMutation.isPending;
    }

    /**
     * Computed getters for reactive state.
     */

    get isLoading(): boolean {
        return this.getExecutionGroupQuery.isLoading;
    }

    get isSavingFeedback(): boolean {
        return this.saveFeedbackMutation.isPending;
    }

    get isUpdatingFeedback(): boolean {
        return this.updateFeedbackMutation.isPending;
    }

    get isAnyOperationPending(): boolean {
        return (
            this.isLoading ||
            this.isCreating ||
            this.isSavingFeedback ||
            this.isUpdatingFeedback
        );
    }

    private showGeneralErrorSnackbar(): void {
        snackbarController.addSnackbar({
            id: 'ai-feedback-general-error',
            props: {
                title: t`Something went wrong`,
                description: t`An unexpected error occurred. Please try again.`,
                severity: 'critical',
                closeButtonAriaLabel: t`Close`,
            },
        });
    }

    private showSuccessSnackbar(title: string): void {
        snackbarController.addSnackbar({
            id: 'ai-feedback-success',
            props: {
                title,
                description: t`Thank you for your feedback!`,
                severity: 'success',
                closeButtonAriaLabel: t`Close`,
            },
        });
    }

    loadExecutionGroups = ({
        processFeature,
        featureIds,
        executionStatuses,
    }: {
        processFeature?:
            | 'QUESTIONNAIRE_RESPONSE'
            | 'MONITOR_TEST_LOGIC'
            | 'EVENT_TEST_FAILURE'
            | 'EXTRACT_SECURITY_QUESTIONS'
            | 'ANSWERING_QUESTION'
            | 'ANSWER_SECURITY_QUESTION'
            | 'MONITOR_TEST_INSTRUCTIONS'
            | 'MONITOR_TEST_TEMPLATE_INSTRUCTIONS';
        featureIds?: string[];
        executionStatuses?: (
            | 'PENDING'
            | 'INPROGRESS'
            | 'COMPLETED'
            | 'ERROR'
        )[];
    } = {}): void => {
        this.getExecutionGroupQuery.load({
            query: {
                ...(processFeature && { processFeature }),
                ...(featureIds && !isEmpty(featureIds) && { featureIds }),
                ...(executionStatuses &&
                    !isEmpty(executionStatuses) && { executionStatuses }),
                page: 1,
                limit: 20,
            },
        });
    };

    invalidateExecutionGroups = (): void => {
        this.getExecutionGroupQuery.invalidate();
    };

    get executionGroups(): AiExecutionGroupResponseDto[] {
        return this.getExecutionGroupQuery.data?.data ?? [];
    }

    createExecutionGroup = ({
        data,
        feedback,
        onSuccess,
    }: {
        data: AiExecutionGroupFeedbackGroupCreateRequestDto;
        feedback: AiExecutionFeedbackCreateRequestDto;
        onSuccess: () => void;
    }): void => {
        this.createExecutionGroupMutation
            .mutateAsync({
                body: data,
            })
            .then(() => {
                const newExecutionGroup =
                    this.createExecutionGroupMutation.response;

                if (!newExecutionGroup) {
                    return;
                }

                this.saveFeedback({
                    aiExecutionGroupId: newExecutionGroup.id,
                    feedback,
                    onSuccess,
                });
            })
            .catch(() => {
                this.showGeneralErrorSnackbar();
            })
            .finally(() => {
                this.invalidateExecutionGroups();
            });
    };

    /**
     * Feedback actions.
     */

    saveFeedback = ({
        aiExecutionGroupId,
        feedback,
        onSuccess,
    }: {
        aiExecutionGroupId: number;
        feedback: AiExecutionFeedbackCreateRequestDto;
        onSuccess: () => void;
    }): void => {
        this.saveFeedbackMutation
            .mutateAsync({
                path: { aiExecutionGroupId },
                body: feedback,
            })
            .then(() => {
                this.showSuccessSnackbar(t`Feedback submitted successfully`);
                onSuccess();
            })
            .catch(() => {
                this.showGeneralErrorSnackbar();
            })
            .finally(() => {
                this.invalidateExecutionGroups();
            });
    };

    updateFeedback = ({
        aiExecutionGroupId,
        feedbackId,
        feedback,
        onSuccess,
    }: {
        aiExecutionGroupId: number;
        feedbackId: number;
        feedback: AiExecutionFeedbackUpdateRequestDto;
        onSuccess: () => void;
    }): void => {
        this.updateFeedbackMutation
            .mutateAsync({
                path: { aiExecutionGroupId, feedbackId },
                body: feedback,
            })
            .then(() => {
                this.invalidateExecutionGroups();
                this.showSuccessSnackbar(t`Feedback updated successfully`);
                onSuccess();
            })
            .catch(() => {
                this.showGeneralErrorSnackbar();
            })
            .finally(() => {
                this.invalidateExecutionGroups();
            });
    };

    submitFeedback = ({
        testId,
        feedbackStatus,
        feedbackText,
        feedbackReason,
        onSuccess,
    }: {
        testId: string;
        feedbackStatus: 'USEFUL' | 'NOT_USEFUL';
        feedbackText: string;
        feedbackReason?: ('NOT_ACCURATE' | 'NOT_HELPFUL' | 'OTHER')[];
        onSuccess: () => void;
    }): void => {
        const feedbackData = {
            status: feedbackStatus,
            opinion: feedbackText,
            reasons: feedbackReason,
        };

        if (isEmpty(this.executionGroups)) {
            // Create execution group first
            this.createExecutionGroup({
                data: {
                    processFeature: 'MONITOR_TEST_TEMPLATE_INSTRUCTIONS',
                    featureId: testId,
                },
                feedback: feedbackData,
                onSuccess,
            });
        } else {
            // There's always only one execution group
            const firstGroup = this.executionGroups[0];

            if (isEmpty(firstGroup.feedbacks)) {
                this.saveFeedback({
                    aiExecutionGroupId: firstGroup.id,
                    feedback: feedbackData,
                    onSuccess,
                });
            } else {
                this.updateFeedback({
                    aiExecutionGroupId: firstGroup.id,
                    // There's always only one feedback
                    feedbackId: firstGroup.feedbacks[0].id,
                    feedback: feedbackData,
                    onSuccess,
                });
            }
        }
    };
}

export const sharedAIExecutionGroupFeedbackGroupsController =
    new AIExecutionGroupFeedbackGroupsController();
