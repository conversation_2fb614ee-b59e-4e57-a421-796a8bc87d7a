import { grcControllerGetRequirementOptions } from '@globals/api-sdk/queries';
import type { RequirementDetailResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

class RequirementDetailsController {
    constructor() {
        makeAutoObservable(this);
    }

    requirementDetailsQuery = new ObservedQuery(
        grcControllerGetRequirementOptions,
    );

    loadRequirementById(id: number): void {
        this.requirementDetailsQuery.load({ path: { requirementId: id } });
    }

    get requirement(): RequirementDetailResponseDto | null {
        return this.requirementDetailsQuery.data;
    }

    get isRequirementLoading(): boolean {
        return this.requirementDetailsQuery.isLoading;
    }

    get isRequirementFetching(): boolean {
        return this.requirementDetailsQuery.isFetching;
    }
}

export const sharedRequirementDetailsController =
    new RequirementDetailsController();
