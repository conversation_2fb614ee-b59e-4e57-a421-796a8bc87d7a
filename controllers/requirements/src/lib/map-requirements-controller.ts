import { isEmpty, isString } from 'lodash-es';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { action, makeAutoObservable } from '@globals/mobx';
import { sharedRequirementsController } from './requirements-controller';
import { sharedRequirementsInfiniteController } from './requirements-infinite-controller';

class MapRequirementsController {
    constructor() {
        makeAutoObservable(this);
    }

    mapRequirementsToFramework = (
        frameworkId: number,
        selectedRequirements: ListBoxItemData[],
    ): void => {
        action(() => {
            const { mappedRequirements, requirements } =
                sharedRequirementsController;
            const { requirementsInfiniteList } =
                sharedRequirementsInfiniteController;

            const otherFrameworkRequirements = mappedRequirements.filter(
                (req) => req.frameworkId !== frameworkId,
            );

            if (
                Array.isArray(selectedRequirements) &&
                !isEmpty(selectedRequirements)
            ) {
                const requirementsToAdd = selectedRequirements.map((item) => {
                    const requirementId = Number(item.value);

                    // First try to find the requirement in the current data
                    let requirement = [
                        ...requirementsInfiniteList,
                        ...requirements,
                    ].find((req) => req.id === requirementId);

                    // If not found in current data, create a minimal requirement object
                    // from the ComboboxField item data since the ComboboxField ensures
                    // only valid options can be selected
                    requirement ??= {
                        id: requirementId,
                        name: item.label,
                        description: isString(item.description)
                            ? item.description
                            : '',
                        frameworkId,
                        frameworkName: '',
                        longDescription: '',
                        additionalInfo: '',
                        additionalInfo2: '',
                        additionalInfo3: '',
                        isReady: false,
                        rationale: undefined,
                        archivedAt: undefined,
                        controls: [],
                        totalInScopeControls: 0,
                        isParameterCompleted: false,
                    };

                    return {
                        ...requirement,
                        frameworkId,
                    };
                });

                sharedRequirementsController.mappedRequirements = [
                    ...otherFrameworkRequirements,
                    ...requirementsToAdd,
                ];
            } else {
                sharedRequirementsController.mappedRequirements =
                    otherFrameworkRequirements;
            }
        })();
    };
}

export const sharedMapRequirementsController = new MapRequirementsController();
