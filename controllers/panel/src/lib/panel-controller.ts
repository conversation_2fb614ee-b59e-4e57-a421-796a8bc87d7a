import { routeController } from '@controllers/route';
import { makeAutoObservable, reaction } from '@globals/mobx';

export class PanelController {
    currentPanelId: string | null = null;
    queryParams: Record<string, unknown> = {};
    content: (() => JSX.Element) | null = null;
    routeReactionDisposer?: () => void;

    constructor() {
        makeAutoObservable(this);

        this.setupRouteReaction();
    }

    openPanel({
        id,
        queryParams,
        content,
    }: {
        id: string;
        queryParams?: Record<string, unknown>;
        content?: () => JSX.Element;
    }): void {
        this.currentPanelId = id;
        this.queryParams = queryParams ?? {};
        this.content = content ?? null;
    }

    closePanel = (): void => {
        this.currentPanelId = null;
        this.queryParams = {};
        this.content = null;
    };

    /**
     * Set up reaction to route changes for clearing any open panels.
     */
    private setupRouteReaction(): void {
        this.routeReactionDisposer = reaction(
            () => routeController.matches,
            () => {
                this.closePanel();
            },
            {
                fireImmediately: false, // Don't fire on initial setup
            },
        );
    }

    dispose(): void {
        // Dispose route reaction
        this.routeReactionDisposer?.();
    }
}

export const panelController = new PanelController();
