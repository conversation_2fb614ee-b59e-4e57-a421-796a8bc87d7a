import { snackbarController } from '@controllers/snackbar';
import { policiesControllerCreateCustomPolicyMutation } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import {
    makeAutoObservable,
    ObservedMutation,
    toJS,
    when,
} from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { PolicyFormModel } from '@models/policies';
import type { NavigateFunction } from '@remix-run/react';
import type {
    CreatePolicyFormData,
    CreatePolicyRequestDto,
} from './types/create-policy.types';

/**
 * Controller for managing policy creation workflow.
 * Follows Multiverse MobX patterns with ObservedMutation and proper state management.
 */
export class CreatePolicyController {
    // ===== OBSERVABLE STATE =====

    /**
     * Form model for reactive form state management.
     */
    formModel = new PolicyFormModel();

    /**
     * Form data for each wizard step.
     */
    formData: CreatePolicyFormData | null = null;

    /**
     * Current step in the wizard (0-based index).
     */
    currentStep = 0;

    // ===== MUTATIONS =====

    /**
     * Mutation for creating policy.
     */
    createPolicyMutation = new ObservedMutation(
        policiesControllerCreateCustomPolicyMutation,
    );

    isExternal = false;

    constructor() {
        makeAutoObservable(this);
    }

    // ===== COMPUTED PROPERTIES =====

    /**
     * Whether the controller is currently creating a policy.
     */
    get isCreatingPolicy(): boolean {
        return this.createPolicyMutation.isPending;
    }

    /**
     * Form data formatted for API submission.
     */
    get apiFormData(): CreatePolicyRequestDto | null {
        if (!this.formData) {
            logger.error({
                message: 'Failed to create policy. Missing form data',
            });

            return null;
        }

        const {
            source,
            details,
            personnelGroups,
            replacePolicies,
            externalSource,
        } = this.formData;

        let groupIds: string[] = [];
        let notifyGroups = false;
        const externalFileId = externalSource.externalFileId ?? undefined;
        const { assignedTo } = personnelGroups;

        if (assignedTo === 'GROUP') {
            const { selectedGroups, notifyNewMembers } = personnelGroups;

            groupIds = selectedGroups
                ? selectedGroups.map((group) => group.value)
                : [];
            notifyGroups = Boolean(notifyNewMembers);
        }

        const {
            owner: { id: ownerId },
            description,
            name,
            renewalDate,
        } = details;

        let sourceType: CreatePolicyRequestDto['sourceType'] = 'EXTERNAL';
        let uploadedFile: File | undefined;

        if (!this.isExternal) {
            sourceType = source.sourceType;
            uploadedFile = source.uploadedFile;
        }

        return {
            name: name.trim(),
            description: description.trim(),
            renewalDate: renewalDate.renewalDate,
            ownerId: Number(ownerId),
            sourceType,
            assignedTo,
            groupIds,
            notifyGroups,
            material: false,
            replacedPoliciesIds:
                replacePolicies.shouldReplacePolicies === 'yes'
                    ? (replacePolicies.policiesToReplace?.map(
                          (policy) => policy.id,
                      ) ?? [])
                    : [],
            ...(uploadedFile && { file: uploadedFile }),
            ...(externalFileId && { externalFileId }),
        };
    }

    // ===== ACTIONS =====

    /**
     * Initialize the controller and load required data.
     */
    initialize = (): void => {
        this.resetForm();
        this.formModel.loadUsers();
    };

    /**
     * Update form data for a specific step.
     */
    updateStepData = <K extends keyof CreatePolicyFormData>(
        step: K,
        data: CreatePolicyFormData[K],
    ): void => {
        if (!this.formData) {
            logger.error({
                message: 'Failed to update step data. Missing form data',
            });

            return;
        }

        this.formData[step] = data;
        this.formModel.setReferenceToModal(toJS(this.formData));
    };

    /**
     * Creates a new policy with navigation workflow.
     */
    createPolicyWorkflow = (
        navigate: NavigateFunction,
        workspaceId: string,
    ): void => {
        const formData = this.apiFormData;

        if (!formData) {
            logger.error({
                message: 'Failed to create policy. Missing form data',
            });
            snackbarController.addSnackbar({
                id: 'create-policy-error',
                props: {
                    title: t`Error creating policy`,
                    description: t`Failed to create policy. Please try again.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        if (this.isExternal) {
            openConfirmationModal({
                onConfirm: () => {
                    this.createPolicy(navigate, workspaceId, formData);
                },
                onCancel: () => {
                    closeConfirmationModal();
                },
                title: t`Publish policy`,
                body: t`You're importing a policy that has been created in BambooHR, which handles policy acknowledgment. This will immediately publish the policy in Drata.`,
                confirmText: t`Import`,
                cancelText: t`Cancel`,
                type: 'primary',
                size: 'md',
                disableClickOutsideToClose: true,
                isLoading: () => this.isCreatingPolicy,
            });
        } else {
            this.createPolicy(navigate, workspaceId, formData);
        }
    };

    createPolicy(
        navigate: NavigateFunction,
        workspaceId: string,
        body: CreatePolicyRequestDto,
    ): void {
        this.createPolicyMutation.mutate({
            body,
        });
        when(
            () => !this.isCreatingPolicy,
            () => {
                if (this.createPolicyMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'create-policy-error',
                        props: {
                            title: t`Error creating policy`,
                            description: t`Failed to create policy. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                if (this.isExternal) {
                    closeConfirmationModal();
                }

                snackbarController.addSnackbar({
                    id: 'create-policy-success',
                    props: {
                        title: this.isExternal
                            ? t`Policy imported successfully`
                            : t`Policy created successfully`,
                        description: t`Your policy has been created and is now available.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                // Navigate to policies list after successful creation
                navigate(`/workspaces/${workspaceId}/governance/policies`);
            },
        );
    }

    /**
     * Cancel the wizard and reset form.
     */
    cancel = (): void => {
        this.resetForm();
    };

    /**
     * Cancel the wizard with navigation workflow.
     */
    cancelWorkflow = (
        navigate: NavigateFunction,
        workspaceId: string,
    ): void => {
        this.resetForm();
        navigate(`/workspaces/${workspaceId}/governance/policies`);
    };

    /**
     * Reset the form to initial state.
     */
    resetForm = (): void => {
        this.formData = {
            source: {
                sourceType: this.isExternal ? 'EXTERNAL' : 'BUILDER',
            },
            details: {
                name: '',
                description: '',
                renewalDate: {
                    renewalFrequency: {
                        id: 'ONE_YEAR',
                        label: 'One Year',
                        value: 'ONE_YEAR',
                    },
                    renewalDate: '',
                },
                owner: {
                    id: '',
                    label: '',
                    value: '',
                },
            },
            personnelGroups: {
                assignedTo: 'ALL',
                selectedGroups: [],
                notifyNewMembers: false,
            },
            externalSource: {
                providerType: '',
                externalFileId: '',
            },
            replacePolicies: {
                shouldReplacePolicies: 'no',
                policiesToReplace: [],
            },
        };
        this.currentStep = 0;
        this.formModel.reset();
    };

    setExternal = (isExternal: boolean): void => {
        this.isExternal = isExternal;
    };
}

export const sharedCreatePolicyController = new CreatePolicyController();
