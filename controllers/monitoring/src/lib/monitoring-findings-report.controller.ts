import { snackbarController } from '@controllers/snackbar';
import {
    monitorsControllerGetCspmMonitorFailDetailsReportOptions,
    monitorsControllerGetEdrMonitorFailDetailsReportOptions,
    monitorsControllerGetMonitorFailedResultsReportOptions,
} from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { fileNameDate } from '@helpers/date-time';
import { downloadBlob } from '@helpers/download-file';

interface DownloadParams {
    testId: number;
    testName: string;
    checkType: string;
    type?: string;
}

class MonitoringFindingsReportController {
    constructor() {
        makeAutoObservable(this);
    }

    monitoringFailedFindingsReportQuery = new ObservedQuery(
        monitorsControllerGetMonitorFailedResultsReportOptions,
    );

    monitoringEdrReportQuery = new ObservedQuery(
        monitorsControllerGetEdrMonitorFailDetailsReportOptions,
    );

    monitoringCspmReportQuery = new ObservedQuery(
        monitorsControllerGetCspmMonitorFailDetailsReportOptions,
    );

    downloadEdrFailedTestReport = (testId: number) => {
        this.monitoringEdrReportQuery.load({
            path: { testId },
        });

        const fileName = `Malware-Detection-Test-Tailed-Results-${fileNameDate()}.csv`;

        this.handleFindingsQueryDownload({
            query: this.monitoringEdrReportQuery,
            fileName,
        });
    };

    downloadCspmFailedTestReport = (testId: number) => {
        this.monitoringCspmReportQuery.load({
            path: { testId },
        });

        const fileName = `Cloud_Security_Posture_Management_Test-Failed-Results-${fileNameDate()}.csv`;

        this.handleFindingsQueryDownload({
            query: this.monitoringCspmReportQuery,
            fileName,
        });
    };

    downloadFailedFindings = ({
        testId,
        testName,
        checkType,
        type = 'included',
    }: DownloadParams) => {
        this.monitoringFailedFindingsReportQuery.load({
            path: { testId },
            query: {
                type,
                checkType,
            },
        });

        const fileName = `Failing-Resources-For-Test-${testId}-${testName.split(' ').join('-')}-${fileNameDate()}.csv`;

        this.handleFindingsQueryDownload({
            query: this.monitoringFailedFindingsReportQuery,
            fileName,
        });
    };

    handleFindingsQueryDownload = ({
        query,
        fileName,
    }: {
        query: { error: unknown; data?: unknown; isLoading: boolean };
        fileName: string;
    }): void => {
        when(() => !query.isLoading)
            .then(() => {
                if (!query.data) {
                    snackbarController.addSnackbar({
                        id: 'no-result-findings-download-error',
                        props: {
                            title: t`No result`,
                            description: t`No findings found to download.`,
                            severity: 'warning',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                const blob = new Blob([query.data as unknown as string], {
                    type: 'text/csv',
                });

                downloadBlob(blob, fileName);

                snackbarController.addSnackbar({
                    id: 'findings-download-success',
                    props: {
                        title: t`Download successful`,
                        description: t`Findings downloaded successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            })
            .catch(() => {
                logger.error(
                    `Failed to download failed findings: ${query.error as string}`,
                );

                snackbarController.addSnackbar({
                    id: 'failed-findings-download-error',
                    props: {
                        title: t`Download failed`,
                        description: t`Unable to download failed findings. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };
}

export const sharedMonitoringFindingsReportController =
    new MonitoringFindingsReportController();
