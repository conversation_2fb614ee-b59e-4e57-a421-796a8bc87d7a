import { isEmpty } from 'lodash-es';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import { riskLibraryControllerGetRisksLibraryListOptions } from '@globals/api-sdk/queries';
import type { RiskLibraryResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';
import { LOAD_RISK_LIBRARY_INITIAL_PARAMS } from '../constants/risk-owner-roles.constant';
import { formatRiskLibraryFilters } from '../helpers/format-risk-filters';

class RiskLibraryController {
    constructor() {
        makeAutoObservable(this);
    }

    riskLibraryListQuery = new ObservedQuery(
        riskLibraryControllerGetRisksLibraryListOptions,
    );

    get isLoading(): boolean {
        return this.riskLibraryListQuery.isLoading;
    }

    get total(): number {
        return this.riskLibraryListQuery.data?.total ?? 0;
    }

    get risks(): RiskLibraryResponseDto[] {
        return this.riskLibraryListQuery.data?.data ?? [];
    }

    loadRiskLibrary = (params: FetchDataResponseParams) => {
        const { filters, search } = params.globalFilter;
        const categoriesIds = isEmpty(filters.categories)
            ? undefined
            : formatRiskLibraryFilters(filters);

        this.riskLibraryListQuery.load({
            query: {
                page: params.pagination.page,
                limit: params.pagination.pageSize,
                q: search ?? '',
                categoriesIds,
            },
        });
    };

    loadInitialData = () => {
        this.loadRiskLibrary(LOAD_RISK_LIBRARY_INITIAL_PARAMS);
    };
}

export const sharedRiskLibraryController = new RiskLibraryController();
