import { riskManagementControllerGetRiskCategoriesOptions } from '@globals/api-sdk/queries';
import type { RiskCategoryResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

class RiskLibraryCategoriesController {
    constructor() {
        makeAutoObservable(this);
    }

    riskLibraryCategoriesQuery = new ObservedQuery(
        riskManagementControllerGetRiskCategoriesOptions,
    );

    get isLoading(): boolean {
        return this.riskLibraryCategoriesQuery.isLoading;
    }

    get categories(): RiskCategoryResponseDto[] {
        return this.riskLibraryCategoriesQuery.data?.data ?? [];
    }

    loadCategories = () => {
        this.riskLibraryCategoriesQuery.load();
    };
}

export const sharedRiskLibraryCategoriesController =
    new RiskLibraryCategoriesController();
