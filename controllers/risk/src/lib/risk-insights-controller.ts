import {
    riskManagementControllerGetDashboardOptions,
    riskManagementControllerGetRiskDashboardReportMutation,
} from '@globals/api-sdk/queries';
import type {
    DashboardResponseDto,
    RiskDashboardReportRequestDto,
} from '@globals/api-sdk/types';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

class RiskInsightsController {
    constructor() {
        makeAutoObservable(this);
    }

    riskInsightsQuery = new ObservedQuery(
        riskManagementControllerGetDashboardOptions,
    );

    riskInsightsReportMutation = new ObservedMutation(
        riskManagementControllerGetRiskDashboardReportMutation,
    );

    get isLoading(): boolean {
        return this.riskInsightsQuery.isLoading;
    }

    get riskInsights(): DashboardResponseDto | null {
        return this.riskInsightsQuery.data;
    }

    get isDownloadLoading(): boolean {
        return this.riskInsightsReportMutation.isPending;
    }

    get hasError(): boolean {
        return this.riskInsightsReportMutation.hasError;
    }

    downloadRiskInsightsReport = () => {
        const { currentWorkspace } = sharedWorkspacesController;

        if (!currentWorkspace || !this.riskInsights?.treatmentOverview) {
            return;
        }

        const riskTreatment = this.riskInsights.treatmentOverview;

        const requestBody: RiskDashboardReportRequestDto = {
            treatmentOverview: {
                untreated: Number(riskTreatment.UNTREATED) || 0,
                accepted: Number(riskTreatment.ACCEPT) || 0,
                transferred: Number(riskTreatment.TRANSFER) || 0,
                avoided: Number(riskTreatment.AVOID) || 0,
                mitigate: Number(riskTreatment.MITIGATE) || 0,
            },
        };

        this.riskInsightsReportMutation.mutate({
            body: requestBody,
        });
    };

    initialize = () => {
        when(
            () => sharedWorkspacesController.isReady,
            () => {
                this.load();
            },
        );
    };

    load = () => {
        this.riskInsightsQuery.load({ query: { isScored: true } });
    };
}

export const sharedRiskInsightsController = new RiskInsightsController();
