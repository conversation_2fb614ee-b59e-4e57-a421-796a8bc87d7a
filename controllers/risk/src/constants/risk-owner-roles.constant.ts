import {
    DEFAULT_PAGE_SIZE,
    DEFAULT_PAGE_SIZE_OPTIONS,
    type FetchDataResponseParams,
} from '@cosmos/components/datatable';
import type { RoleEnum } from '@globals/api-sdk/types';

export const RISK_OWNER_ROLES: RoleEnum[] = [
    'ADMIN',
    'TECHGOV',
    'RISK_MANAGER',
    'SERVICE_USER',
    'WORKSPACE_ADMINISTRATOR',
];

export const LOAD_RISK_LIBRARY_INITIAL_PARAMS: FetchDataResponseParams = {
    pagination: {
        page: 1,
        pageSize: DEFAULT_PAGE_SIZE,
        pageIndex: 0,
        pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
    },
    globalFilter: {
        search: '',
        filters: {},
    },
    sorting: [],
};
