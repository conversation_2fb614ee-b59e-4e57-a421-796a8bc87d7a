import type { FetchDataResponseParams } from '@cosmos/components/datatable';

export const formatRiskLibraryFilters = (
    filters: FetchDataResponseParams['globalFilter']['filters'],
): number[] | undefined => {
    const categories = filters.categories.value as
        | { value: string; id: string; label: string }[]
        | undefined;

    if (!categories) {
        return undefined;
    }

    return categories.map((item) => {
        return Number(item.value);
    });
};
