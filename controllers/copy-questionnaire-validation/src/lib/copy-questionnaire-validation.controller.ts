import { debounce } from 'lodash-es';
import { questionnaireVendorSecurityTypeformControllerQuestionnaireTitleCheckOptions } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedQuery, runInAction } from '@globals/mobx';

// Simple validation controller for the modal
export class CopyQuestionnaireValidationController {
    titleValidationQuery = new ObservedQuery(
        questionnaireVendorSecurityTypeformControllerQuestionnaireTitleCheckOptions,
    );

    lastValidatedTitle = '';

    constructor() {
        makeAutoObservable(this);
    }

    getTitleValidationError(currentTitle: string): string | null {
        // Only show error if the current title matches the last validated title
        if (currentTitle.trim() !== this.lastValidatedTitle) {
            return null;
        }

        if (this.titleValidationQuery.error) {
            // On error, allow the title (don't block the user)
            return null;
        }

        if (this.titleValidationQuery.data?.isFound) {
            return t`The questionnaire name you entered is already in use.`;
        }

        return null;
    }

    handleValidateTitle = (title: string): void => {
        runInAction(() => {
            if (!title.trim()) {
                this.lastValidatedTitle = '';

                return;
            }

            const trimmedTitle = title.trim();

            this.lastValidatedTitle = trimmedTitle;

            this.titleValidationQuery.load({
                query: { title: trimmedTitle },
            });
        });
    };

    validateTitle = debounce(this.handleValidateTitle, 300);
}

export const sharedCopyQuestionnaireValidationController =
    new CopyQuestionnaireValidationController();
