import { sharedAuditHubControlsController } from '@controllers/audit-hub';
import { sharedCustomerRequestsController } from '@controllers/customer-requests';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { snackbarController } from '@controllers/snackbar';
import {
    auditHubControllerGenerateRequestControlEvidencePackageMutation,
    auditHubControllerGetAuditCustomerRequestControlEvidencesOptions,
    customerRequestControllerAssignControlsToAuditRequestMutation,
    customerRequestControllerDeleteCustomerRequestsMutation,
    customerRequestControllerGetCustomerRequestDetailsWithFrameworkOptions,
    customerRequestControllerGetCustomerRequestEvidencesOptions,
    customerRequestControllerUpdateCustomerRequestDetailsMutation,
    customerRequestControllerUpdateCustomerRequestStatusMutation,
} from '@globals/api-sdk/queries';
import type {
    AuditCustomerRequestControlEvidencePackageRequestDto,
    AuditHubEvidenceResponseDto,
    CustomerRequestDetailsWithFrameworkResponseDto,
    CustomerRequestEvidenceResponseDto,
    DeleteCustomerRequestsRequestDto,
    UpdateCustomerRequestDetailsRequestDto,
    UpdateCustomerRequestStatusesRequestDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { getStatusOptions } from '@models/request-details-page-header';

class CustomerRequestDetailsController {
    auditorId: string | null = '';
    clientId: string | null = '';
    auditorFrameworkId: string | null = '';
    requestId: number | null = null;
    isEvidencePackageDownloading = false;

    customerQuery = new ObservedQuery(
        customerRequestControllerGetCustomerRequestDetailsWithFrameworkOptions,
    );

    updateDetailsMutation = new ObservedMutation(
        customerRequestControllerUpdateCustomerRequestDetailsMutation,
    );

    updateStatusMutation = new ObservedMutation(
        customerRequestControllerUpdateCustomerRequestStatusMutation,
    );

    customerRequestEvidenceQuery = new ObservedQuery(
        customerRequestControllerGetCustomerRequestEvidencesOptions,
    );

    generateRequestControlEvidencePackageMutation = new ObservedMutation(
        auditHubControllerGenerateRequestControlEvidencePackageMutation,
    );

    deleteCustomerRequestMutation = new ObservedMutation(
        customerRequestControllerDeleteCustomerRequestsMutation,
    );

    assignControlsToAuditRequestMutation = new ObservedMutation(
        customerRequestControllerAssignControlsToAuditRequestMutation,
    );

    controlEvidencesQuery = new ObservedQuery(
        auditHubControllerGetAuditCustomerRequestControlEvidencesOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    get customerRequestDetails(): CustomerRequestDetailsWithFrameworkResponseDto | null {
        return this.customerQuery.data;
    }

    get isLoading(): boolean {
        return this.customerQuery.isLoading;
    }

    get isSaving(): boolean {
        return this.updateDetailsMutation.isPending;
    }

    get customerRequestEvidences(): CustomerRequestEvidenceResponseDto[] {
        return this.customerRequestEvidenceQuery.data?.evidences ?? [];
    }

    get customerRequestEvidenceIsLoading(): boolean {
        return this.customerRequestEvidenceQuery.isLoading;
    }

    get getFrameworkId(): string | null {
        return this.auditorFrameworkId;
    }

    get getAuditorId(): string | null {
        return this.auditorId;
    }

    get getRequestId(): number | null {
        return this.requestId;
    }

    get controlEvidences(): AuditHubEvidenceResponseDto[] {
        const data = this.controlEvidencesQuery.data as {
            data?: AuditHubEvidenceResponseDto[];
        } | null;

        return data?.data ?? [];
    }

    get controlEvidencesIsLoading(): boolean {
        return this.controlEvidencesQuery.isLoading;
    }

    /**
     * Loads control evidences for a specific control.
     */
    loadControlEvidences = (controlId: number): void => {
        if (this.auditorFrameworkId && this.requestId && controlId) {
            this.controlEvidencesQuery.load({
                path: {
                    auditId: String(this.auditorFrameworkId),
                    customerRequestId: Number(this.requestId),
                    controlId: Number(controlId),
                },
            });
        }
    };

    updateCustomerRequestDetails = (
        requestId: number,
        updateData: UpdateCustomerRequestDetailsRequestDto,
        onSuccess?: () => void,
    ): void => {
        this.updateDetailsMutation.mutate({
            path: { customerRequestId: requestId },
            body: updateData,
        });

        when(
            () => !this.updateDetailsMutation.isPending,
            () => {
                if (this.updateDetailsMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'customer-request-update-error',
                        props: {
                            title: t`Update Failed`,
                            description: t`An error occurred while updating the customer request. Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                this.customerQuery.invalidate();

                snackbarController.addSnackbar({
                    id: 'customer-request-updated',
                    props: {
                        title: t`Customer request updated successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                // Optional callback for additional actions
                onSuccess?.();
            },
        );
    };

    updateCustomerRequestStatus = (
        statusData: UpdateCustomerRequestStatusesRequestDto,
    ) => {
        this.updateStatusMutation.mutate({
            body: statusData,
        });

        when(
            () => !this.updateStatusMutation.isPending,
            () => {
                if (this.updateStatusMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'customer-request-status-update-error',
                        props: {
                            title: t`Update Failed`,
                            description: t`An error occurred while updating the customer request status. Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                this.customerQuery.invalidate();
                sharedCustomerRequestsController.customerRequestListQuery.invalidate();
                snackbarController.addSnackbar({
                    id: 'customer-request-status-updated',
                    props: {
                        title: t`Customer request status updated successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    updateCustomerRequestStatusOnCell = (
        statusData: UpdateCustomerRequestStatusesRequestDto,
    ) => {
        this.updateStatusMutation.mutate({
            body: statusData,
        });

        when(
            () => !this.updateStatusMutation.isPending,
            () => {
                if (this.updateStatusMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'customer-request-status-update-error',
                        props: {
                            title: t`Update Failed`,
                            description: t`An error occurred while updating the customer request status. Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    sharedCustomerRequestsController.customerRequestListQuery.invalidate();
                    snackbarController.addSnackbar({
                        id: 'customer-request-status-updated-on-cell',
                        props: {
                            title: t`Customer request status updated successfully.`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }
            },
        );
    };

    generateRequestControlEvidencePackage(
        requestDto: AuditCustomerRequestControlEvidencePackageRequestDto,
    ) {
        this.generateRequestControlEvidencePackageMutation.mutate({
            path: {
                auditId: this.auditorFrameworkId as string,
                customerRequestId: this.requestId as number,
            },
            body: requestDto,
        });
        when(
            () => !this.generateRequestControlEvidencePackageMutation.isPending,
            () => {
                if (
                    this.generateRequestControlEvidencePackageMutation.hasError
                ) {
                    snackbarController.addSnackbar({
                        id: 'generate-evidence-package-error',
                        props: {
                            title: t`Generation Failed`,
                            description: t`An error occurred while generating the evidence package. Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        id: 'generating-evidence-package',
                        props: {
                            title: t`Generating evidence package...`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }
            },
        );
    }

    deleteCustomerRequests = (requestDto: DeleteCustomerRequestsRequestDto) => {
        this.deleteCustomerRequestMutation.mutate({
            body: requestDto,
        });

        when(
            () => !this.deleteCustomerRequestMutation.isPending,
            () => {
                if (this.deleteCustomerRequestMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'delete-customer-request-error',
                        props: {
                            title: t`Delete Failed`,
                            description: t`An error occurred while deleting the customer request. Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                this.customerQuery.invalidate();
                sharedCustomerRequestsController.customerRequestListQuery.invalidate();
                snackbarController.addSnackbar({
                    id: 'customer-request-deleted',
                    props: {
                        title: t`Customer request deleted successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                sharedProgrammaticNavigationController.navigateTo(
                    `/audit-hub/clients/${this.clientId}/audits/${this.auditorFrameworkId}/details`,
                );
            },
        );
    };

    handleDeleteWithConfirmation = (
        customerRequestId: number,
        cellAction: boolean,
    ): void => {
        const modalTitleText = cellAction
            ? t`Delete Confirmation`
            : t`Are you sure?`;
        const modalBodyText = cellAction
            ? t`Are you sure?`
            : t`Files your team uploaded directly to this request will be permanently deleted. Evidence already linked to a control won’t be affected.`;
        const modalConfirmText = cellAction ? t`Ok` : t`Yes, I'm sure`;

        openConfirmationModal({
            title: modalTitleText,
            body: modalBodyText,
            confirmText: modalConfirmText,
            cancelText: t`Cancel`,
            type: 'danger',
            size: 'md',
            onConfirm: () => {
                this.deleteCustomerRequests({
                    requestIdList: [customerRequestId],
                });
                closeConfirmationModal();
            },
            onCancel: closeConfirmationModal,
        });
    };

    /**
     * Updates customer request status with proper navigation handling.
     */
    updateCustomerRequestStatusWithNavigation = (
        statusId: string,
        requestId: number,
        cellAction = false,
    ): void => {
        if (!requestId) {
            return;
        }

        const customerRequestId = Number(requestId);

        // Determine which methods to use based on cellAction
        const updateFunction = cellAction
            ? this.updateCustomerRequestStatusOnCell
            : this.updateCustomerRequestStatus;

        const statusOptions = getStatusOptions();

        switch (statusId) {
            case statusOptions.IN_REVIEW.id: {
                updateFunction({
                    requestIds: [customerRequestId],
                    status: statusOptions.IN_REVIEW
                        .id as UpdateCustomerRequestStatusesRequestDto['status'],
                });
                break;
            }

            case statusOptions.OUTSTANDING.id: {
                updateFunction({
                    requestIds: [customerRequestId],
                    status: statusOptions.OUTSTANDING
                        .id as UpdateCustomerRequestStatusesRequestDto['status'],
                });
                break;
            }

            case statusOptions.ACCEPTED.id: {
                updateFunction({
                    requestIds: [customerRequestId],
                    status: statusOptions.ACCEPTED
                        .id as UpdateCustomerRequestStatusesRequestDto['status'],
                });
                break;
            }

            case statusOptions.DELETE.id: {
                this.handleDeleteWithConfirmation(
                    customerRequestId,
                    cellAction,
                );

                break;
            }

            default: {
                snackbarController.addSnackbar({
                    id: 'invalid-status-error',
                    props: {
                        title: t`Invalid Status`,
                        description: t`Please select a valid status.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                break;
            }
        }
    };

    updateRequestControls = (controlIds: number[]) => {
        when(() => !this.customerQuery.isLoading)
            .then(() => {
                this.assignControlsToAuditRequestMutation
                    .mutateAsync({
                        path: {
                            customerRequestId: this.requestId as number,
                        },
                        body: {
                            controlIds,
                        },
                    })
                    .then(() => {
                        this.customerRequestEvidenceQuery.invalidate();
                        sharedAuditHubControlsController.auditCustomerRequestControlsQuery.invalidate();
                    })
                    .catch(() => {
                        snackbarController.addSnackbar({
                            id: 'update-request-controls-error',
                            props: {
                                title: t`Update Failed`,
                                description: t`An error occurred while updating the request controls. Try again later.`,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                    });
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: 'update-request-controls-error',
                    props: {
                        title: t`Update Failed`,
                        description: t`An error occurred while updating the request controls. Try again later.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };

    downloadAllControls = () => {
        this.generateRequestControlEvidencePackage({});
    };
}

export const sharedCustomerRequestDetailsController =
    new CustomerRequestDetailsController();
