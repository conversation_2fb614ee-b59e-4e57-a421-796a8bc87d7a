import { isEmpty, isNil } from 'lodash-es';
import { sharedRequirementDetailsController } from '@controllers/requirements';
import { grcControllerUpdateRequirementParameterMutation } from '@globals/api-sdk/queries';
import type {
    ParameterValuesRequestDto,
    ParameterValuesResponseDto,
    ParamsResponseDto,
    RequirementDetailResponseDto,
    RequirementPartDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';

export class OscalRequirementsController {
    constructor() {
        makeAutoObservable(this);
    }

    updateOscalRequirementParamValues = new ObservedMutation(
        grcControllerUpdateRequirementParameterMutation,
    );

    get isUpdating(): boolean {
        return (
            this.updateOscalRequirementParamValues.isPending ||
            sharedRequirementDetailsController.isRequirementLoading ||
            sharedRequirementDetailsController.isRequirementFetching
        );
    }

    get hasError(): boolean {
        return this.updateOscalRequirementParamValues.hasError;
    }

    get error(): Error | null {
        return this.updateOscalRequirementParamValues.error;
    }

    updateParamValues = (values: ParameterValuesRequestDto[]): void => {
        if (!this.requirement?.profileRequirementId) {
            return;
        }

        this.updateOscalRequirementParamValues.mutate({
            path: {
                profileRequirementId: this.requirement.profileRequirementId,
            },
            body: {
                values,
            },
        });

        when(
            () => !this.updateOscalRequirementParamValues.isPending,
            () => {
                if (isNil(this.requirement)) {
                    return;
                }

                sharedRequirementDetailsController.loadRequirementById(
                    this.requirement.id,
                );
            },
        );
    };

    get requirement(): RequirementDetailResponseDto | null {
        return sharedRequirementDetailsController.requirement;
    }

    get parts(): RequirementPartDto[] {
        return this.requirement?.parts ?? [];
    }

    get params(): ParamsResponseDto[] {
        return this.requirement?.params ?? [];
    }

    get values(): ParameterValuesResponseDto[] {
        return this.requirement?.values ?? [];
    }

    get oscalProfile(): number | undefined {
        return this.requirement?.profileRequirementId;
    }

    /**
     * # isRequirementReadOnly
     *
     * Asserts whether all parameters being referenced on the parts
     * do not require user input. If that is so, there is nothing to
     * edit in the requirement and we hide the edit button.
     */
    get isRequirementReadOnly(): boolean {
        if (isEmpty(this.parts) || isEmpty(this.params)) {
            return true;
        }

        const partParamIds = this.parts.flatMap((part) =>
            this.getParameterReferenceIds(part),
        );

        const uniquePartParamIds = [...new Set(partParamIds)];

        const paramsReferencedInParts = this.params.filter((param) =>
            uniquePartParamIds.includes(param.id),
        );

        const paramsThatNeedInput = paramsReferencedInParts.filter(
            this.doesParamRequireIntervention,
        );

        return isEmpty(paramsThatNeedInput);
    }

    /**
     * # getParameterReferenceIds
     *
     * Recursively extract the param ids from all the nested prose properties.
     *
     * This method and its dependencies are direct copies of the helpers in
     * `@cosmos-lab/components/oscal-requirement-details/src/lib/helpers`.
     */
    getParameterReferenceIds(part: RequirementPartDto): string[] {
        const paramRefs = this.getParameterReferences(part.prose);

        const ids = paramRefs.map((paramRef) => this.getParamId(paramRef));

        const childrenIds = (part.parts ?? []).flatMap((_part) =>
            this.getParameterReferenceIds(_part),
        );

        return [...ids, ...childrenIds];
    }

    getParameterReferences(prose = ''): string[] {
        const parameterRefRegEx = /\{\{[^{]+\}\}/g;

        return prose.match(parameterRefRegEx) ?? [];
    }

    getParamId(placeholder: string): string {
        const [, param] = placeholder.match(this.parameterIdRegEx) ?? [
            null,
            '',
        ];

        return param;
    }

    get parameterIdRegEx(): RegExp {
        return /\{\{\sinsert:\sparam,\s(.+)\s\}\}/;
    }

    doesParamRequireIntervention = (param: ParamsResponseDto): boolean => {
        return !param.constraints?.some((constraint) =>
            Boolean(constraint.description),
        );
    };
}

export const sharedOscalRequirementsController =
    new OscalRequirementsController();
