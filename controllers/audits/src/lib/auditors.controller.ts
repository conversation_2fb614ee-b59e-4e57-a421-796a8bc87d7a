import { modalController } from '@controllers/modal';
import { snackbarController } from '@controllers/snackbar';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import {
    auditHubControllerPostAuditAuditorMutation,
    usersControllerGetAuditorFirmsOptions,
    usersControllerGetAuditorsOptions,
} from '@globals/api-sdk/queries';
import type { AuditorWithAuditsResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';

// Types for auditor creation
interface AuditorFormData {
    firstName: string;
    lastName: string;
    email: string;
    firm?: string;
    grantAccess?: boolean;
    allowDownloads?: boolean;
}

interface AuditorApiRequestBody {
    firstName: string;
    lastName: string;
    email: string;
    firmName: string;
    readOnly: boolean;
    allowDownloads: boolean;
    appAccess: 'NONE' | 'READ_ONLY' | 'READ_ONLY_DOWNLOADS';
}

// Constants for default values
const AUDITOR_DEFAULTS = {
    FIRM_NAME: '',
    READ_ONLY: false,
    ALLOW_DOWNLOADS: false,
} as const;

class AuditorsController {
    constructor() {
        makeAutoObservable(this);
    }

    auditorsListQuery = new ObservedQuery(usersControllerGetAuditorsOptions);

    auditorFirmsQuery = new ObservedQuery(
        usersControllerGetAuditorFirmsOptions,
    );

    createAuditor = new ObservedMutation(
        auditHubControllerPostAuditAuditorMutation,
        {
            onSuccess: () => {
                this.auditorsListQuery.invalidate();
            },
        },
    );

    loadPage = (params: FetchDataResponseParams): void => {
        const { pagination, globalFilter } = params;
        const { pageSize, page = 1 } = pagination;
        const { filters: { appAccess, firmName } = {}, search } = globalFilter;

        type Query = Required<
            Parameters<typeof usersControllerGetAuditorsOptions>
        >[0]['query'];

        const query: Query = {
            page,
            limit: pageSize,
            appAccess: appAccess.value as
                | 'NONE'
                | 'READ_ONLY'
                | 'READ_ONLY_DOWNLOADS',
            q: search ?? undefined,
            firmName: this.#extractComboboxValue(firmName),
        };

        this.auditorsListQuery.load({ query });
    };

    searchFirmOptions = ({ search }: { search?: string }): void => {
        try {
            const query = search ? { q: search } : {};

            this.auditorFirmsQuery.load({ query });
        } catch {
            snackbarController.addSnackbar({
                id: 'search-firm-options-error',
                props: {
                    title: t`Failed to search firm options`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });
        }
    };

    get searchableFirmOptions(): ListBoxItemData[] {
        const firms = this.auditorFirmsQuery.data ?? [];

        return firms.map((firm: string) => ({
            id: firm,
            label: firm,
            value: firm,
        }));
    }

    get auditorFirms(): string[] {
        return this.auditorFirmsQuery.data ?? [];
    }

    get auditors(): AuditorWithAuditsResponseDto[] {
        return this.auditorsListQuery.data?.data ?? [];
    }

    get isLoading(): boolean {
        return this.auditorsListQuery.isLoading;
    }

    get total(): number {
        return this.auditorsListQuery.data?.total ?? 0;
    }

    #extractComboboxValue = (
        filter: { value?: unknown } | undefined,
    ): string | undefined => {
        if (!filter?.value) {
            return undefined;
        }
        const comboboxValue = filter.value as ListBoxItemData | undefined;

        return comboboxValue?.value;
    };

    get createAuditorIsLoading(): boolean {
        return this.createAuditor.isPending;
    }

    get hasCreateAuditorError(): boolean {
        return this.createAuditor.hasError;
    }

    createAuditorFromFormValues = (
        formValues: Record<string, unknown>,
        onSuccess?: () => void,
    ): void => {
        const auditorData = this.transformFormValuesToAuditorData(formValues);

        this.createAuditorWithData(auditorData, onSuccess);
    };

    private transformFormValuesToAuditorData(
        formValues: Record<string, unknown>,
    ): AuditorFormData {
        const {
            firstName,
            lastName,
            email,
            firm,
            grantAccess,
            allowDownloads,
        } = formValues;

        return {
            firstName: firstName as string,
            lastName: lastName as string,
            email: email as string,
            firm: firm as string,
            grantAccess: grantAccess as boolean,
            allowDownloads: allowDownloads as boolean,
        };
    }

    createAuditorWithData = (
        auditorData: AuditorFormData,
        onSuccess?: () => void,
    ): void => {
        const requestBody = this.buildAuditorRequestBody(auditorData);

        this.createAuditor.mutate({ body: requestBody });

        when(
            () => !this.createAuditorIsLoading,
            () => {
                if (this.hasCreateAuditorError) {
                    snackbarController.addSnackbar({
                        id: 'auditor-create-error',
                        props: {
                            title: t`Failed to create auditor`,
                            description: t`An error occurred while creating the auditor. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: 'auditor-created-success',
                    props: {
                        title: t`Auditor created successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                onSuccess?.();
            },
        );
    };

    /**
     * Builds the API request body from auditor data.
     */
    private buildAuditorRequestBody(
        auditorData: AuditorFormData,
    ): AuditorApiRequestBody {
        const appAccess = this.determineAppAccess(auditorData);

        return {
            firstName: auditorData.firstName,
            lastName: auditorData.lastName,
            email: auditorData.email,
            firmName: auditorData.firm ?? AUDITOR_DEFAULTS.FIRM_NAME,
            readOnly: auditorData.grantAccess ?? AUDITOR_DEFAULTS.READ_ONLY,
            allowDownloads:
                auditorData.allowDownloads ?? AUDITOR_DEFAULTS.ALLOW_DOWNLOADS,
            appAccess,
        };
    }

    private determineAppAccess(
        auditorData: Pick<AuditorFormData, 'grantAccess' | 'allowDownloads'>,
    ): AuditorApiRequestBody['appAccess'] {
        if (!auditorData.grantAccess) {
            return 'NONE';
        }

        return auditorData.allowDownloads ? 'READ_ONLY_DOWNLOADS' : 'READ_ONLY';
    }

    /**
     * Handles form submission from the create auditor modal.
     */
    handleCreateAuditorFormSubmission = (
        formValues: Record<string, unknown>,
    ): void => {
        this.createAuditorFromFormValues(formValues, () => {
            modalController.closeModal('create-auditor-modal');
        });
    };
}

export const sharedAuditorsController = new AuditorsController();
