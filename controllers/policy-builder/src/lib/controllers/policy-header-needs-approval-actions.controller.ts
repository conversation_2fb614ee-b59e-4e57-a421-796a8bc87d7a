import { isEmpty } from 'lodash-es';
import { modalController } from '@controllers/modal';
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import { snackbarController } from '@controllers/snackbar';
import type { Action } from '@cosmos/components/action-stack';
import type { SchemaDropdownItemData } from '@cosmos/components/schema-dropdown';
import {
    policyApprovalsControllerOverrideApprovalMutation,
    policyApprovalsControllerPostApprovalReviewMutation,
    policyVersionControllerPutPolicyVersionStatusMutation,
} from '@globals/api-sdk/queries';
import { sharedCurrentUserController } from '@globals/current-user';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation } from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import { openPolicyOverrideApprovalModal } from '../helpers/open-policy-override-approval-modal.helper';
import { openPolicyRequestChangesModal } from '../helpers/open-policy-request-changes-modal.helper';
import { sharedPolicyHeaderSharedActionsController } from './policy-header-shared-actions.controller';

export class PolicyHeaderNeedsApprovalActions {
    approveMutation = new ObservedMutation(
        policyVersionControllerPutPolicyVersionStatusMutation,
        {
            onSuccess: () => {
                snackbarController.addSnackbar({
                    id: 'policy-action-result',
                    props: {
                        title: t`Policy approved successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                sharedPolicyBuilderController.invalidatePolicyQueries();
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'policy-action-result',
                    props: {
                        title: t`Failed to approve policy`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    requestChangesMutation = new ObservedMutation(
        policyApprovalsControllerPostApprovalReviewMutation,
        {
            onSuccess: () => {
                snackbarController.addSnackbar({
                    id: 'policy-action-result',
                    props: {
                        title: t`Changes requested successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                modalController.closeModal('policy-request-changes-modal');

                sharedPolicyBuilderController.invalidatePolicyQueries();
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'policy-action-result',
                    props: {
                        title: t`Failed to request changes`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    cancelApprovalMutation = new ObservedMutation(
        policyVersionControllerPutPolicyVersionStatusMutation,
        {
            onSuccess: () => {
                snackbarController.addSnackbar({
                    id: 'policy-action-result',
                    props: {
                        title: t`Approval cancelled successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                closeConfirmationModal();

                sharedPolicyBuilderController.invalidatePolicyQueries();
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'policy-action-result',
                    props: {
                        title: t`Failed to cancel approval`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    overrideApprovalMutation = new ObservedMutation(
        policyApprovalsControllerOverrideApprovalMutation,
        {
            onSuccess: () => {
                snackbarController.addSnackbar({
                    id: 'policy-action-result',
                    props: {
                        title: t`Approval overridden successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                modalController.closeModal('policy-override-approval-modal');

                sharedPolicyBuilderController.invalidatePolicyQueries();
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'policy-action-result',
                    props: {
                        title: t`Failed to override approval`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    constructor() {
        makeAutoObservable(this);
    }

    getActions(): Action[] {
        const actions: Action[] = [];

        if (this.hasChangesRequested) {
            if (
                sharedPolicyHeaderSharedActionsController.shouldShowPdfPreview
            ) {
                actions.push({
                    id: 'pdf-preview-button',
                    actionType: 'button',
                    typeProps: {
                        label: t`PDF preview`,
                        level: 'secondary',
                        colorScheme: 'primary',
                        onClick:
                            sharedPolicyHeaderSharedActionsController.handlePdfPreview,
                    },
                });
            }
            const changesRequestedDropdownActions = [];

            if (this.shouldShowOverrideApproval) {
                changesRequestedDropdownActions.push({
                    id: 'override-approval-action',
                    type: 'item',
                    label: t`Override policy approval`,
                    value: 'override-policy-approval',
                    onSelect: this.handleOverrideApproval,
                });
            }

            if (!isEmpty(changesRequestedDropdownActions)) {
                actions.push({
                    id: 'changes-requested-dropdown-actions',
                    actionType: 'dropdown',
                    typeProps: {
                        label: '',
                        level: 'tertiary',
                        colorScheme: 'primary',
                        startIconName: 'HorizontalMenu',
                        items: changesRequestedDropdownActions,
                    },
                });
            }

            return actions;
        }
        if (this.shouldShowApproveButton) {
            actions.push(this.approveAction);
        }

        if (this.shouldShowRequestChangesButton) {
            actions.push(this.requestChangesAction);
        }

        const dropdownActions = this.getDropdownActions();

        if (!isEmpty(dropdownActions)) {
            actions.push({
                id: 'needs-approval-dropdown-actions',
                actionType: 'dropdown',
                typeProps: {
                    label: '',
                    level: 'tertiary',
                    colorScheme: 'primary',
                    startIconName: 'HorizontalMenu',
                    items: dropdownActions,
                },
            });
        }

        return actions;
    }

    get approveAction(): Action {
        return {
            id: 'approve-button',
            actionType: 'button',
            typeProps: {
                label: t`Approve`,
                level: 'primary',
                colorScheme: 'primary',
                onClick: this.handleApprove,
                cosmosUseWithCaution_isDisabled: this.approveMutation.isPending,
                isLoading: this.approveMutation.isPending,
            },
        };
    }

    get requestChangesAction(): Action {
        return {
            id: 'request-changes-button',
            actionType: 'button',
            typeProps: {
                label: t`Request changes`,
                level: 'secondary',
                colorScheme: 'primary',
                onClick: this.handleRequestChanges,
            },
        };
    }

    getDropdownActions(): SchemaDropdownItemData[] {
        const actions = [];

        if (this.shouldShowCancelApproval) {
            actions.push({
                id: 'cancel-approval-action',
                type: 'item',
                label: t`Cancel approval`,
                value: 'cancel-approval',
                onSelect: this.handleCancelApproval,
            });
        }

        if (this.shouldShowOverrideApproval) {
            actions.push({
                id: 'override-approval-action',
                type: 'item',
                label: t`Override policy approval`,
                value: 'override-policy-approval',
                onSelect: this.handleOverrideApproval,
            });
        }

        if (this.shouldShowPdfPreviewDropdown) {
            actions.push(
                sharedPolicyHeaderSharedActionsController.pdfPreviewAction,
            );
        }

        return actions;
    }

    get isApprover(): boolean {
        const { currentVersion } = sharedPolicyBuilderController;
        const currentUserId = sharedCurrentUserController.entryId;

        if (!currentUserId) {
            return false;
        }

        if (currentVersion?.approver?.entryId === currentUserId) {
            return true;
        }

        return sharedPolicyBuilderModel.isCurrentUserApprover;
    }

    get isPolicyVersionOwner(): boolean {
        const { currentVersion } = sharedPolicyBuilderController;

        if (!currentVersion?.owner) {
            return false;
        }

        const currentUserId = sharedCurrentUserController.entryId;

        return currentVersion.owner.entryId === currentUserId;
    }

    get consensusReached(): boolean {
        const { currentVersion } = sharedPolicyBuilderController;

        return currentVersion?.policyVersionStatus === 'APPROVED';
    }

    get hasChangesRequested(): boolean {
        return sharedPolicyBuilderModel.hasChangesRequested;
    }

    get isApproverReadyForReview(): boolean {
        if (!this.isApprover) {
            return false;
        }

        const { currentVersion } = sharedPolicyBuilderController;

        return currentVersion?.policyVersionStatus === 'NEEDS_APPROVAL';
    }

    get canOverridePolicy(): boolean {
        return (
            sharedFeatureAccessModel.hasPolicyManagePermission ||
            this.isPolicyVersionOwner
        );
    }

    get shouldShowApproveButton(): boolean {
        return (
            this.isApprover &&
            this.isApproverReadyForReview &&
            !this.consensusReached
        );
    }

    get shouldShowRequestChangesButton(): boolean {
        return (
            !this.consensusReached &&
            this.isApprover &&
            this.isApproverReadyForReview
        );
    }

    get shouldShowPdfPreviewDropdown(): boolean {
        return sharedPolicyHeaderSharedActionsController.shouldShowPdfPreview;
    }

    get shouldShowCancelApproval(): boolean {
        return this.isApprovalPending && this.isPolicyVersionOwner;
    }

    get shouldShowOverrideApproval(): boolean {
        return this.canOverridePolicy && this.isApprovalPending;
    }

    private get isApprovalPending(): boolean {
        return !this.consensusReached;
    }

    handleApprove = (): void => {
        if (this.approveMutation.isPending) {
            return;
        }

        const { policyId, currentVersionId, currentUserReviewId } =
            sharedPolicyBuilderModel;

        if (!policyId || !currentVersionId || !currentUserReviewId) {
            snackbarController.addSnackbar({
                id: 'approve-policy-error',
                props: {
                    title: t`Unable to approve policy: Missing required data`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        this.approveMutation.mutate({
            path: {
                policyId,
                policyVersionId: currentVersionId,
            },
            body: {
                policyVersionStatus: 'APPROVED',
                reviewId: currentUserReviewId,
            },
        });
    };

    handleRequestChanges = (): void => {
        openPolicyRequestChangesModal();
    };

    handleCancelApproval = (): void => {
        openConfirmationModal({
            title: t`Cancel approval process?`,
            body: t`Are you sure you want to cancel the approval process? This policy will remain in a "Needs Approval" state and all approval progress will be reset. A cancellation email will be sent to all approvers.`,
            confirmText: t`Cancel approval`,
            cancelText: t`Keep in approval`,
            type: 'danger',
            size: 'md',
            disableClickOutsideToClose: true,
            onConfirm: this.confirmCancelApproval,
            onCancel: closeConfirmationModal,
            isLoading: () => this.cancelApprovalMutation.isPending,
        });
    };

    confirmCancelApproval = (): void => {
        const { policyId, currentVersionId } = sharedPolicyBuilderModel;

        if (!policyId || !currentVersionId) {
            snackbarController.addSnackbar({
                id: 'policy-error',
                props: {
                    title: t`Unable to cancel approval: Missing policy or version ID`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        this.cancelApprovalMutation.mutate({
            path: {
                policyId,
                policyVersionId: currentVersionId,
            },
            body: {
                policyVersionStatus: 'DRAFT',
            },
        });
    };

    handleOverrideApproval = (): void => {
        openPolicyOverrideApprovalModal();
    };

    handleRequestChangesSubmit = (reason: string): void => {
        if (this.requestChangesMutation.isPending) {
            return;
        }

        const { policyId, currentVersionId, latestApprovalId } =
            sharedPolicyBuilderModel;

        if (!policyId || !currentVersionId || !latestApprovalId) {
            snackbarController.addSnackbar({
                id: 'policy-error',
                props: {
                    title: t`Unable to request changes: Missing required data`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        this.requestChangesMutation.mutate({
            path: {
                policyId,
                versionId: currentVersionId,
                approvalId: latestApprovalId,
            },
            body: {
                status: 'CHANGES_REQUESTED',
                changeRequest: reason,
            },
        });
    };

    handleOverrideApprovalSubmit = (overrideReason: string): void => {
        if (this.overrideApprovalMutation.isPending) {
            return;
        }

        const { policyId, latestApprovalId } = sharedPolicyBuilderModel;

        if (!policyId) {
            snackbarController.addSnackbar({
                id: 'policy-error',
                props: {
                    title: t`Unable to override approval: Missing policy ID`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        const approvalId = latestApprovalId;

        if (!approvalId) {
            snackbarController.addSnackbar({
                id: 'policy-error',
                props: {
                    title: t`Unable to override approval: No approval found`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        this.overrideApprovalMutation.mutate({
            path: { policyId, approvalId },
            body: {
                overrideReason,
            },
        });
    };
}

export const sharedPolicyHeaderNeedsApprovalActions =
    new PolicyHeaderNeedsApprovalActions();
