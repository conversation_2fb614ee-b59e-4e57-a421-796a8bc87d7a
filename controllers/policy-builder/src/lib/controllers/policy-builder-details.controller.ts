import { snackbarController } from '@controllers/snackbar';
import { policiesControllerUpdatePolicyMutation } from '@globals/api-sdk/queries';
import type { UpdatePolicyDetailsRequestDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import { sharedPolicyBuilderController } from '../policy-builder.controller';

class PolicyBuilderDetailsController {
    updatePolicyDetailsMutation = new ObservedMutation(
        policiesControllerUpdatePolicyMutation,
    );

    constructor() {
        makeAutoObservable(this);
    }

    get isUpdatingPolicy(): boolean {
        return this.updatePolicyDetailsMutation.isPending;
    }

    get workspaceId(): number | null {
        return sharedWorkspacesController.currentWorkspace?.id ?? null;
    }

    /**
     * Save policy details with the update policy endpoint.
     * Follows the same pattern as create-policy.controller.ts.
     */
    savePolicyDetails = (formData: {
        description: string;
        renewalDate: {
            renewalFrequency: { value: string };
            renewalDate: string;
        };
        assignedTo: 'ALL' | 'GROUP' | 'NONE';
        notifyNewMembers?: boolean;
        selectedGroups?: { value: string }[];
    }): void => {
        const { policyId, currentVersionId: versionId } =
            sharedPolicyBuilderModel;

        const { workspaceId } = this;

        if (!workspaceId || !policyId || !versionId) {
            logger.error({
                message: 'Failed to update policy. Missing required IDs',
                additionalInfo: {
                    workspaceId,
                    policyId,
                    versionId,
                },
            });
            snackbarController.addSnackbar({
                id: 'update-policy-error',
                props: {
                    title: t`Error updating policy`,
                    description: t`Failed to update policy. Please try again.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        // Format data for API - include all current policy data
        const { policy, currentVersion } = sharedPolicyBuilderController;

        // Validate we have required policy data
        if (!policy || !currentVersion) {
            logger.error({
                message: 'Failed to update policy. Policy data is missing',
                additionalInfo: { policyId },
            });
            snackbarController.addSnackbar({
                id: 'update-policy-error',
                props: {
                    title: t`Error updating policy`,
                    description: t`Policy data is missing. Please refresh and try again.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        // Handle group assignment
        const groupIds: number[] = [];
        let notifyGroups = false;

        if (formData.assignedTo === 'GROUP' && formData.selectedGroups) {
            groupIds.push(
                ...formData.selectedGroups.map((g) => Number(g.value)),
            );
            notifyGroups = Boolean(formData.notifyNewMembers);
        }

        // Get current control IDs from the policy
        const { policyControlsAssociated } = sharedPolicyBuilderController;
        const controlIds = policyControlsAssociated.map(
            (control) => control.id,
        );

        const requestData: UpdatePolicyDetailsRequestDto = {
            // Required fields
            name: policy.name || '', // Name comes from policy, not editable in this form
            description: formData.description.trim(),
            assignedTo: formData.assignedTo,
            groupIds,
            // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition -- Legacy Allowance. DO NOT COPY
            userId: policy.currentOwner?.id,
            material: false, // Not a material change for these edit fields
            renewalDate:
                formData.renewalDate.renewalDate || currentVersion.renewalDate,
            controlIds,
            newPolicySlaRequest: {
                weekTimeFrameSLAs: undefined,
                gracePeriodSLAs: undefined,
                p3MatrixSLAs: undefined,
            },

            // Optional fields
            notifyGroups,
            disclaimer: policy.disclaimer,
            weekTimeFrameSLARequests: currentVersion.weekTimeFrameSLAs?.map(
                (sla) => ({
                    policyWeekTimeFrameSLAId: sla.policyWeekTimeFrameSLAId,
                    timeFrame: sla.timeFrame,
                }),
            ),
            gracePeriodSLARequests: currentVersion.gracePeriodSLAs?.map(
                (sla) => ({
                    policyGracePeriodSLAId: sla.policyGracePeriodSLAId,
                    gracePeriod: sla.gracePeriod,
                }),
            ),
            p3MatrixPolicySLARequests: currentVersion.p3MatrixSLAs?.map(
                (sla) => ({
                    policyP3MatrixSLAId: sla.policyP3MatrixSLAId,
                    matrixItems: [
                        {
                            definition: sla.definition,
                            severity: sla.severity,
                            timeFrame: sla.timeFrame,
                            examples: sla.examples,
                        },
                    ],
                }),
            ),
            replacedPoliciesIds: policy.replacedPolicies.map(
                (p) => p.templateId,
            ),
            changesExplanation: currentVersion.changesExplanation || '',
        };

        const { updatePolicyDetailsMutation } = this;

        updatePolicyDetailsMutation.mutate({
            path: {
                id: policyId,
                versionId,
                xProductId: workspaceId,
            },
            body: requestData,
        });

        when(
            () => !updatePolicyDetailsMutation.isPending,
            () => {
                if (updatePolicyDetailsMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'update-policy-error',
                        props: {
                            title: t`Error updating policy`,
                            description: t`Failed to update policy. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }
                // Refresh policy data
                sharedPolicyBuilderController.invalidatePolicyQueries();

                snackbarController.addSnackbar({
                    id: 'update-policy-success',
                    props: {
                        title: t`Policy updated successfully BK`,
                        description: t`Your policy details have been updated.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };
}

export const sharedPolicyBuilderDetailsController =
    new PolicyBuilderDetailsController();
