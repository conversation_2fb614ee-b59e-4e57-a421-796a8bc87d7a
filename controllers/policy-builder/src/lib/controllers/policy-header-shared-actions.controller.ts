import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import { snackbarController } from '@controllers/snackbar';
import type { SchemaDropdownItemData } from '@cosmos/components/schema-dropdown';
import {
    policyVersionControllerCreatePolicyVersionPdfFromHtmlMutation,
    policyVersionControllerPutPolicyVersionExternalFileMutation,
    policyVersionControllerPutPolicyVersionFileMutation,
    policyVersionControllerUpdatePolicyVersionToAuthoredMutation,
} from '@globals/api-sdk/queries';
import type { PolicyVersionControllerPutPolicyVersionFileData } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { downloadBlob } from '@helpers/download-file';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import {
    closePolicyUploadModal,
    openPolicyUploadModal,
} from '../helpers/policy-upload-modal.helper';
import { openSelectExternalPolicyModal } from '../helpers/select-external-policy-modal.helper';

export class PolicyHeaderSharedActions {
    constructor() {
        makeAutoObservable(this);
    }

    authorPolicyMutation = new ObservedMutation(
        policyVersionControllerUpdatePolicyVersionToAuthoredMutation,
        {
            onSuccess: () => {
                sharedPolicyBuilderController.invalidatePolicyQueries();
            },
        },
    );

    pdfPreviewMutation = new ObservedMutation(
        policyVersionControllerCreatePolicyVersionPdfFromHtmlMutation,
        {
            onSuccess: (data) => {
                const blob = data as Blob;
                const fileName = `${sharedPolicyBuilderModel.policyName}.pdf`;

                downloadBlob(blob, fileName);
            },
        },
    );

    uploadFileMutation = new ObservedMutation(
        policyVersionControllerPutPolicyVersionFileMutation,
        {
            onSuccess: () => {
                sharedPolicyBuilderController.invalidatePolicyQueries();

                closePolicyUploadModal();

                snackbarController.addSnackbar({
                    id: 'upload-file-success',
                    props: {
                        title: t`File uploaded successfully`,
                        description: t`The policy file has been uploaded.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
            onError: (error) => {
                snackbarController.addSnackbar({
                    id: 'upload-file-error',
                    props: {
                        title: t`Upload failed`,
                        description:
                            error.message ||
                            t`An error occurred while uploading the file. Please try again or contact support.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    importExternalFileMutation = new ObservedMutation(
        policyVersionControllerPutPolicyVersionExternalFileMutation,
        {
            onSuccess: () => {
                snackbarController.addSnackbar({
                    id: 'import-file-success',
                    props: {
                        title: t`External file imported successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                sharedPolicyBuilderController.invalidatePolicyQueries();
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'import-file-error',
                    props: {
                        title: t`Failed to import external file`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    get isAuthoringPolicy(): boolean {
        return this.authorPolicyMutation.isPending;
    }

    get hasAuthorPolicyError(): boolean {
        return this.authorPolicyMutation.hasError;
    }

    get isGeneratingPdfDownload(): boolean {
        return this.pdfPreviewMutation.isPending;
    }

    get hasPdfDownloadError(): boolean {
        return this.pdfPreviewMutation.hasError;
    }

    get isUploadingFile(): boolean {
        return this.uploadFileMutation.isPending;
    }

    get isImportingFile(): boolean {
        return this.importExternalFileMutation.isPending;
    }

    get authorPolicyAction(): SchemaDropdownItemData {
        return {
            id: 'author-policy-action',
            type: 'item',
            label: t`Author policy`,
            value: 'author-policy',
            onSelect: this.handleAuthorPolicy,
            disabled: this.isAuthoringPolicy,
        };
    }

    get pdfPreviewAction(): SchemaDropdownItemData {
        return {
            id: 'pdf-preview-action',
            type: 'item',
            label: t`PDF preview`,
            value: 'pdf-preview',
            onSelect: this.handlePdfPreview,
            disabled: this.isGeneratingPdfDownload,
        };
    }

    get uploadFileAction(): SchemaDropdownItemData {
        return {
            id: 'upload-file-action',
            type: 'item',
            label: t`Upload file`,
            value: 'upload-file',
            onSelect: openPolicyUploadModal,
            disabled: this.isUploadingFile,
        };
    }

    get importFileAction(): SchemaDropdownItemData {
        return {
            id: 'import-file-action',
            type: 'item',
            label: t`Import file`,
            value: 'import-file',
            onSelect: this.handleImportFile,
            disabled: this.isImportingFile,
        };
    }

    handleAuthorPolicy = (): void => {
        const { policyId } = sharedPolicyBuilderModel;

        this.authorPolicyMutation.mutate({
            path: { id: policyId },
        });

        when(
            () => !this.isAuthoringPolicy,
            () => {
                if (this.hasAuthorPolicyError) {
                    snackbarController.addSnackbar({
                        id: 'author-policy-error',
                        props: {
                            title: t`Failed to author policy`,
                            description: t`An error occurred while converting the policy to authored. Please try again or contact support.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: 'author-policy-success',
                    hasTimeout: true,
                    props: {
                        title: t`Policy converted to authored successfully`,
                        description: t`The policy is now available for editing in the policy builder`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    handlePdfPreview = (): void => {
        const { policyId, htmlContent, hasHtmlContent } =
            sharedPolicyBuilderModel;

        if (!hasHtmlContent) {
            snackbarController.addSnackbar({
                id: 'pdf-download-empty-content',
                props: {
                    title: t`Empty content`,
                    description: t`Policy content is empty and cannot be downloaded as PDF.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        this.pdfPreviewMutation.mutate({
            path: { policyId },
            body: { html: htmlContent },
        });

        when(
            () => !this.isGeneratingPdfDownload,
            () => {
                if (this.hasPdfDownloadError) {
                    snackbarController.addSnackbar({
                        id: 'pdf-download-error',
                        props: {
                            title: t`Failed to download PDF`,
                            description: t`An error occurred while generating the PDF download. Please try again or contact support.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }
            },
        );
    };

    handleFileUpload = (file: File[]): void => {
        const { policyId, currentVersionId } = sharedPolicyBuilderModel;

        if (!currentVersionId) {
            snackbarController.addSnackbar({
                id: 'upload-file-no-version',
                props: {
                    title: t`Upload failed`,
                    description: t`No policy version found. Please try again.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        this.uploadFileMutation.mutate({
            path: {
                policyId,
                policyVersionId: currentVersionId,
            },
            body: {
                file,
            } satisfies PolicyVersionControllerPutPolicyVersionFileData['body'],
        });
    };

    handleImportFile = (): void => {
        openSelectExternalPolicyModal({
            onConfirm: (externalFileId) => {
                const { policyId, currentVersionId } = sharedPolicyBuilderModel;

                if (!policyId || !currentVersionId || !externalFileId) {
                    return;
                }

                this.importExternalFileMutation.mutate({
                    path: {
                        policyId,
                        policyVersionId: currentVersionId,
                    },
                    body: {
                        externalFileId,
                        needsPersonnelAcknowledgement: false,
                    },
                });
            },
        });
    };

    get shouldShowPdfPreview(): boolean {
        return (
            sharedPolicyBuilderModel.hasHtmlContent &&
            sharedPolicyBuilderModel.isAuthoredPolicy
        );
    }
}

export const sharedPolicyHeaderSharedActionsController =
    new PolicyHeaderSharedActions();
