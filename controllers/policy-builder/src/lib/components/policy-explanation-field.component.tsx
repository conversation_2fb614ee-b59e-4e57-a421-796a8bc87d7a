import type * as React from 'react';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { type CustomFieldRenderProps, UniversalFormField } from '@ui/forms';

export const PolicyExplanationField = ({
    'data-id': dataId,
    name,
    formId,
}: CustomFieldRenderProps): React.JSX.Element => {
    return (
        <Stack
            direction="column"
            gap="2x"
            data-id={dataId}
            data-testid="PolicyExplanationField"
        >
            <UniversalFormField
                __fromCustomRender
                data-id={`${dataId}-textarea`}
                formId={formId}
                name={name}
            />

            <Text
                allowBold
                type="body"
                size="100"
                colorScheme="faded"
                data-id={`${dataId}-help-text`}
            >
                <strong>{t`NOTE: The explanation of changes will appear in the email sent to your personnel when you publish the policy and choose to notify your personnel as well as the notification shown to the approver and the version history.`}</strong>
            </Text>
        </Stack>
    );
};
