import { isString } from 'lodash-es';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import {
    Form,
    type FormSchema,
    type FormValues,
    useFormSubmit,
} from '@ui/forms';
import { sharedPolicyHeaderNeedsApprovalActions } from '../controllers/policy-header-needs-approval-actions.controller';
import { closePolicyOverrideApprovalModal } from '../helpers/open-policy-override-approval-modal.helper';

const buildFormSchema = (): FormSchema => ({
    justification: {
        type: 'textarea',
        label: t`Justification for override`,
        rows: 4,
    },
});

export const PolicyOverrideApprovalModal = observer(() => {
    const { formRef, triggerSubmit } = useFormSubmit();

    const handleSubmit = action((values: FormValues) => {
        const justification = isString(values.justification)
            ? values.justification
            : '';

        sharedPolicyHeaderNeedsApprovalActions.handleOverrideApprovalSubmit(
            justification,
        );
    });

    const handleOverride = () => {
        triggerSubmit().catch(console.error);
    };

    return (
        <Modal data-id="DxM4s1vs" onClose={closePolicyOverrideApprovalModal}>
            <Modal.Header
                title={t`Override Approval`}
                description={t`By overriding the approval process, this policy will be approved without requiring consensus from all approvers. Please provide a justification.`}
                closeButtonAriaLabel={t`Close override approval modal`}
                onClose={closePolicyOverrideApprovalModal}
            />

            <Modal.Body>
                <Form
                    hasExternalSubmitButton
                    data-id="policy-override-approval-form"
                    ref={formRef}
                    formId="policy-override-approval-form"
                    schema={buildFormSchema()}
                    onSubmit={handleSubmit}
                />
            </Modal.Body>

            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Cancel`,
                        level: 'secondary',
                        onClick: closePolicyOverrideApprovalModal,
                        cosmosUseWithCaution_isDisabled:
                            sharedPolicyHeaderNeedsApprovalActions
                                .overrideApprovalMutation.isPending,
                    },
                    {
                        label: t`Override Approval`,
                        level: 'primary',
                        onClick: handleOverride,
                        cosmosUseWithCaution_isDisabled:
                            sharedPolicyHeaderNeedsApprovalActions
                                .overrideApprovalMutation.isPending,
                        isLoading:
                            sharedPolicyHeaderNeedsApprovalActions
                                .overrideApprovalMutation.isPending,
                    },
                ]}
            />
        </Modal>
    );
});
