import { isString } from 'lodash-es';
import * as React from 'react';
import { Banner } from '@cosmos/components/banner';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import {
    Form,
    type FormSchema,
    type FormValues,
    useFormSubmit,
} from '@ui/forms';
import { sharedPolicyHeaderDraftActions } from '../controllers/policy-header-draft-actions.controller';
import { closePolicyFinalizeModal } from '../helpers/policy-finalize-modal.helper';
import { PolicyExplanationField } from './policy-explanation-field.component';

const handleCancel = (): void => {
    closePolicyFinalizeModal();
};

export const PolicyFinalizeModal = observer((): React.JSX.Element => {
    const { isFinalizingDraft } = sharedPolicyHeaderDraftActions;
    const { formRef, triggerSubmit } = useFormSubmit();

    const buildFormSchema = React.useCallback((): FormSchema => {
        const { nextMajorVersion, nextMinorVersion, currentMajorVersion } =
            sharedPolicyBuilderModel;

        const schema: FormSchema = {
            isMaterialChange: {
                type: 'choiceCardGroup',
                label: t`Does this draft include material changes?`,
                choiceCardInputType: 'radio',
                cosmosUseWithCaution_forceOptionOrientation: 'vertical',
                options: [
                    {
                        value: 'true',
                        label: t`YES, it includes material changes`,
                        helpText: t`This draft becomes Version ${nextMajorVersion}`,
                    },
                    {
                        value: 'false',
                        label: t`NO, it does not include material changes`,
                        helpText: t`This draft becomes Version ${currentMajorVersion}.${nextMinorVersion}`,
                    },
                ],
                initialValue: undefined,
            },
            materialChangesBanner: {
                type: 'custom',
                label: '',
                render: () => {
                    return (
                        <Banner
                            severity="primary"
                            title={t`Material changes require approval`}
                            body={t`All material changes require approval before publishing`}
                            data-id="FxRZoJnW"
                        />
                    );
                },
                shownIf: {
                    fieldName: 'isMaterialChange',
                    operator: 'equals',
                    value: 'true',
                },
            },
            requiresApproval: {
                type: 'choiceCardGroup',
                label: t`Does this change require approval?`,

                choiceCardInputType: 'radio',
                cosmosUseWithCaution_forceOptionOrientation: 'vertical',
                options: [
                    {
                        value: 'true',
                        label: t`YES, approval is required`,
                        helpText: t`This will require the policy to be approved prior to publishing.`,
                    },
                    {
                        value: 'false',
                        label: t`NO, approval is not required`,
                        helpText: t`This will immediately publish the policy without approval.`,
                    },
                ],
                initialValue: undefined,
                shownIf: {
                    fieldName: 'isMaterialChange',
                    operator: 'equals',
                    value: 'false',
                },
            },
            requiresAcknowledgment: {
                type: 'choiceCardGroup',
                label: t`Does this change require personnel acknowledgment?`,
                choiceCardInputType: 'radio',
                cosmosUseWithCaution_forceOptionOrientation: 'vertical',
                options: [
                    {
                        value: 'true',
                        label: t`YES, personnel acknowledgement is required`,
                    },
                    {
                        value: 'false',
                        label: t`NO, personnel acknowledgement is not required`,
                    },
                ],
                initialValue: undefined,
                shownIf: {
                    fieldName: 'requiresApproval',
                    operator: 'equals',
                    value: 'false',
                },
            },
            changesExplanation: {
                type: 'custom',
                validateWithDefault: 'textarea',
                label: t`Explanation of changes`,
                placeholder: t`Any details about what has changed in the policy...`,
                isOptional: true,
                rows: 2,
                initialValue: '',
                render: PolicyExplanationField,
            },
        };

        return schema;
    }, []);

    const handleSubmit = action((values: FormValues) => {
        const data = {
            isMaterialChange: values.isMaterialChange === 'true',
            requiresApproval: values.requiresApproval === 'true',
            requiresAcknowledgment: values.requiresAcknowledgment === 'true',
            changesExplanation: isString(values.changesExplanation)
                ? values.changesExplanation
                : '',
        };

        sharedPolicyHeaderDraftActions.handleFinalizeDraftFromModal(data);
    });

    const handleSave = (): void => {
        triggerSubmit().catch(console.error);
    };

    return (
        <Modal
            data-id="policy-finalize-modal"
            data-testid="PolicyFinalizeModal"
            size="lg"
            onClose={handleCancel}
        >
            <Modal.Header
                closeButtonAriaLabel={t`Close`}
                title={t`Finalize Draft`}
                onClose={handleCancel}
            />

            <Modal.Body>
                <Form
                    hasExternalSubmitButton
                    data-id="policy-finalize-form"
                    formId="policy-finalize-form"
                    ref={formRef}
                    schema={buildFormSchema()}
                    isReadOnly={isFinalizingDraft}
                    onSubmit={handleSubmit}
                />
            </Modal.Body>

            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Cancel`,
                        level: 'tertiary',
                        onClick: handleCancel,
                        cosmosUseWithCaution_isDisabled: isFinalizingDraft,
                    },
                    {
                        label: t`Finalize`,
                        level: 'primary',
                        onClick: handleSave,
                        cosmosUseWithCaution_isDisabled: isFinalizingDraft,
                        isLoading: isFinalizingDraft,
                    },
                ]}
            />
        </Modal>
    );
});
