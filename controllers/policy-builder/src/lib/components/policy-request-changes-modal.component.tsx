import { isString } from 'lodash-es';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import {
    Form,
    type FormSchema,
    type FormValues,
    useFormSubmit,
} from '@ui/forms';
import { sharedPolicyHeaderNeedsApprovalActions } from '../controllers/policy-header-needs-approval-actions.controller';
import { closePolicyRequestChangesModal } from '../helpers/open-policy-request-changes-modal.helper';

const buildFormSchema = (): FormSchema => ({
    reason: {
        type: 'textarea',
        label: t`Reason for requesting changes`,
        rows: 4,
    },
});

export const PolicyRequestChangesModal = observer(() => {
    const { formRef, triggerSubmit } = useFormSubmit();

    const handleSubmit = action((values: FormValues) => {
        const reason = isString(values.reason) ? values.reason : '';

        sharedPolicyHeaderNeedsApprovalActions.handleRequestChangesSubmit(
            reason,
        );
    });

    const handleRequestChanges = () => {
        triggerSubmit().catch(console.error);
    };

    return (
        <Modal data-id="Dx_mX6cC" onClose={closePolicyRequestChangesModal}>
            <Modal.Header
                title={t`Request Changes`}
                description={t`Please provide feedback on what changes need to be made to this policy.`}
                closeButtonAriaLabel={t`Close request changes modal`}
                onClose={closePolicyRequestChangesModal}
            />

            <Modal.Body>
                <Form
                    hasExternalSubmitButton
                    data-id="policy-request-changes-form"
                    ref={formRef}
                    formId="policy-request-changes-form"
                    schema={buildFormSchema()}
                    onSubmit={handleSubmit}
                />
            </Modal.Body>

            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Cancel`,
                        level: 'secondary',
                        onClick: closePolicyRequestChangesModal,
                        cosmosUseWithCaution_isDisabled:
                            sharedPolicyHeaderNeedsApprovalActions
                                .requestChangesMutation.isPending,
                    },
                    {
                        label: t`Request Changes`,
                        level: 'primary',
                        onClick: handleRequestChanges,
                        cosmosUseWithCaution_isDisabled:
                            sharedPolicyHeaderNeedsApprovalActions
                                .requestChangesMutation.isPending,
                        isLoading:
                            sharedPolicyHeaderNeedsApprovalActions
                                .requestChangesMutation.isPending,
                    },
                ]}
            />
        </Modal>
    );
});
