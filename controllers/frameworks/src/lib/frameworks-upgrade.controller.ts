import { isNil } from 'lodash-es';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { snackbarController } from '@controllers/snackbar';
import { FrameworkTag } from '@drata/enums';
import { grcControllerUpgradeFrameworkMutation } from '@globals/api-sdk/queries';
import type {
    FrameworkResponseDto,
    FrameworkTagEnum,
} from '@globals/api-sdk/types';
import { sharedCurrentCompanyController } from '@globals/current-company';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    reaction,
    when,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { FrameworksController } from './frameworks-controller';

const POLL_INTERVAL_MS = 5000;

type SelfActivateFrameworkTag = Extract<FrameworkTagEnum, 'NISTCSF2' | 'PCI4'>;

export class FrameworksUpgradeController {
    constructor() {
        makeAutoObservable(this, undefined, { autoBind: true });
    }

    upgradedFramework: FrameworkResponseDto | undefined = undefined;

    upgradeFrameworkMutation = new ObservedMutation(
        grcControllerUpgradeFrameworkMutation,
    );

    get isPending(): boolean {
        return this.upgradeFrameworkMutation.isPending;
    }

    get hasError(): boolean {
        return this.upgradeFrameworkMutation.hasError;
    }

    async upgradeFramework(
        frameworkTag: SelfActivateFrameworkTag,
    ): Promise<void> {
        if (!sharedWorkspacesController.currentWorkspace) {
            return;
        }

        await this.upgradeFrameworkMutation
            .mutateAsync({
                body: {
                    upgradeTag: FrameworkTag[frameworkTag],
                },
                path: {
                    xProductId: sharedWorkspacesController.currentWorkspace.id,
                },
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: 'framework-upgrade-error',
                    props: {
                        title: t`Failed to upgrade framework`,
                        description: t`An error occurred while upgrading the framework. Please try again later.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });

        snackbarController.addSnackbar({
            id: 'framework-upgrade-success',
            hasTimeout: true,
            props: {
                title: t`Framework upgraded`,
                description: t`Framework is being activated. It might take more than a minute so please refresh the page later.`,
                severity: 'success',
                closeButtonAriaLabel: t`Close`,
            },
        });

        sharedCurrentCompanyController.companyQuery.invalidate();

        await when(
            () =>
                !sharedCurrentCompanyController.companyQuery.isLoading &&
                !sharedCurrentCompanyController.companyQuery.isFetching,
        );

        sharedWorkspacesController.updateCurrentWorkspace();
        const pathname = window.location.pathname
            .split('/')
            .slice(0, -2)
            .join('/');

        sharedProgrammaticNavigationController.navigateTo(pathname);

        reaction(
            () => !isNil(this.upgradeFrameworkMutation.response),
            (isDone) => {
                if (isDone) {
                    this.recursiveLoadUpgradedFramework(frameworkTag).catch(
                        (error) => {
                            console.error(error);

                            snackbarController.addSnackbar({
                                id: 'framework-upgrade-error',
                                props: {
                                    title: t`Failed to upgrade framework`,
                                    description: t`An error occurred while upgrading the framework. Please try again later.`,
                                    severity: 'critical',
                                    closeButtonAriaLabel: t`Close`,
                                },
                            });
                        },
                    );
                }
            },
        );
    }

    async recursiveLoadUpgradedFramework(
        frameworkTag: SelfActivateFrameworkTag,
    ): Promise<void> {
        await when(() => !isNil(sharedWorkspacesController.currentWorkspaceId));

        // This should be a given at this point, but TypeScript doesn't know that
        if (isNil(sharedWorkspacesController.currentWorkspace)) {
            throw new Error('No workspace found');
        }

        const frameworksController = new FrameworksController();

        frameworksController.allProductFrameworks.load({
            path: {
                id: sharedWorkspacesController.currentWorkspace.id,
            },
        });

        await when(
            () => !isNil(frameworksController.allProductFrameworks.data),
        );

        const upgradedFramework = frameworksController.productFrameworks.find(
            ({ tag }) => tag === frameworkTag,
        );

        if (upgradedFramework) {
            this.upgradedFramework = upgradedFramework;

            return;
        }

        await this.#sleep();

        await this.recursiveLoadUpgradedFramework(frameworkTag);
    }

    isSelfActivateFramework(
        frameworkTag: FrameworkTagEnum,
    ): frameworkTag is SelfActivateFrameworkTag {
        return frameworkTag === 'NISTCSF2' || frameworkTag === 'PCI4';
    }

    #sleep(): Promise<void> {
        return new Promise((resolve) => {
            setTimeout(resolve, POLL_INTERVAL_MS);
        });
    }
}

export const sharedFrameworksUpgradeController =
    new FrameworksUpgradeController();
