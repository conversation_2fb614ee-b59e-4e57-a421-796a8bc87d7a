import { controlApprovalsControllerListControlApprovalsOptions } from '@globals/api-sdk/queries';
import type { ApprovalsResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { queryClient } from '@globals/query-client';
import { sharedControlApprovalReviewersController } from './control-approval-reviewers.controller';

class ControlApprovalsController {
    constructor() {
        makeAutoObservable(this);
    }

    controlApprovalsQuery = new ObservedQuery(
        controlApprovalsControllerListControlApprovalsOptions,
    );

    get controlApprovals(): ApprovalsResponseDto[] {
        return this.controlApprovalsQuery.data?.data ?? [];
    }

    get currentControlApproval(): ApprovalsResponseDto | null {
        return (
            this.controlApprovalsQuery.data?.data.find(
                ({ current }) => current,
            ) ?? null
        );
    }

    get nextControlApproval(): ApprovalsResponseDto | null {
        return (
            this.controlApprovalsQuery.data?.data.find(
                ({ current }) => !current,
            ) ?? null
        );
    }

    load = (controlId: number): void => {
        this.controlApprovalsQuery.load({
            path: {
                controlId,
            },
            query: {
                current: true,
                next: true,
            },
        });

        when(
            () =>
                !this.isLoading &&
                Boolean(this.currentControlApproval) &&
                !this.hasError,
            () => {
                if (!this.currentControlApproval) {
                    return;
                }

                sharedControlApprovalReviewersController.load(
                    controlId,
                    this.currentControlApproval.id,
                );
            },
        );
    };

    get isLoading(): boolean {
        return this.controlApprovalsQuery.isLoading;
    }

    get hasError(): boolean {
        return this.controlApprovalsQuery.hasError;
    }

    get isReady(): boolean {
        return this.controlApprovalsQuery.isReady;
    }

    invalidate = () => {
        this.controlApprovalsQuery.invalidate();
    };

    get queryKey() {
        return this.controlApprovalsQuery.query?.queryOptions.queryKey;
    }

    setControlApprovalsCurrentQueryData = (
        data: ApprovalsResponseDto,
    ): void => {
        if (!this.queryKey) {
            return;
        }

        queryClient.setQueryData(this.queryKey, (oldData) => {
            if (!oldData) {
                return oldData;
            }

            const existingCurrentApproval = oldData.data.find(
                (approval) => approval.current,
            );

            if (!existingCurrentApproval) {
                return {
                    ...oldData,
                    data: [data, ...oldData.data],
                };
            }

            return {
                ...oldData,
                data: oldData.data.map((approval) => {
                    if (approval.current) {
                        return data;
                    }

                    return approval;
                }),
            };
        });
    };

    deleteControlApprovalQueryData = (approvalId: number): void => {
        if (!this.queryKey) {
            return;
        }

        queryClient.setQueryData(this.queryKey, (oldData) => {
            if (!oldData) {
                return oldData;
            }

            return {
                ...oldData,
                data: oldData.data.filter(
                    (approval) => approval.id !== approvalId,
                ),
            };
        });
    };
}

export const sharedControlApprovalsController =
    new ControlApprovalsController();
