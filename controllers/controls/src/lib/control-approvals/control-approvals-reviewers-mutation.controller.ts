import { uniqueId } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import { ApprovalStatus } from '@drata/enums';
import {
    controlApprovalsControllerCreateCurrentControlApprovalMutation,
    controlApprovalsControllerDeleteControlApprovalMutation,
    controlApprovalsControllerUpdateControlApprovalMutation,
    controlApprovalsReviewControllerUpdateControlApprovalReviewMutation,
} from '@globals/api-sdk/queries';
import type {
    ControlApprovalsControllerUpdateControlApprovalData,
    UpdateApprovalRequestDto,
    UpdateApprovalReviewRequestDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { getControlApprovalUpdateMessages } from '../../helpers/control-approval-update-messages.helper';
import { sharedControlApprovalReviewersController } from './control-approval-reviewers.controller';
import { sharedControlApprovalsController } from './control-approvals.controller';

class ControlApprovalsReviewersMutationController {
    constructor() {
        makeAutoObservable(this);
    }

    updateControlApprovalMutation = new ObservedMutation(
        controlApprovalsControllerUpdateControlApprovalMutation,
        {
            onSuccess: (data, variables) => {
                const [currentVariable] = variables as unknown as [
                    ControlApprovalsControllerUpdateControlApprovalData,
                ];

                const {
                    body: { status },
                } = currentVariable;

                if (status === ApprovalStatus.CHANGES_REQUESTED) {
                    sharedControlApprovalsController.invalidate();

                    return;
                }

                sharedControlApprovalsController.setControlApprovalsCurrentQueryData(
                    data,
                );

                sharedControlApprovalReviewersController.load(
                    data.control,
                    data.id,
                );
            },
        },
    );

    updateControlApprovalReviewMutation = new ObservedMutation(
        controlApprovalsReviewControllerUpdateControlApprovalReviewMutation,
    );

    createCurrentControlApprovalMutation = new ObservedMutation(
        controlApprovalsControllerCreateCurrentControlApprovalMutation,
        {
            onSuccess: (data) => {
                sharedControlApprovalsController.setControlApprovalsCurrentQueryData(
                    data,
                );
                sharedControlApprovalReviewersController.load(
                    data.control,
                    data.id,
                );
            },
        },
    );

    deleteControlApprovalMutation = new ObservedMutation(
        controlApprovalsControllerDeleteControlApprovalMutation,
        {
            onSuccess: (_data, variables) => {
                const [currentVariable] = variables as unknown as [
                    ControlApprovalsControllerUpdateControlApprovalData,
                ];

                const {
                    path: { approvalId },
                } = currentVariable;

                sharedControlApprovalsController.deleteControlApprovalQueryData(
                    approvalId,
                );
            },
        },
    );

    get isUpdatingControlApproval(): boolean {
        return this.updateControlApprovalMutation.isPending;
    }

    get isUpdatingControlApprovalReview(): boolean {
        return this.updateControlApprovalReviewMutation.isPending;
    }

    get isUpdating(): boolean {
        return (
            this.isUpdatingControlApproval ||
            this.isUpdatingControlApprovalReview
        );
    }

    get isCreating(): boolean {
        return this.createCurrentControlApprovalMutation.isPending;
    }

    get isDeleting(): boolean {
        return this.deleteControlApprovalMutation.isPending;
    }

    updateControlApproval = (
        controlId: number,
        approvalId: number,
        body: UpdateApprovalRequestDto,
    ): void => {
        const { currentWorkspace } = sharedWorkspacesController;

        if (!currentWorkspace) {
            return;
        }

        this.updateControlApprovalMutation.mutate({
            path: { approvalId, controlId, xProductId: currentWorkspace.id },
            body,
        });
    };

    updateCurrentControlApproval = (body: UpdateApprovalRequestDto): void => {
        const { currentControlApproval } = sharedControlApprovalsController;

        const messages = getControlApprovalUpdateMessages({
            status: body.status,
            previousStatus: currentControlApproval?.approvalStatus,
        });

        if (!currentControlApproval || !messages) {
            return;
        }

        const { successTitle, errorTitle } = messages;

        this.updateControlApproval(
            currentControlApproval.control,
            currentControlApproval.id,
            body,
        );

        when(
            () => !this.isUpdating,
            () => {
                if (this.updateControlApprovalMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: `update-control-approval-error-${uniqueId()}`,
                        props: {
                            title: errorTitle,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: `update-control-approval-success-${uniqueId()}`,
                    props: {
                        title: successTitle,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    updateNextControlApproval = (body: UpdateApprovalRequestDto): void => {
        const { nextControlApproval } = sharedControlApprovalsController;

        if (!nextControlApproval) {
            return;
        }

        this.updateControlApproval(
            nextControlApproval.control,
            nextControlApproval.id,
            body,
        );
    };

    updateControlApprovalReview = (
        controlId: number,
        approvalId: number,
        reviewId: number,
        body: UpdateApprovalReviewRequestDto,
    ): void => {
        this.updateControlApprovalReviewMutation.mutate({
            path: { approvalId, reviewId, controlId },
            body,
        });
    };

    updateCurrentControlApprovalReview = (
        body: UpdateApprovalReviewRequestDto,
    ): void => {
        const { currentControlApproval } = sharedControlApprovalsController;

        if (!currentControlApproval) {
            return;
        }
        const { id, control, lastReview } = currentControlApproval;

        if (!lastReview) {
            return;
        }

        this.updateControlApprovalReview(control, id, lastReview.id, body);
    };

    createCurrentControlApproval = (controlId: number): void => {
        const { currentControlApproval } = sharedControlApprovalsController;

        if (currentControlApproval) {
            return;
        }

        this.createCurrentControlApprovalMutation.mutate({
            path: { controlId },
        });
    };

    deleteCurrentControlApproval = (): void => {
        const { currentControlApproval } = sharedControlApprovalsController;

        if (!currentControlApproval) {
            return;
        }

        this.deleteControlApprovalMutation.mutate({
            path: {
                approvalId: currentControlApproval.id,
                controlId: currentControlApproval.control,
            },
        });

        when(
            () => !this.isDeleting,
            () => {
                if (this.deleteControlApprovalMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'delete-control-approval-error',
                        props: {
                            title: t`Error deleting control approval`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: 'delete-control-approval-success',
                    props: {
                        title: t`Control approval deleted`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };
}

export const sharedControlApprovalsReviewersMutationController =
    new ControlApprovalsReviewersMutationController();
