import { controlApprovalsReviewControllerGetControlApprovalReviewIndexOptions } from '@globals/api-sdk/queries';
import type { ApprovalsReviewResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

class ControlApprovalReviewersController {
    constructor() {
        makeAutoObservable(this);
    }

    controlApprovalReviewerListQuery = new ObservedQuery(
        controlApprovalsReviewControllerGetControlApprovalReviewIndexOptions,
    );

    load = (controlId: number, approvalId: number): void => {
        this.controlApprovalReviewerListQuery.load({
            path: {
                controlId,
                approvalId,
            },
        });
    };

    get controlApprovalsReviews(): ApprovalsReviewResponseDto[] {
        return this.controlApprovalReviewerListQuery.data?.data ?? [];
    }

    get controlApprovalsReviewers(): ApprovalsReviewResponseDto['reviewer'][] {
        return (
            this.controlApprovalReviewerListQuery.data?.data.map(
                ({ reviewer }) => reviewer,
            ) ?? []
        );
    }

    get isLoading(): boolean {
        return this.controlApprovalReviewerListQuery.isLoading;
    }

    get isFetching(): boolean {
        return this.controlApprovalReviewerListQuery.isFetching;
    }

    invalidate = () => {
        this.controlApprovalReviewerListQuery.invalidate();
    };
}

export const sharedControlApprovalReviewersController =
    new ControlApprovalReviewersController();
