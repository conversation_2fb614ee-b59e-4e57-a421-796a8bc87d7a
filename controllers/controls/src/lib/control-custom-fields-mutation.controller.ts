import { controlCustomFieldsControllerCreateCustomFieldMutation } from '@globals/api-sdk/queries';
import type { CustomFieldSubmissionsRequestDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { makeAutoObservable, ObservedMutation } from '@globals/mobx';

export class ControlCustomFieldsMutationController {
    constructor() {
        makeAutoObservable(this);
    }

    createCustomFieldMutation = new ObservedMutation(
        controlCustomFieldsControllerCreateCustomFieldMutation,
    );

    get isSubmitting(): boolean {
        return this.createCustomFieldMutation.isPending;
    }

    get hasError(): boolean {
        return this.createCustomFieldMutation.hasError;
    }

    get error(): Error | null {
        return this.createCustomFieldMutation.error;
    }

    submitCustomFields = (
        controlId: number,
        customFieldSubmissions: CustomFieldSubmissionsRequestDto,
    ): void => {
        if (!sharedFeatureAccessModel.isCustomFieldsEnabled) {
            return;
        }

        this.createCustomFieldMutation.mutate({
            path: { controlId },
            body: customFieldSubmissions,
        });
    };
}

export const sharedControlCustomFieldsMutationController =
    new ControlCustomFieldsMutationController();
