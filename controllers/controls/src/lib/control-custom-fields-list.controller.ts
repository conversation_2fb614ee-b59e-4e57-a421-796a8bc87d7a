import { controlCustomFieldsControllerGetCustomFieldsListOptions } from '@globals/api-sdk/queries';
import type { CustomFieldsSubmissionListResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

class ControlCustomFieldsListController {
    constructor() {
        makeAutoObservable(this);
    }

    customFieldsListQuery = new ObservedQuery(
        controlCustomFieldsControllerGetCustomFieldsListOptions,
    );

    loadCustomFieldsList = (): void => {
        const { isCustomFieldsEnabled } = sharedFeatureAccessModel;

        if (!isCustomFieldsEnabled) {
            return;
        }

        this.customFieldsListQuery.load();
    };

    get customFieldsList(): CustomFieldsSubmissionListResponseDto['data'] {
        return this.customFieldsListQuery.data?.data ?? [];
    }

    get isLoading(): boolean {
        return this.customFieldsListQuery.isLoading;
    }
}

export const sharedControlCustomFieldsListController =
    new ControlCustomFieldsListController();
