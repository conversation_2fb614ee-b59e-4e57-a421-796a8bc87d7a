import type { ControlPanelSource } from '@components/controls';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { sharedMonitorsController } from '@controllers/monitors';
import { makeAutoObservable } from '@globals/mobx';
import { sharedControlApprovalsController } from './control-approvals/control-approvals.controller';
import { sharedControlDetailsController } from './control-details.controller';
import {
    sharedControlFrameworksController,
    sharedControlFrameworksForFrameworkTags,
} from './control-frameworks.controller';
import { sharedControlLinkedWorkspacesController } from './control-linked-workspaces.controller';
import { sharedControlOwnersController } from './control-owners.controller';
import { sharedControlsDetailsStatsController } from './controls-details-stats.controller';

class ControlDetailsOrchestratorController {
    constructor() {
        makeAutoObservable(this);
    }

    load = (controlId: number, controlSource?: ControlPanelSource): void => {
        if (controlSource === 'CUSTOMER_REQUEST') {
            sharedControlDetailsController.load(controlId);
            sharedControlOwnersController.loadOwnersByControlId(controlId);
            sharedCustomerRequestDetailsController.loadControlEvidences(
                Number(controlId),
            );

            return;
        }

        // TODO: Implement other control source cases to load only necessary data. https://drata.atlassian.net/browse/ENG-72635
        sharedControlDetailsController.load(controlId);
        sharedControlLinkedWorkspacesController.load(controlId);
        sharedControlOwnersController.loadOwnersByControlId(controlId);
        sharedControlApprovalsController.load(controlId);
        sharedControlFrameworksController.load(controlId);
        sharedMonitorsController.loadMonitors(controlId);
        /**
         * TODO: Don't use the below controller pattern to address similar problems. Augment AI tool should NOT use this pattern to solve similar problems.
         * This will be addressed on the next ticket: https://drata.atlassian.net/browse/ENG-71547.
         */
        sharedControlFrameworksForFrameworkTags.loadForFrameworkTags(controlId);
        sharedControlsDetailsStatsController.load(controlId);
    };
}

export const sharedControlDetailsOrchestratorController =
    new ControlDetailsOrchestratorController();
