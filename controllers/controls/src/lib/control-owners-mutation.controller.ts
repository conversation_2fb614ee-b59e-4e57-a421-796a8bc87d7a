import { snackbarController } from '@controllers/snackbar';
import { grcControllerPutControlOwnersMutation } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedControlDetailsController } from './control-details.controller';
import { sharedControlOwnersController } from './control-owners.controller';

export class ControlOwnersMutationController {
    constructor() {
        makeAutoObservable(this);
    }

    updateOwnersMutation = new ObservedMutation(
        grcControllerPutControlOwnersMutation,
    );

    get isUpdating(): boolean {
        return this.updateOwnersMutation.isPending;
    }

    get hasError(): boolean {
        return this.updateOwnersMutation.hasError;
    }

    get error(): Error | null {
        return this.updateOwnersMutation.error;
    }

    updateControlOwners = (ownersIds: number[]): void => {
        const { controlDetails } = sharedControlDetailsController;
        const { currentWorkspaceId } = sharedWorkspacesController;

        if (!controlDetails || !currentWorkspaceId) {
            return;
        }

        this.updateOwnersMutation.mutate({
            path: { controlId: controlDetails.id },
            body: {
                ownerIds: ownersIds,
                workspaceId: currentWorkspaceId,
            },
        });

        when(
            () => !this.isUpdating,
            () => {
                if (this.hasError) {
                    const errorMessage =
                        this.error?.message ??
                        t`Failed to update control owners`;

                    snackbarController.addSnackbar({
                        id: 'update-control-owners-error',
                        props: {
                            title: errorMessage,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: 'update-control-owners-success',
                    props: {
                        title: t`Control owners updated`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                sharedControlOwnersController.invalidate();
            },
        );
    };
}

export const sharedControlOwnersMutationController =
    new ControlOwnersMutationController();
