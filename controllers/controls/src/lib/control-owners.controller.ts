import { grcControllerGetControlOwnersForAControlOptions } from '@globals/api-sdk/queries';
import type { UserCardResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

export class ControlOwnersController {
    constructor() {
        makeAutoObservable(this);
    }

    controlOwnersQuery = new ObservedQuery(
        grcControllerGetControlOwnersForAControlOptions,
    );

    get isLoading(): boolean {
        return this.controlOwnersQuery.isLoading;
    }

    get controlOwners(): UserCardResponseDto[] {
        return this.controlOwnersQuery.data?.data ?? [];
    }

    get total(): number {
        return this.controlOwnersQuery.data?.total ?? 0;
    }

    get controlHasOwners(): boolean {
        return this.total > 0;
    }

    loadOwnersByControlId = (controlId: number): void => {
        this.controlOwnersQuery.load({
            path: { controlId },
        });
    };

    get isReady(): boolean {
        return this.controlOwnersQuery.isReady;
    }

    invalidate = (): void => {
        this.controlOwnersQuery.invalidate();
    };
}

export const sharedControlOwnersController = new ControlOwnersController();
