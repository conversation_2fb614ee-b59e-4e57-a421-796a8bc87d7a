import { isArray, isEmpty, isError, isObject } from 'lodash-es';
import { sharedMonitorsInfiniteController } from '@controllers/monitors';
import { sharedRequirementsController } from '@controllers/requirements';
import { snackbarController } from '@controllers/snackbar';
import { grcControllerCreateControlMutation } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    type ControlInfoValues,
    sharedCreateControlInfoModel,
    sharedMapEvidenceModel,
    sharedMapPoliciesModel,
} from '@models/controls';

class CreateControlController {
    createControlMutation = new ObservedMutation(
        grcControllerCreateControlMutation,
    );

    constructor() {
        makeAutoObservable(this);
    }

    get isSubmitting(): boolean {
        return this.createControlMutation.isPending;
    }

    get error(): Error | null {
        return this.createControlMutation.error;
    }

    createControl = (
        controlInfoValues: ControlInfoValues,
        onSuccess?: () => void,
        onError?: (error: string) => void,
    ): void => {
        if (this.isSubmitting) {
            return;
        }

        const workspaceId = sharedWorkspacesController.currentWorkspace?.id;

        if (!workspaceId) {
            const errorMessage = t`No workspace selected`;

            onError?.(errorMessage);

            return;
        }

        const requestBody = {
            name: controlInfoValues.name || '',
            code: controlInfoValues.code || '',
            description: controlInfoValues.description || '',
            ...(controlInfoValues.question?.trim()
                ? { question: controlInfoValues.question }
                : {}),
            ...(controlInfoValues.activity?.trim()
                ? { activity: controlInfoValues.activity }
                : {}),
            owners:
                controlInfoValues.owner && !isEmpty(controlInfoValues.owner)
                    ? controlInfoValues.owner.map((owner) => Number(owner.id))
                    : [],

            requirementIds: isEmpty(
                sharedRequirementsController.mappedRequirements,
            )
                ? []
                : sharedRequirementsController.mappedRequirements.map(
                      (req) => req.id,
                  ),

            testIds: isEmpty(sharedMonitorsInfiniteController.selectedMonitors)
                ? []
                : sharedMonitorsInfiniteController.selectedMonitors.map(
                      (monitor) => Number(monitor.testId),
                  ),

            reportIds: isEmpty(sharedMapEvidenceModel.selectedEvidence)
                ? []
                : sharedMapEvidenceModel.selectedEvidence.map((evidence) =>
                      Number(evidence.id),
                  ),

            policyIds: isEmpty(sharedMapPoliciesModel.selectedPolicies)
                ? []
                : sharedMapPoliciesModel.selectedPolicies.map(
                      (policy) => policy.id,
                  ),

            // TODO: These are required fields in the DTO. Need to look further into what they are used for
            externalEvidenceMetadata: '',
            base64Files: '[]',
        };

        this.createControlMutation.mutate({
            path: {
                xProductId: workspaceId,
            },
            body: requestBody,
        });

        when(
            () => !this.isSubmitting,
            () => {
                if (this.createControlMutation.hasError) {
                    const { error } = this.createControlMutation;
                    let errorMessage = t`Unable to create control at this time.`;

                    if (error) {
                        if (isError(error)) {
                            errorMessage = error.message;
                        } else if (isObject(error)) {
                            const apiError = error as {
                                message?: string | string[];
                                statusCode?: number;
                            };

                            if (apiError.message) {
                                if (isArray(apiError.message)) {
                                    errorMessage = apiError.message.join('\n');
                                } else {
                                    errorMessage = apiError.message;
                                }
                            }
                        }
                    }

                    onError?.(errorMessage);

                    return;
                }

                snackbarController.addSnackbar({
                    id: 'control-create-success',
                    hasTimeout: true,
                    props: {
                        title: t`Control created`,
                        description: t`The control was created successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: 'Close',
                    },
                });

                sharedCreateControlInfoModel.setControlInfoValues({});
                sharedMonitorsInfiniteController.clearSelectedMonitors();
                sharedRequirementsController.clearMappedRequirements();
                sharedMapEvidenceModel.clearSelectedEvidence();
                sharedMapPoliciesModel.clearSelectedPolicies();

                onSuccess?.();
            },
        );
    };

    handleNavigationAfterCreation = (
        navigate: (path: string) => void,
        location: { pathname: string },
    ): void => {
        when(
            () => {
                const { response } = this.createControlMutation;

                return Boolean(response?.id);
            },
            () => {
                const createdControl = this.createControlMutation.response;

                if (createdControl?.id) {
                    const pathSegments = location.pathname.split('/');
                    const workspaceId = pathSegments[2];

                    navigate(
                        `/workspaces/${workspaceId}/compliance/controls/${createdControl.id}/overview`,
                    );
                }
            },
        );
    };
}

export const sharedCreateControlController = new CreateControlController();
