import { ApprovalStatus } from '@drata/enums';
import type { UpdateApprovalRequestDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

interface GetControlApprovalUpdateMessagesProps {
    status: UpdateApprovalRequestDto['status'];
    previousStatus?: UpdateApprovalRequestDto['status'];
}

export const getControlApprovalUpdateMessages = ({
    status,
    previousStatus,
}: GetControlApprovalUpdateMessagesProps):
    | {
          successTitle: string;
          errorTitle: string;
      }
    | undefined => {
    if (previousStatus === ApprovalStatus.INITIALIZED) {
        return {
            successTitle: t`Required approval saved`,
            errorTitle: t`Failed to save required approval`,
        };
    }

    if (status === previousStatus) {
        return {
            successTitle: t`Required approval updated`,
            errorTitle: t`Failed to update required approval`,
        };
    }

    switch (status) {
        case ApprovalStatus.READY_FOR_REVIEWS:
        case ApprovalStatus.PREPARE_FOR_REVIEWS: {
            return {
                successTitle: t`Sent to approvers`,
                errorTitle: t`Failed to send to approvers`,
            };
        }
        case ApprovalStatus.CHANGES_REQUESTED: {
            return {
                successTitle: t`Change request sent`,
                errorTitle: t`Failed to send change request`,
            };
        }
        case ApprovalStatus.APPROVED: {
            return {
                successTitle: t`Control approved`,
                errorTitle: t`Failed to approve control`,
            };
        }
        default: {
            return undefined;
        }
    }
};
