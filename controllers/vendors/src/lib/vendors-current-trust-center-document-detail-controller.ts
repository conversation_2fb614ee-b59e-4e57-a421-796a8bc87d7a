import { vendorTrustCenterControllerGetVendorTrustCenterDocumentByIdOptions } from '@globals/api-sdk/queries';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedVendorTrustCenterController } from './vendor-trust-center.controller';

class VendorsCurrentTrustCenterDocumentDetailController {
    documentData = new ObservedQuery(
        vendorTrustCenterControllerGetVendorTrustCenterDocumentByIdOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    get fileName(): string | null {
        return this.documentData.data?.fileName ?? null;
    }

    get downloadUrl(): string | null {
        return this.documentData.data?.url ?? null;
    }

    get isLoading(): boolean {
        return this.documentData.isLoading;
    }

    loadDocumentData = (
        options: Parameters<typeof this.documentData.load>[0],
    ) => {
        when(
            () => sharedVendorTrustCenterController.isVendorTrustCenterEnabled,
            () => {
                this.documentData.load(options);
            },
        );
    };
}

export const sharedVendorsCurrentTrustCenterDocumentDetailController =
    new VendorsCurrentTrustCenterDocumentDetailController();
