import { isNil } from 'lodash-es';
import { VENDOR_DELETE_SECURITY_REVIEW_FILE_MODAL_ID } from '@components/vendors-current-security-review-files';
import { modalController } from '@controllers/modal';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { snackbarController } from '@controllers/snackbar';
import {
    vendorsControllerDeleteSecurityReviewDocumentMutation,
    vendorsControllerGetPdfVendorDocumentDownloadUrlOptions,
    vendorsControllerGetSecurityReviewDocumentOptions,
} from '@globals/api-sdk/queries';
import type {
    SignedUrlResponseDto,
    VendorSecurityReviewDocumentResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

class VendorsSecurityReviewFileController {
    constructor() {
        makeAutoObservable(this);
    }

    vendorId: number | null = null;
    securityReviewId: number | null = null;
    vendorType: 'current' | 'prospective' = 'current';
    pdfDownloadUrlQuery = new ObservedQuery(
        vendorsControllerGetPdfVendorDocumentDownloadUrlOptions,
    );

    securityReviewDocumentQuery = new ObservedQuery(
        vendorsControllerGetSecurityReviewDocumentOptions,
    );

    deleteSecurityReviewDocumentMutation = new ObservedMutation(
        vendorsControllerDeleteSecurityReviewDocumentMutation,
    );

    setVendorId = (vendorId: number) => {
        this.vendorId = vendorId;
    };

    setSecurityReviewId = (securityReviewId: number) => {
        this.securityReviewId = securityReviewId;
    };

    get currentVendorId(): number | null {
        return this.vendorId;
    }

    get currentSecurityReviewId(): number | null {
        return this.securityReviewId;
    }

    get isDeleting(): boolean {
        return this.deleteSecurityReviewDocumentMutation.isPending;
    }

    get pdfDownloadUrl(): SignedUrlResponseDto | null {
        return this.pdfDownloadUrlQuery.data;
    }

    get securityReviewDocument(): VendorSecurityReviewDocumentResponseDto | null {
        return this.securityReviewDocumentQuery.data;
    }

    get isLoading(): boolean {
        return this.pdfDownloadUrlQuery.isLoading;
    }

    loadPdfDownloadUrl = (docId: number) => {
        if (!docId || !this.currentVendorId) {
            return;
        }

        this.pdfDownloadUrlQuery.load({
            path: { id: this.currentVendorId, docId },
        });
    };

    loadSecurityReviewDocument = (docId: number) => {
        if (!docId || !this.currentSecurityReviewId) {
            return;
        }

        this.securityReviewDocumentQuery.load({
            path: { id: this.currentSecurityReviewId, documentId: docId },
        });
        when(
            () => !this.securityReviewDocumentQuery.isLoading,
            () => {
                const { currentWorkspace } = sharedWorkspacesController;

                if (
                    this.securityReviewDocumentQuery.hasError ||
                    isNil(currentWorkspace)
                ) {
                    snackbarController.addSnackbar({
                        id: 'security-review-document-not-found-error',
                        props: {
                            title: t`Security review document not found.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                    sharedProgrammaticNavigationController.navigateTo(
                        `workspaces/${currentWorkspace?.id}/vendors/${this.vendorType}/${this.currentVendorId}/security-reviews/${this.currentSecurityReviewId}`,
                    );
                }
            },
        );
    };

    deleteSecurityReviewDocument = () => {
        const docId = this.securityReviewDocumentQuery.data?.id;
        const { currentWorkspace } = sharedWorkspacesController;

        if (!docId || !this.currentSecurityReviewId || !currentWorkspace?.id) {
            return;
        }

        this.deleteSecurityReviewDocumentMutation.mutate({
            path: { id: docId },
        });
        when(
            () => !this.deleteSecurityReviewDocumentMutation.isPending,
            () => {
                if (this.deleteSecurityReviewDocumentMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'security-review-document-delete-error',
                        props: {
                            title: t`Error deleting security review document`,
                            description: t`An error occurred while deleting the security review document. Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        hasTimeout: true,
                        id: 'security-review-document-delete-success',
                        props: {
                            title: t`Security review document deleted`,
                            description: t`The security review document was deleted successfully.`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                    modalController.closeModal(
                        VENDOR_DELETE_SECURITY_REVIEW_FILE_MODAL_ID,
                    );
                    sharedProgrammaticNavigationController.navigateTo(
                        `workspaces/${currentWorkspace.id}/vendors/${this.vendorType}/${this.currentVendorId}/security-reviews/${this.currentSecurityReviewId}`,
                    );
                }
            },
        );
    };

    setVendorType = (vendorType: 'current' | 'prospective') => {
        this.vendorType = vendorType;
    };
}

export const sharedVendorsSecurityReviewFileController =
    new VendorsSecurityReviewFileController();
