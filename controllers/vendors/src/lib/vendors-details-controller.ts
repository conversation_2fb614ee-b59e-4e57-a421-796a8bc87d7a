import { isEmpty, isNil } from 'lodash-es';
import { calculateReviewDeadlineDate } from '@components/vendors-security-reviews';
import { modalController } from '@controllers/modal';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { snackbarController } from '@controllers/snackbar';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import {
    vendorsControllerDeleteVendorMutation,
    vendorsControllerGetVendorOptions,
    vendorsControllerUpdateVendorMutation,
    vendorsControllerUpdateVendorStatusMutation,
} from '@globals/api-sdk/queries';
import type {
    VendorRelationshipContactRequestDto,
    VendorRequestDto,
    VendorResponseDto,
    VendorSecurityReviewResponseDto,
} from '@globals/api-sdk/types';
import { zVendorRequestDto } from '@globals/api-sdk/zod';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    toJS,
    when,
} from '@globals/mobx';
import { addDaysToDate, formatDate } from '@helpers/date-time';
import { convertToMinorUnits } from '@helpers/formatters';
import type { FormValues } from '@ui/forms';
import type { VendorProfileHeader } from './types/vendor-profile.type';
import { sharedVendorsCurrentController } from './vendors-current-controller';
import { sharedVendorsProspectiveController } from './vendors-prospective-controller';
import { sharedVendorsSettingsController } from './vendors-settings-controller';

class VendorsDetailsController {
    constructor() {
        makeAutoObservable(this);
    }

    vendorDetailsQuery = new ObservedQuery(vendorsControllerGetVendorOptions);

    mutatedVendorDetails: VendorRequestDto | null = null;

    updateDetailsMutation = new ObservedMutation(
        vendorsControllerUpdateVendorMutation,
        {
            onSuccess: () => {
                this.vendorDetailsQuery.invalidate();
            },
        },
    );

    updateVendorStatusMutation = new ObservedMutation(
        vendorsControllerUpdateVendorStatusMutation,
    );

    deleteVendorMutation = new ObservedMutation(
        vendorsControllerDeleteVendorMutation,
    );

    get vendorDetails(): VendorResponseDto | null {
        return this.vendorDetailsQuery.data;
    }

    get isProspectiveVendor(): boolean {
        return this.vendorDetails?.status === 'PROSPECTIVE';
    }

    get isLoading(): boolean {
        return this.vendorDetailsQuery.isLoading;
    }

    get isSaving(): boolean {
        return this.updateDetailsMutation.isPending;
    }

    get isUpdatingVendor(): boolean {
        return this.isLoading || this.isSaving;
    }

    get hasErrorVendorUpdate(): boolean {
        return this.updateDetailsMutation.hasError;
    }

    get profileHeaderData(): VendorProfileHeader | null {
        const { data } = this.vendorDetailsQuery;

        if (!data) {
            return null;
        }

        const { vendorSettings } = sharedVendorsSettingsController;

        const {
            name,
            logoUrl,
            category,
            impactLevel,
            risk,
            riskCount,
            latestSecurityReviews,
            status,
            renewalDateStatus,
            renewalDate,
        } = data;
        const showSecurityReviewsInfoBanner =
            latestSecurityReviews?.some(
                (item: VendorSecurityReviewResponseDto) =>
                    item.status === 'IN_PROGRESS',
            ) ?? false;

        const buildSecurityReviewWindow = (): string | undefined => {
            const { renewalScheduleType } = data;

            if (renewalScheduleType === 'NONE') {
                return undefined;
            }

            if (
                !isNil(renewalScheduleType) &&
                renewalScheduleType !== 'CUSTOM' &&
                vendorSettings?.defaultReviewPeriod
            ) {
                const renewalDeadlineDate =
                    calculateReviewDeadlineDate(renewalScheduleType);

                const reviewStartDate = addDaysToDate(
                    renewalDeadlineDate,
                    -vendorSettings.defaultReviewPeriod,
                );

                const reviewStartFormatted = formatDate(
                    'field',
                    reviewStartDate,
                );
                const renewalDeadlineFormatted = formatDate(
                    'field',
                    renewalDeadlineDate,
                );

                return `${reviewStartFormatted} to ${renewalDeadlineFormatted}`;
            }

            if (
                renewalDateStatus !== 'NO_RENEWAL' &&
                renewalDate &&
                vendorSettings
            ) {
                const renewalFormattedDate = formatDate(
                    'field',
                    addDaysToDate(
                        renewalDate,
                        -vendorSettings.defaultReviewPeriod,
                    ),
                );

                const formattedDate = formatDate('field', renewalDate);

                return `${renewalFormattedDate} to ${formattedDate}`;
            }

            return undefined;
        };

        const currentRiskCount =
            status === 'PROSPECTIVE' ? undefined : (riskCount ?? 0);

        return {
            name,
            logoUrl: logoUrl ?? '',
            showSecurityReviewsInfoBanner,
            businessUnit: category,
            impactLevel,
            risk,
            associatedRisks: currentRiskCount,
            securityReviewWindow: buildSecurityReviewWindow(),
        };
    }

    setVendorProperty = <T extends keyof VendorRequestDto>(
        prop: T,
        value: VendorRequestDto[T],
    ): void => {
        if (!this.mutatedVendorDetails) {
            return;
        }

        this.mutatedVendorDetails[prop] = value;
    };

    loadVendorDetails = (vendorId: number) => {
        this.vendorDetailsQuery.load({
            path: { id: vendorId },
        });
    };

    createMutatedVendorDetails = (data: VendorResponseDto | null) => {
        const dataForZodValidation = {
            ...data,
            status: data?.status ?? undefined,
            location: data?.location ?? undefined,
            operationalImpact: data?.operationalImpact ?? undefined,
            environmentAccess: data?.environmentAccess ?? undefined,
            impactLevel: data?.impactLevel ?? undefined,
            renewalScheduleType: data?.renewalScheduleType ?? undefined,
            renewalDate: data?.renewalDate ?? undefined,
            passwordPolicy: data?.passwordPolicy ?? 'NONE',
            type: data?.type ?? 'NONE',
            category: data?.category ?? undefined,
            url: data?.url === '' ? null : data?.url,
            integrations:
                data?.integrations?.map((integration) => integration.id) ?? [],
            userId: data?.user?.id ?? null,
            contact: data?.contact ? { id: data.contact.id } : null,
            notes: data?.notes === null ? undefined : data?.notes,
        };

        this.mutatedVendorDetails =
            zVendorRequestDto.parse(dataForZodValidation);
    };

    updateVendorDetails = (vendorId: number, formValues: FormValues) => {
        const passwordPolicyGroup = formValues.passwordPolicyGroup as
            | FormValues
            | undefined;
        const passwordPolicyValue = passwordPolicyGroup?.passwordPolicy as
            | ListBoxItemData
            | undefined;
        const passwordMinLengthValue = passwordPolicyGroup?.passwordMinLength as
            | ListBoxItemData
            | undefined;

        const integrationsValue = formValues.integrations as
            | ListBoxItemData[]
            | undefined;
        let transformedIntegrations: number[];

        if ('integrations' in formValues) {
            // Field provided: allow clearing (empty array) or setting new values
            transformedIntegrations =
                integrationsValue?.map((integration) =>
                    Number(integration.value),
                ) ?? [];
        } else {
            // Field not provided: preserve existing integrations
            transformedIntegrations =
                this.mutatedVendorDetails?.integrations ?? [];
        }

        const userValue = formValues.user as ListBoxItemData | undefined;
        let transformedUserId: number | null | undefined;

        if ('user' in formValues) {
            // Field provided: allow clearing (null) or setting new value
            transformedUserId = userValue ? Number(userValue.value) : null;
        } else {
            // Field not provided: preserve existing value
            transformedUserId = this.mutatedVendorDetails?.userId;
        }

        const contactValue = formValues.contact as ListBoxItemData | undefined;
        let transformedContact:
            | VendorRelationshipContactRequestDto
            | null
            | undefined;

        if ('contact' in formValues) {
            // Field provided: allow clearing (null) or setting new value
            transformedContact = contactValue
                ? { id: Number(contactValue.value) }
                : null;
        } else {
            // Field not provided: preserve existing value
            transformedContact = this.mutatedVendorDetails?.contact;
        }

        const baseData = {
            ...this.mutatedVendorDetails,
            ...formValues,
            ...passwordPolicyGroup,
            passwordPolicy:
                passwordPolicyValue?.value ??
                this.mutatedVendorDetails?.passwordPolicy ??
                'NONE',
            passwordMinLength: passwordMinLengthValue
                ? Number(passwordMinLengthValue.value)
                : this.mutatedVendorDetails?.passwordMinLength,
            integrations: transformedIntegrations,
            userId: transformedUserId,
            contact: transformedContact,
        };

        const dataForZodValidation = {
            ...baseData,
            notes: isNil(baseData.notes) ? undefined : baseData.notes,
        };

        this.mutatedVendorDetails =
            zVendorRequestDto.parse(dataForZodValidation);

        const apiPayload = {
            ...toJS(this.mutatedVendorDetails),
            notes: baseData.notes,
        };

        this.updateDetailsMutation.mutate({
            path: { id: vendorId },
            body: apiPayload,
        });

        when(
            () => !this.updateDetailsMutation.isPending,
            () => {
                if (this.updateDetailsMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'vendor-data-update-error',
                        props: {
                            title: t`An error occurred while saving the vendor data.`,
                            description: t`Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: 'vendor-data-updated-successfully',
                    props: {
                        title: t`Vendor Updated.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    updateBaseDetails = (
        vendorId: number,
        currentState: VendorResponseDto,
        formValues: FormValues,
    ) => {
        this.createMutatedVendorDetails(currentState);

        const passwordPolicyGroup = formValues.passwordPolicyGroup as
            | FormValues
            | undefined;
        const passwordPolicyValue = passwordPolicyGroup?.passwordPolicy as
            | ListBoxItemData
            | undefined;

        const newValues: FormValues = {
            ...formValues,
            passwordPolicy: passwordPolicyValue?.value ?? 'NONE',
            passwordPolicyGroup: {
                passwordPolicy: passwordPolicyValue,
                passwordRequiresMinLength:
                    passwordPolicyGroup?.passwordRequiresMinLength ?? false,
                passwordMinLength: passwordPolicyGroup?.passwordMinLength,
                passwordRequiresNumber:
                    passwordPolicyGroup?.passwordRequiresNumber ?? false,
                passwordRequiresSymbol:
                    passwordPolicyGroup?.passwordRequiresSymbol ?? false,
                passwordMfaEnabled:
                    passwordPolicyGroup?.passwordMfaEnabled ?? false,
            },
            url: this.getUrlValue(formValues),
            servicesProvided: this.getServicesProvidedValue(formValues),
            privacyUrl: this.getPrivacyUrlValue(formValues),
            termsUrl: this.getTermsUrlValue(formValues),
            contactAtVendor: this.getContactAtVendorValue(formValues),
            contactsEmail: this.getContactsEmailValue(formValues),
        };

        this.updateVendorDetails(vendorId, newValues);
    };

    updateImpactAssessmentDetails = (
        vendorId: number,
        currentState: VendorResponseDto,
        formValues: FormValues,
    ) => {
        this.createMutatedVendorDetails(currentState);

        const impactLevelValue = formValues.impactLevel as
            | ListBoxItemData
            | undefined;

        const newValues: FormValues = {
            ...formValues,
            impactLevel: impactLevelValue?.value ?? undefined,
            dataAccessedOrProcessedList: isEmpty(
                formValues.dataAccessedOrProcessedList,
            )
                ? undefined
                : formValues.dataAccessedOrProcessedList,
            operationalImpact: isEmpty(formValues.operationalImpact)
                ? undefined
                : formValues.operationalImpact,
            environmentAccess: isEmpty(formValues.environmentAccess)
                ? undefined
                : formValues.environmentAccess,
            passwordMinLength: currentState.passwordMinLength,
        };

        this.updateVendorDetails(vendorId, newValues);
    };

    updateInternalDetails = (
        vendorId: number,
        currentState: VendorResponseDto,
        formValues: FormValues,
    ) => {
        this.createMutatedVendorDetails(currentState);

        const categoryValue = formValues.category as
            | ListBoxItemData
            | undefined;
        const riskValue = formValues.risk as ListBoxItemData | undefined;
        const statusValue = formValues.status as ListBoxItemData | undefined;
        const typeValue = formValues.type as ListBoxItemData | undefined;
        const impactLevelValue = formValues.impactLevel as
            | ListBoxItemData
            | undefined;

        const newValues: FormValues = {
            ...formValues,
            category: categoryValue?.value ?? undefined,
            risk: riskValue?.value ?? undefined,
            status: statusValue?.value ?? undefined,
            type: typeValue?.value ?? undefined,
            impactLevel:
                'impactLevel' in formValues
                    ? (impactLevelValue?.value ?? undefined)
                    : this.mutatedVendorDetails?.impactLevel,
            dataStored: this.getDataStoredValue(formValues),
            notes: this.getNotesValue(formValues),
            cost: this.getCostValue(formValues),
        };

        this.updateVendorDetails(vendorId, newValues);
    };

    deleteVendor = (vendorId: number, isProspective?: boolean): void => {
        if (!vendorId) {
            return;
        }

        this.deleteVendorMutation.mutate({ path: { id: vendorId } });
        when(
            () => !this.deleteVendorMutation.isPending,
            () => {
                if (this.deleteVendorMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'vendor-deletion-error',
                        props: {
                            title: t`An error occurred while deleting the vendor.`,
                            description: t`Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                if (isProspective) {
                    sharedVendorsProspectiveController.allVendorsProspectiveQuery.invalidate();
                } else {
                    sharedVendorsCurrentController.allVendorsCurrentQuery.invalidate();
                }
                modalController.closeModal('delete-vendor-modal');

                snackbarController.addSnackbar({
                    id: 'vendor-deleted-successfully',
                    hasTimeout: true,
                    props: {
                        title: t`Vendor deleted successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    archiveVendor = (vendorId: number, isProspective?: boolean): void => {
        if (!vendorId) {
            return;
        }

        this.updateVendorStatusMutation.mutate({
            path: { id: vendorId },
            body: {
                vendorStatus: 'ARCHIVED',
            },
        });
        when(
            () => !this.updateVendorStatusMutation.isPending,
            () => {
                if (this.updateVendorStatusMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'vendor-archive-error',
                        props: {
                            title: t`An error occurred while archiving the vendor.`,
                            description: t`Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                // Invalidate queries to refresh data
                this.vendorDetailsQuery.invalidate();

                // Invalidate the appropriate vendor list based on vendor type
                const isVendorProspective =
                    isProspective ?? this.isProspectiveVendor;

                if (isVendorProspective) {
                    sharedVendorsProspectiveController.allVendorsProspectiveQuery.invalidate();
                } else {
                    sharedVendorsCurrentController.allVendorsCurrentQuery.invalidate();
                }

                // Close the modal
                modalController.closeModal('archive-vendor-modal');

                // Show success message
                snackbarController.addSnackbar({
                    id: 'vendor-archived-successfully',
                    hasTimeout: true,
                    props: {
                        title: t`Vendor archived successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    archiveVendorAndRedirect = (
        vendorId: number,
        redirectPath: string,
    ): void => {
        if (!vendorId) {
            return;
        }

        this.archiveVendor(vendorId, false);

        when(
            () => !this.updateVendorStatusMutation.isPending,
            () => {
                if (!this.updateVendorStatusMutation.hasError) {
                    sharedProgrammaticNavigationController.navigateTo(
                        redirectPath,
                    );
                }
            },
        );
    };

    private getDataStoredValue(formValues: FormValues): string | null {
        if (!('dataStored' in formValues)) {
            return this.mutatedVendorDetails?.dataStored ?? null;
        }
        if (formValues.dataStored === '' || formValues.dataStored === null) {
            return null;
        }

        return formValues.dataStored as string;
    }

    private getNotesValue(formValues: FormValues): string | null {
        if (!('notes' in formValues)) {
            return this.mutatedVendorDetails?.notes ?? null;
        }
        if (formValues.notes === '' || formValues.notes === null) {
            return null;
        }

        return formValues.notes as string;
    }

    private getCostValue(formValues: FormValues): string | null {
        if (!('cost' in formValues)) {
            return this.mutatedVendorDetails?.cost ?? null;
        }
        const cost = formValues.cost as string;

        if (cost === '' || isNaN(Number(cost))) {
            return null;
        }

        return convertToMinorUnits(formValues.cost as string);
    }

    getUrlValue(formValues: FormValues): string | null {
        if (!('url' in formValues)) {
            return this.mutatedVendorDetails?.url ?? null;
        }
        if (formValues.url === '' || formValues.url === null) {
            return null;
        }

        return formValues.url as string;
    }

    private getServicesProvidedValue(formValues: FormValues): string | null {
        if (!('servicesProvided' in formValues)) {
            return this.mutatedVendorDetails?.servicesProvided ?? null;
        }
        if (
            formValues.servicesProvided === '' ||
            formValues.servicesProvided === null
        ) {
            return null;
        }

        return formValues.servicesProvided as string;
    }

    private getPrivacyUrlValue(formValues: FormValues): string | null {
        if (!('privacyUrl' in formValues)) {
            return this.mutatedVendorDetails?.privacyUrl ?? null;
        }
        if (formValues.privacyUrl === '' || formValues.privacyUrl === null) {
            return null;
        }

        return formValues.privacyUrl as string;
    }

    private getTermsUrlValue(formValues: FormValues): string | null {
        if (!('termsUrl' in formValues)) {
            return this.mutatedVendorDetails?.termsUrl ?? null;
        }
        if (formValues.termsUrl === '' || formValues.termsUrl === null) {
            return null;
        }

        return formValues.termsUrl as string;
    }

    private getContactAtVendorValue(formValues: FormValues): string | null {
        if (!('contactAtVendor' in formValues)) {
            return this.mutatedVendorDetails?.contactAtVendor ?? null;
        }
        if (
            formValues.contactAtVendor === '' ||
            formValues.contactAtVendor === null
        ) {
            return null;
        }

        return formValues.contactAtVendor as string;
    }

    private getContactsEmailValue(formValues: FormValues): string | null {
        if (!('contactsEmail' in formValues)) {
            return this.mutatedVendorDetails?.contactsEmail ?? null;
        }
        if (
            formValues.contactsEmail === '' ||
            formValues.contactsEmail === null
        ) {
            return null;
        }

        return formValues.contactsEmail as string;
    }

    restoreVendor = (vendorId: number, isProspective?: boolean): void => {
        if (!vendorId) {
            return;
        }

        this.updateVendorStatusMutation.mutate({
            path: { id: vendorId },
            body: {
                vendorStatus: 'ACTIVE',
            },
        });
        when(
            () => !this.updateVendorStatusMutation.isPending,
            () => {
                if (this.updateVendorStatusMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'vendor-restore-error',
                        props: {
                            title: t`An error occurred while restoring the vendor.`,
                            description: t`Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                // Invalidate queries to refresh data
                this.vendorDetailsQuery.invalidate();

                // Invalidate the appropriate vendor list based on vendor type
                const isVendorProspective =
                    isProspective ?? this.isProspectiveVendor;

                if (isVendorProspective) {
                    sharedVendorsProspectiveController.allVendorsProspectiveQuery.invalidate();
                } else {
                    sharedVendorsCurrentController.allVendorsCurrentQuery.invalidate();
                }

                // Show success message
                snackbarController.addSnackbar({
                    id: 'vendor-restored-successfully',
                    hasTimeout: true,
                    props: {
                        title: t`Vendor restored successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };
}

export const sharedVendorsDetailsController = new VendorsDetailsController();
