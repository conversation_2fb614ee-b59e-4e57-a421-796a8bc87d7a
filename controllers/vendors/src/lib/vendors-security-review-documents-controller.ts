import { isEmpty } from 'lodash-es';
import {
    ADD_SOC_REVIEW_MODAL_ID,
    UPLOAD_REVIEW_REPORT_MODAL_ID,
} from '@components/vendors-security-reviews';
import { modalController } from '@controllers/modal';
import { snackbarController } from '@controllers/snackbar';
import {
    vendorsControllerCreateSecurityReviewDocumentMutation,
    vendorsControllerGetPdfVendorDocumentDownloadUrlOptions,
    vendorsControllerGetSecurityReviewDocumentsOptions,
    vendorsControllerUploadVendorDocumentMutation,
} from '@globals/api-sdk/queries';
import type {
    SignedUrlResponseDto,
    VendorsControllerUploadVendorDocumentData,
    VendorSecurityReviewDocumentRequestDto,
    VendorSecurityReviewDocumentResponseDto,
    VendorSecurityReviewRequestDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';
import type { NavigateFunction } from '@remix-run/react';
import { VENDORS_DETAILS_TABS_DEFAULT_PARAMS } from './constants/vendors-profile-documents.constants';
import {
    transformDocumentsToFiles,
    transformDocumentsToQuestionnaires,
    transformToDocuments,
} from './helpers/vendors-security-review-documents-adapter.helper';
import type {
    Document,
    DocumentFiles,
    QuestionnaireFiles,
} from './types/vendor-security-review.type';
import { sharedVendorsCurrentSecurityReviewsController } from './vendors-current-security-reviews-controller';

export class VendorsSecurityReviewDocumentsController {
    constructor() {
        makeAutoObservable(this);
    }

    vendorId: number | null = null;

    setVendorId = (vendorId: number): void => {
        this.vendorId = vendorId;
    };

    get currentVendorId(): number | null {
        return this.vendorId;
    }

    securityReviewDocumentsQuery = new ObservedQuery(
        vendorsControllerGetSecurityReviewDocumentsOptions,
    );

    pdfDownloadUrlQuery = new ObservedQuery(
        vendorsControllerGetPdfVendorDocumentDownloadUrlOptions,
    );

    uploadFileMutation = new ObservedMutation(
        vendorsControllerUploadVendorDocumentMutation,
    );
    createSecurityReviewDocumentMutation = new ObservedMutation(
        vendorsControllerCreateSecurityReviewDocumentMutation,
    );

    get socDocument(): VendorSecurityReviewDocumentResponseDto | undefined {
        return this.allSecurityReviewDocuments.find(
            ({ type }) => type === 'SOC_REPORT',
        );
    }

    get reviewDocument(): VendorSecurityReviewDocumentResponseDto | undefined {
        return this.allSecurityReviewDocuments.find(
            ({ type }) => type === 'REVIEW',
        );
    }

    get allSecurityReviewDocuments(): VendorSecurityReviewDocumentResponseDto[] {
        return this.securityReviewDocumentsQuery.data?.data ?? [];
    }

    get pdfDownloadUrl(): SignedUrlResponseDto | null {
        if (!this.socDocument?.id) {
            return null;
        }

        return this.pdfDownloadUrlQuery.data;
    }

    get isPdfDownloadLoading(): boolean {
        return this.pdfDownloadUrlQuery.isLoading;
    }

    get isLoading(): boolean {
        return this.securityReviewDocumentsQuery.isLoading;
    }

    get securityReviewDocuments(): Document[] {
        return transformToDocuments(
            this.securityReviewDocumentsQuery.data?.data ?? [],
        );
    }

    get files(): DocumentFiles[] {
        return transformDocumentsToFiles(
            this.allSecurityReviewDocuments.filter(
                (item) => item.type === 'DOCUMENT',
            ),
        );
    }

    get questionnaires(): QuestionnaireFiles[] {
        return transformDocumentsToQuestionnaires(
            this.allSecurityReviewDocuments.filter(
                (item) => item.type === 'QUESTIONNAIRE',
            ),
        );
    }

    get bridgeLetterDocuments(): VendorSecurityReviewDocumentResponseDto[] {
        return this.allSecurityReviewDocuments.filter(
            ({ type }) => type === 'BRIDGE_LETTER',
        );
    }

    loadSecurityReviewDocuments = (
        options: Parameters<typeof this.securityReviewDocumentsQuery.load>[0],
    ): void => {
        this.securityReviewDocumentsQuery.load(options);
    };

    loadSecurityReviewSOCDocument = (
        options: Parameters<typeof this.securityReviewDocumentsQuery.load>[0],
    ): void => {
        this.securityReviewDocumentsQuery.load(options);
        when(
            () => !this.securityReviewDocumentsQuery.isLoading,
            () => {
                const data: VendorSecurityReviewDocumentResponseDto[] =
                    this.allSecurityReviewDocuments;

                if (isEmpty(data)) {
                    return;
                }

                if (!this.socDocument?.documentId) {
                    return;
                }

                this.loadPdfDownloadUrl(
                    this.vendorId ?? 0,
                    this.socDocument.documentId,
                );
            },
        );
    };

    loadPdfDownloadUrl = (vendorId: number, docId: number): void => {
        if (!docId || !vendorId) {
            return;
        }

        this.pdfDownloadUrlQuery.load({
            path: { id: vendorId, docId },
        });
    };

    uploadSecurityReviewDocument = (
        navigate: NavigateFunction,
        documentType: VendorsControllerUploadVendorDocumentData['body']['type'],
        securityReviewStatus: VendorSecurityReviewRequestDto['securityReviewStatus'],
        securityReviewType: VendorSecurityReviewRequestDto['securityReviewType'],
        linkDocumentType: VendorSecurityReviewDocumentRequestDto['type'],
        file: File,
        vendorId: number | null,
    ): void => {
        if (!vendorId) {
            return;
        }

        this.uploadFileMutation.mutate({
            body: {
                file,
                type: documentType,
            },
            path: { id: vendorId },
        });

        when(
            () => !this.uploadFileMutation.isPending,
            () => {
                const {
                    response: responseUploadFile,
                    hasError: hasErrorUploadFile,
                } = this.uploadFileMutation;

                if (hasErrorUploadFile) {
                    snackbarController.addSnackbar({
                        id: 'upload-security-review-document-error',
                        props: {
                            title: t`Error while uploading the file`,
                            description: t`Please try again later`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                const documents = responseUploadFile?.documents;

                if (documents) {
                    const documentsLength = documents.length;

                    sharedVendorsCurrentSecurityReviewsController.createNewSecurityReview(
                        navigate,
                        securityReviewStatus,
                        securityReviewType,
                        linkDocumentType,
                        documents[documentsLength - 1].id,
                    );
                } else {
                    console.error('Error while uploading the file');
                }
            },
        );
    };

    createReviewDocument = async (
        documentId: number,
        securityReviewId: number,
    ): Promise<void> => {
        if (!documentId || !securityReviewId) {
            return;
        }

        await this.createSecurityReviewDocumentMutation.mutateAsync({
            body: {
                documentId,
                type: 'REVIEW',
            },
            path: { id: securityReviewId },
        });
    };

    uploadFileAndLinkToSecurityReview = (
        file: File,
        securityReviewId: number,
        workspaceId: number,
        securityReviewType: VendorSecurityReviewRequestDto['securityReviewType'],
        linkDocumentType: VendorSecurityReviewDocumentRequestDto['type'],
        onError?: () => void,
    ): void => {
        if (!this.currentVendorId) {
            onError?.();

            return;
        }

        this.uploadFileMutation.mutate({
            body: {
                file,
                type: 'SOC_DOCUMENT',
            },
            path: { id: this.currentVendorId },
        });

        when(
            () => !this.uploadFileMutation.isPending,
            () => {
                const { response, hasError } = this.uploadFileMutation;

                if (hasError) {
                    onError?.();

                    return;
                }

                const documents = response?.documents;

                if (documents && !isEmpty(documents)) {
                    const documentId = documents.find(
                        (doc) => doc.type === 'SOC_DOCUMENT',
                    )?.id;

                    if (!documentId) {
                        onError?.();

                        return;
                    }

                    this.createNewSecurityReviewWithDocuments(
                        documentId,
                        securityReviewId,
                        workspaceId,
                        securityReviewType,
                        linkDocumentType,
                        this.currentVendorId,
                    );
                } else {
                    onError?.();
                }
            },
        );
    };

    createNewSecurityReviewWithDocuments = (
        documentId: number,
        securityReviewId: number,
        workspaceId: number,
        securityReviewType: VendorSecurityReviewRequestDto['securityReviewType'],
        linkDocumentType: VendorSecurityReviewDocumentRequestDto['type'],
        vendorId: number | null,
        navigate?: NavigateFunction,
    ): void => {
        this.createSecurityReviewDocumentMutation.mutate({
            body: {
                documentId,
                type: linkDocumentType,
                isAddedFromLibrary: false,
            },
            path: { id: securityReviewId },
        });
        when(
            () => !this.createSecurityReviewDocumentMutation.isPending,
            () => {
                const { hasError: hasErrorDocumentReview } =
                    this.createSecurityReviewDocumentMutation;

                if (hasErrorDocumentReview) {
                    snackbarController.addSnackbar({
                        id: 'create-security-review-error',
                        props: {
                            title: t`Error while creating the security review SOC document`,
                            description: t`Please try again later`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    sharedVendorsCurrentSecurityReviewsController.activeProcess = false;

                    return;
                }

                if (securityReviewType === 'SOC_REPORT') {
                    this.securityReviewDocumentsQuery.invalidate();
                    if (vendorId && documentId) {
                        this.pdfDownloadUrlQuery.load({
                            path: { id: vendorId, docId: documentId },
                        });
                    }
                    modalController.closeModal(ADD_SOC_REVIEW_MODAL_ID);
                    navigate?.(
                        `workspaces/${workspaceId}/vendors/current/${vendorId}/security-reviews/soc/${securityReviewId}`,
                    );
                } else {
                    modalController.closeModal(UPLOAD_REVIEW_REPORT_MODAL_ID);
                    snackbarController.addSnackbar({
                        id: 'create-security-review-success',
                        props: {
                            title: t`Security review created successfully`,
                            description: t`The security review has been created successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                    sharedVendorsCurrentSecurityReviewsController.loadPaginatedSecurityReviews(
                        VENDORS_DETAILS_TABS_DEFAULT_PARAMS,
                    );
                }

                sharedVendorsCurrentSecurityReviewsController.activeProcess = false;
            },
        );
    };
}

export const sharedVendorsSecurityReviewDocumentsController =
    new VendorsSecurityReviewDocumentsController();
