import { isEmpty } from 'lodash-es';
import { ADD_SOC_REVIEW_MODAL_ID } from '@components/vendors-security-reviews';
import { modalController } from '@controllers/modal';
import { snackbarController } from '@controllers/snackbar';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import {
    vendorsControllerCreateVendorSecurityReviewMutation,
    vendorsControllerGetVendorsSecurityReviewsOptions,
} from '@globals/api-sdk/queries';
import type {
    VendorsControllerUploadVendorDocumentData,
    VendorSecurityReviewDocumentRequestDto,
    VendorSecurityReviewRequestDto,
} from '@globals/api-sdk/types';
import { sharedCurrentUserController } from '@globals/current-user';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { addMonths } from '@helpers/date-time';
import type { NavigateFunction } from '@remix-run/react';
import { sharedVendorsDetailsController } from './vendors-details-controller';
import { sharedVendorsSecurityReviewDocumentsController } from './vendors-security-review-documents-controller';

class VendorsCurrentSecurityReviewsController {
    constructor() {
        makeAutoObservable(this);
    }

    vendorId: number | null = null;
    activeProcess = false;

    paginatedSecurityReviews = new ObservedQuery(
        vendorsControllerGetVendorsSecurityReviewsOptions,
    );
    createVendorSecurityReviewMutation = new ObservedMutation(
        vendorsControllerCreateVendorSecurityReviewMutation,
    );

    setVendorId = (vendorId: number) => {
        this.vendorId = vendorId;
    };

    loadPaginatedSecurityReviews = (params: FetchDataResponseParams) => {
        const { vendorDetails } = sharedVendorsDetailsController;

        if (!vendorDetails?.id) {
            return;
        }

        const { pagination, sorting } = params;
        const { page, pageSize } = pagination;

        const query: Required<
            Parameters<typeof vendorsControllerGetVendorsSecurityReviewsOptions>
        >[0]['query'] = {
            page,
            limit: pageSize,
        };

        if (!isEmpty(sorting)) {
            query.sortDir = sorting[0].desc ? 'DESC' : 'ASC';
        }

        this.paginatedSecurityReviews.load({
            query,
            path: { vendorId: vendorDetails.id },
        });
    };

    get isLoading(): boolean {
        return (
            this.createVendorSecurityReviewMutation.isPending ||
            this.activeProcess
        );
    }

    uploadSecurityReviewDocument = (
        navigate: NavigateFunction,
        documentType: VendorsControllerUploadVendorDocumentData['body']['type'],
        securityReviewStatus: VendorSecurityReviewRequestDto['securityReviewStatus'],
        securityReviewType: VendorSecurityReviewRequestDto['securityReviewType'],
        linkDocumentType: VendorSecurityReviewDocumentRequestDto['type'],
        file: File,
    ) => {
        this.activeProcess = true;
        sharedVendorsSecurityReviewDocumentsController.uploadSecurityReviewDocument(
            navigate,
            documentType,
            securityReviewStatus,
            securityReviewType,
            linkDocumentType,
            file,
            this.vendorId,
        );
    };

    createNewSecurityReview = (
        navigate: NavigateFunction,
        securityReviewStatus: VendorSecurityReviewRequestDto['securityReviewStatus'],
        securityReviewType: VendorSecurityReviewRequestDto['securityReviewType'],
        linkDocumentType?: VendorSecurityReviewDocumentRequestDto['type'],
        documentId?: number,
    ) => {
        if (!this.vendorId) {
            return;
        }
        this.activeProcess = true;
        const { user } = sharedCurrentUserController;
        const { currentWorkspace } = sharedWorkspacesController;
        const reviewDeadline = addMonths(new Date(), 1)
            .toISOString()
            .split('T')[0];

        this.createVendorSecurityReviewMutation.mutate({
            body: {
                requesterUserId: Number(user?.id),
                reviewDeadlineAt: reviewDeadline,
                securityReviewStatus,
                securityReviewType,
            },
            path: { vendorId: this.vendorId },
        });

        when(
            () => !this.createVendorSecurityReviewMutation.isPending,
            () => {
                const {
                    response: responseSecurityReview,
                    hasError: hasErrorSecurityReview,
                } = this.createVendorSecurityReviewMutation;

                if (hasErrorSecurityReview) {
                    snackbarController.addSnackbar({
                        id: 'create-security-review-error',
                        props: {
                            title: t`Error while creating the security review`,
                            description: t`Please try again later`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }
                if (responseSecurityReview?.id && currentWorkspace?.id) {
                    if (securityReviewType === 'SECURITY') {
                        this.activeProcess = false;
                        modalController.closeModal(ADD_SOC_REVIEW_MODAL_ID);
                        navigate(`${responseSecurityReview.id}`);
                    } else if (documentId && linkDocumentType) {
                        this.createNewSecurityReviewWithDocuments(
                            navigate,
                            documentId,
                            responseSecurityReview.id,
                            currentWorkspace.id,
                            securityReviewType,
                            linkDocumentType,
                        );
                    }
                } else {
                    console.error(
                        'Failed to get security review ID from response',
                    );
                }
            },
        );
    };

    createNewSecurityReviewWithDocuments = (
        navigate: NavigateFunction,
        documentId: number,
        securityReviewId: number,
        workspaceId: number,
        securityReviewType: VendorSecurityReviewRequestDto['securityReviewType'],
        linkDocumentType: VendorSecurityReviewDocumentRequestDto['type'],
    ) => {
        sharedVendorsSecurityReviewDocumentsController.createNewSecurityReviewWithDocuments(
            documentId,
            securityReviewId,
            workspaceId,
            securityReviewType,
            linkDocumentType,
            this.vendorId,
            navigate,
        );
    };
}

export const sharedVendorsCurrentSecurityReviewsController =
    new VendorsCurrentSecurityReviewsController();
