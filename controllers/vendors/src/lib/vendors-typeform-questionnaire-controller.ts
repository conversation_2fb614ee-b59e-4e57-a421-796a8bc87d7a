import { questionnaireVendorSecurityTypeformControllerGetVendorSecurityTypeformOptions } from '@globals/api-sdk/queries';
import type { QuestionnaireVendorResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';

class VendorsTypeformQuestionnaireController {
    constructor() {
        makeAutoObservable(this);
    }

    vendorsQuestionnaireQuery = new ObservedQuery(
        questionnaireVendorSecurityTypeformControllerGetVendorSecurityTypeformOptions,
    );

    get title(): string {
        return this.vendorsQuestionnaireQuery.data?.title ?? '';
    }

    get isLoading(): boolean {
        return this.vendorsQuestionnaireQuery.isLoading;
    }

    get categories(): QuestionnaireVendorResponseDto['categories'] {
        return this.vendorsQuestionnaireQuery.data?.categories ?? [];
    }

    get riskLevels(): QuestionnaireVendorResponseDto['riskLevels'] {
        return this.vendorsQuestionnaireQuery.data?.riskLevels ?? [];
    }

    get fields(): QuestionnaireVendorResponseDto['fields'] {
        return this.vendorsQuestionnaireQuery.data?.fields ?? [];
    }

    get areAllAsRequired(): boolean {
        return (
            this.vendorsQuestionnaireQuery.data?.fields.every(
                (field) => field.required,
            ) || false
        );
    }

    loadVendorsQuestionnaire = (questionnaireId: number) => {
        this.vendorsQuestionnaireQuery.load({
            path: { id: questionnaireId },
        });
    };

    /**
     * Initialize questionnaire for viewing/editing - validates permissions and loads data.
     */
    initializeForEdit = (questionnaireId: string | undefined) => {
        // Validate questionnaire ID
        if (!questionnaireId) {
            throw new Error('Questionnaire ID is required');
        }

        // Check permissions - allow both read and manage permissions
        const {
            hasSecurityQuestionnaireReadPermission,
            hasSecurityQuestionnaireManagePermission,
        } = sharedFeatureAccessModel;

        if (
            !hasSecurityQuestionnaireReadPermission &&
            !hasSecurityQuestionnaireManagePermission
        ) {
            throw new Error(t`Missing permission to view questionnaires`, {
                cause: '403',
            });
        }

        // Load questionnaire data
        this.loadVendorsQuestionnaire(Number(questionnaireId));
    };

    /**
     * Setup form data when questionnaire is loaded.
     */
    setupFormData = (
        onDataLoaded: (data: QuestionnaireVendorResponseDto) => void,
    ) => {
        when(
            () =>
                !this.isLoading &&
                this.vendorsQuestionnaireQuery.data !== null &&
                !this.vendorsQuestionnaireQuery.error,
            () => {
                const { data } = this.vendorsQuestionnaireQuery;

                if (data) {
                    onDataLoaded(data);
                }
            },
        );
    };
}

export const sharedVendorsTypeformQuestionnaireController =
    new VendorsTypeformQuestionnaireController();
