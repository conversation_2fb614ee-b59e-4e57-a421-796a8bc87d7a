import { debounce } from 'lodash-es';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { snackbarController } from '@controllers/snackbar';
import {
    questionnaireVendorSecurityTypeformControllerCreateVendorSecurityTypeformMutation,
    questionnaireVendorSecurityTypeformControllerQuestionnaireTitleCheckOptions,
    questionnaireVendorSecurityTypeformControllerUpdateVendorSecurityTypeformMutation,
} from '@globals/api-sdk/queries';
import type { QuestionnaireVendorResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    runInAction,
    toJS,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    type QuestionnaireStatus,
    VendorsQuestionnaireAddModel,
} from '@models/vendors-questionnaire-add';
import { mapApiShortAnswerTypeToFormType } from './helpers/questionnaire-field-mapping.helper';
import { sharedVendorsTypeformQuestionnairesController } from './vendors-typeform-questionnaires-controller';

// Type for question types as they come from API (snake_case)
type ApiQuestionType =
    | 'short_answer'
    | 'long_text'
    | 'multiple_choice'
    | 'checkboxes'
    | 'yes_no'
    | 'date'
    | 'file_upload';

export class VendorsQuestionnaireAddController {
    formModel = new VendorsQuestionnaireAddModel();

    titleValidationQuery = new ObservedQuery(
        questionnaireVendorSecurityTypeformControllerQuestionnaireTitleCheckOptions,
    );

    createQuestionnaireMutation = new ObservedMutation(
        questionnaireVendorSecurityTypeformControllerCreateVendorSecurityTypeformMutation,
        {
            onSuccess: () => {
                snackbarController.addSnackbar({
                    id: 'questionnaire-created',
                    props: {
                        title: t`Questionnaire created successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: 'Close',
                    },
                });

                // Navigate back to questionnaires list
                const { currentWorkspace } = sharedWorkspacesController;

                if (currentWorkspace?.id) {
                    sharedProgrammaticNavigationController.navigateTo(
                        `/workspaces/${currentWorkspace.id}/vendors/questionnaires`,
                    );
                }

                // Refresh questionnaires list
                sharedVendorsTypeformQuestionnairesController.allVendorsQuestionnairesQuery.invalidate();
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'questionnaire-create-error',
                    props: {
                        title: t`Failed to create questionnaire`,
                        description: t`Please try again later`,
                        severity: 'critical',
                        closeButtonAriaLabel: 'Close',
                    },
                });
            },
        },
    );

    updateQuestionnaireMutation = new ObservedMutation(
        questionnaireVendorSecurityTypeformControllerUpdateVendorSecurityTypeformMutation,
        {
            onSuccess: () => {
                snackbarController.addSnackbar({
                    id: 'questionnaire-updated',
                    props: {
                        title: t`Questionnaire updated successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: 'Close',
                    },
                });

                // Navigate back to questionnaires list
                const { currentWorkspace } = sharedWorkspacesController;

                if (currentWorkspace?.id) {
                    sharedProgrammaticNavigationController.navigateTo(
                        `/workspaces/${currentWorkspace.id}/vendors/questionnaires`,
                    );
                }

                // Refresh questionnaires list
                sharedVendorsTypeformQuestionnairesController.allVendorsQuestionnairesQuery.invalidate();
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'questionnaire-update-error',
                    props: {
                        title: t`Failed to update questionnaire`,
                        description: t`Please try again later`,
                        severity: 'critical',
                        closeButtonAriaLabel: 'Close',
                    },
                });
            },
        },
    );

    lastValidatedTitle = '';

    /**
     * Track if we're editing an existing questionnaire.
     */
    editingQuestionnaireId: number | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    get isSubmitting(): boolean {
        return (
            this.createQuestionnaireMutation.isPending ||
            this.updateQuestionnaireMutation.isPending
        );
    }

    get titleValidationError(): string | null {
        // Only show error if the current title matches the last validated title
        if (this.formModel.formData.title.trim() !== this.lastValidatedTitle) {
            return null;
        }

        if (this.titleValidationQuery.error) {
            // On error, allow the title (don't block the user)
            return null;
        }

        if (this.titleValidationQuery.data?.isFound) {
            return t`The questionnaire name you entered is already in use.`;
        }

        return null;
    }

    get canSubmit(): boolean {
        return (
            this.formModel.canSubmit &&
            !this.titleValidationError &&
            !this.titleValidationQuery.isLoading &&
            !this.isSubmitting
        );
    }

    /**
     * Validate questionnaire title.
     */
    handleValidateTitle(title: string): void {
        runInAction(() => {
            if (!title.trim()) {
                this.lastValidatedTitle = '';

                return;
            }

            const trimmedTitle = title.trim();

            this.lastValidatedTitle = trimmedTitle;

            this.titleValidationQuery.load({
                query: { title: trimmedTitle },
            });
        });
    }

    validateTitle = debounce(this.handleValidateTitle.bind(this), 300);

    /**
     * Submit the form with specified status.
     */
    submitForm(status: QuestionnaireStatus = 'ACTIVE'): void {
        runInAction(() => {
            if (!this.canSubmit || this.isSubmitting) {
                return;
            }

            // Convert MobX observables to plain JavaScript objects to avoid reactive context warnings
            const formData = toJS(this.formModel.formData);

            const formDataToSubmit = {
                title: formData.title,
                status,
                categories: formData.categories,
                riskLevels: formData.riskLevels,
                fields: formData.questions.map((question, index: number) => ({
                    ref: question.id || `question-${index}`,
                    title: question.title,
                    type: question.type,
                    required: question.required,
                    shortAnswerType: question.shortAnswerType ?? 'TEXT',
                    choices: question.choices ?? [],
                    followUpQn: question.followUpQn ?? '',
                    allowOtherChoice: question.allowOtherChoice ?? false,
                    includeFollowUpQn: question.includeFollowUpQn,
                    followUpQnTrigger: question.followUpQnTrigger ?? false,
                })),
                markAllAsRequired: formData.markAllAsRequired,
            };

            // Use the appropriate mutation based on whether we're editing or creating
            if (this.editingQuestionnaireId) {
                // Update existing questionnaire
                this.updateQuestionnaireMutation.mutate({
                    path: { id: this.editingQuestionnaireId },
                    body: formDataToSubmit,
                });
            } else {
                // Create new questionnaire
                this.createQuestionnaireMutation.mutate({
                    body: formDataToSubmit,
                });
            }
        });
    }

    /**
     * Submit the form and navigate on success.
     */
    submitFormAndNavigate(
        onSuccess: () => void,
        status: QuestionnaireStatus = 'ACTIVE',
    ): void {
        runInAction(() => {
            if (!this.canSubmit || this.isSubmitting) {
                return;
            }

            // Convert MobX observables to plain JavaScript objects to avoid reactive context warnings
            const formData = toJS(this.formModel.formData);

            const formDataToSubmit = {
                title: formData.title,
                status,
                categories: formData.categories,
                riskLevels: formData.riskLevels,
                fields: formData.questions.map((question, index: number) => ({
                    ref: question.id || `question-${index}`,
                    title: question.title,
                    type: question.type,
                    required: question.required,
                    shortAnswerType: question.shortAnswerType ?? 'TEXT',
                    choices: question.choices ?? [],
                    followUpQn: question.followUpQn ?? '',
                    allowOtherChoice: question.allowOtherChoice ?? false,
                    includeFollowUpQn: question.includeFollowUpQn,
                    followUpQnTrigger: question.followUpQnTrigger ?? false,
                })),
                markAllAsRequired: formData.markAllAsRequired,
            };

            // Use the appropriate mutation based on whether we're editing or creating
            if (this.editingQuestionnaireId) {
                // Update existing questionnaire
                this.updateQuestionnaireMutation.mutate({
                    path: { id: this.editingQuestionnaireId },
                    body: formDataToSubmit,
                });
            } else {
                // Create new questionnaire
                this.createQuestionnaireMutation.mutate({
                    body: formDataToSubmit,
                });
            }

            // Handle success after mutation
            const { response, hasError } = this.createQuestionnaireMutation;

            if (!hasError && response) {
                // Show success snackbar with appropriate message
                const message =
                    status === 'DRAFT'
                        ? t`The vendor questionnaire draft has been successfully saved!`
                        : t`The vendor questionnaire has been successfully saved!`;

                snackbarController.addSnackbar({
                    id: 'questionnaire-create-success',
                    hasTimeout: true,
                    props: {
                        title: message,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                // Reset form after successful creation
                this.formModel.resetForm();

                // Invalidate questionnaires list to refresh the table
                sharedVendorsTypeformQuestionnairesController.allVendorsQuestionnairesQuery.invalidate();

                // Execute navigation callback
                onSuccess();
            }
        });
    }

    /**
     * Load existing questionnaire data into the form.
     */
    loadQuestionnaireData(data: QuestionnaireVendorResponseDto): void {
        runInAction(() => {
            // Set the editing questionnaire ID to indicate we're in edit mode
            this.editingQuestionnaireId = data.id;

            const mappedQuestions = data.fields.map((field, index) => ({
                id: field.ref || `question-${index}`,
                title: field.title,
                type: field.type as ApiQuestionType, // API type (snake_case)
                required: field.required,
                shortAnswerType: mapApiShortAnswerTypeToFormType(
                    field.shortAnswerType,
                ),
                choices: field.choices,
                followUpQn: field.followUpQn,
                allowOtherChoice: field.allowOtherChoice,
                includeFollowUpQn: field.includeFollowUpQn,
                followUpQnTrigger: field.followUpQnTrigger,
            }));

            this.formModel.formData = {
                title: data.title,
                categories: data.categories,
                riskLevels: data.riskLevels,
                questions:
                    mappedQuestions as unknown as typeof this.formModel.formData.questions, // DTO types are outdated
                markAllAsRequired: false, // Default value since it's not in the response
            };

            this.lastValidatedTitle = data.title;
        });
    }

    /**
     * Reset the controller for creating a new questionnaire.
     */
    resetForNewQuestionnaire(): void {
        runInAction(() => {
            this.editingQuestionnaireId = null;
            this.formModel.resetForm();
            this.lastValidatedTitle = '';
        });
    }
}

export const sharedVendorsQuestionnaireAddController =
    new VendorsQuestionnaireAddController();
