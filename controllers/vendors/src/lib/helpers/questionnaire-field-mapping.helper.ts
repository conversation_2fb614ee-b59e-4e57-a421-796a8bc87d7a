/**
 * Map API response field types to form field types.
 * Note: DTO types are outdated, but API accepts all types. Using proper types with assertion when needed.
 */
export const mapApiTypeToFormType = (
    apiType: string,
):
    | 'SHORT_ANSWER'
    | 'LONG_ANSWER'
    | 'MULTIPLE_CHOICE'
    | 'CHECKBOXES'
    | 'YES_NO'
    | 'DATE'
    | 'FILE_UPLOAD' => {
    switch (apiType) {
        case 'short_answer': {
            return 'SHORT_ANSWER';
        }
        case 'long_text': {
            return 'LONG_ANSWER';
        }
        case 'multiple_choice': {
            return 'MULTIPLE_CHOICE';
        }
        case 'checkboxes': {
            return 'CHECKBOXES';
        }
        case 'yes_no': {
            return 'YES_NO';
        }
        case 'date': {
            return 'DATE';
        }
        case 'file_upload': {
            return 'FILE_UPLOAD';
        }
        default: {
            return 'LONG_ANSWER';
        }
    }
};

/**
 * Map API response short answer types to form short answer types.
 */
export const mapApiShortAnswerTypeToFormType = (
    apiType: string,
): 'TEXT' | 'EMAIL' | 'URL' | 'PHONE' => {
    switch (apiType) {
        case 'short_text': {
            return 'TEXT';
        }
        case 'email': {
            return 'EMAIL';
        }
        case 'website': {
            return 'URL';
        }
        case 'phone_number': {
            return 'PHONE';
        }
        default: {
            return 'TEXT';
        }
    }
};
