import { isNil } from 'lodash-es';
import {
    vendorTrustCenterControllerGetVendorTrustCenterCertificationsOptions,
    vendorTrustCenterControllerGetVendorTrustCenterDocumentsOptions,
    vendorTrustCenterControllerGetVendorTrustCenterItemsByCategoryOptions,
    vendorTrustCenterControllerGetVendorTrustCenterOptions,
    vendorTrustCenterControllerGetVendorTrustCenterOverviewOptions,
    vendorTrustCenterControllerGetVendorTrustCenterSubProcessorsOptions,
} from '@globals/api-sdk/queries';
import type {
    VendorTrustCenterCertificationResponseDto,
    VendorTrustCenterDocumentResponseDto,
    VendorTrustCenterItemsByCategoryResponseDto,
    VendorTrustCenterOverviewResponseDto,
    VendorTrustCenterResponseDto,
    VendorTrustCenterSubProcessorItemResponseDto,
} from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import {
    makeAutoObservable,
    ObservedQuery,
    reaction,
    when,
} from '@globals/mobx';
import { generateLogoDevUrl } from '@helpers/formatters';
import { sharedVendorsDetailsController } from './vendors-details-controller';

class VendorTrustCenterController {
    constructor() {
        makeAutoObservable(this);

        reaction(
            () => sharedVendorsDetailsController.vendorDetails?.url,
            () => {
                this.#vendorTrustCenterQuery.invalidate();
            },
        );
    }

    readonly #vendorTrustCenterQuery = new ObservedQuery(
        vendorTrustCenterControllerGetVendorTrustCenterOptions,
    );

    readonly #vendorTrustCenterOverviewQuery = new ObservedQuery(
        vendorTrustCenterControllerGetVendorTrustCenterOverviewOptions,
    );

    readonly #vendorTrustCenterCertificationsQuery = new ObservedQuery(
        vendorTrustCenterControllerGetVendorTrustCenterCertificationsOptions,
    );

    readonly #vendorTrustCenterDocumentsQuery = new ObservedQuery(
        vendorTrustCenterControllerGetVendorTrustCenterDocumentsOptions,
    );

    readonly #vendorTrustCenterSubprocessorsQuery = new ObservedQuery(
        vendorTrustCenterControllerGetVendorTrustCenterSubProcessorsOptions,
    );

    readonly #vendorTrustCenterItemsQuery = new ObservedQuery(
        vendorTrustCenterControllerGetVendorTrustCenterItemsByCategoryOptions,
    );

    get info(): VendorTrustCenterResponseDto | null {
        return this.#vendorTrustCenterQuery.data ?? null;
    }

    get overview(): VendorTrustCenterOverviewResponseDto['overview'] | null {
        return this.#vendorTrustCenterOverviewQuery.data?.overview ?? null;
    }

    get certifications(): VendorTrustCenterCertificationResponseDto[] {
        return (
            this.#vendorTrustCenterCertificationsQuery.data?.compliances ?? []
        );
    }

    get documents(): VendorTrustCenterDocumentResponseDto[] {
        const responseData = this.#vendorTrustCenterDocumentsQuery.data;

        if (!responseData?.documents) {
            return [];
        }

        return responseData.documents.map((document) => ({
            id: document.id,
            name: document.name,
            isViewOnly: document.isViewOnly,
            isAccessRequired: false,
        }));
    }

    get subprocessors(): VendorTrustCenterSubProcessorItemResponseDto[] {
        const responseData = this.#vendorTrustCenterSubprocessorsQuery.data;

        if (!responseData?.subProcessors) {
            return [];
        }

        return responseData.subProcessors.map((subprocessor) => ({
            ...subprocessor,
            company: {
                ...subprocessor.company,
                logo: subprocessor.company?.website
                    ? generateLogoDevUrl(subprocessor.company.website)
                    : '',
            },
        }));
    }

    get itemsByCategory(): VendorTrustCenterItemsByCategoryResponseDto[] {
        return this.#vendorTrustCenterItemsQuery.data?.categories ?? [];
    }

    get isInfoLoading(): boolean {
        return this.#vendorTrustCenterQuery.isLoading;
    }

    get isOverviewLoading(): boolean {
        return this.#vendorTrustCenterOverviewQuery.isLoading;
    }

    get isCertificationsLoading(): boolean {
        return this.#vendorTrustCenterCertificationsQuery.isLoading;
    }

    get isDocumentsLoading(): boolean {
        return this.#vendorTrustCenterDocumentsQuery.isLoading;
    }

    get isSubprocessorsLoading(): boolean {
        return this.#vendorTrustCenterSubprocessorsQuery.isLoading;
    }

    get isItemsLoading(): boolean {
        return this.#vendorTrustCenterItemsQuery.isLoading;
    }

    get hasVendorTrustCenterAccess(): boolean {
        return (
            sharedFeatureAccessModel.isReleaseVrmPublicTrustCenterViewCollection &&
            sharedFeatureAccessModel.isVendorRiskManagementProEnabled
        );
    }

    get isVendorTrustCenterEnabled(): boolean {
        return !isNil(this.info?.organizationId);
    }

    loadInfo = (vendorId: number) => {
        when(
            () => this.hasVendorTrustCenterAccess,
            () => {
                this.#vendorTrustCenterQuery.load({
                    path: { id: vendorId },
                });
            },
        );
    };

    loadOverview = (vendorId: number) => {
        when(
            () => this.isVendorTrustCenterEnabled,
            () => {
                this.#vendorTrustCenterOverviewQuery.load({
                    path: { id: vendorId },
                });
            },
        );
    };

    loadCertifications = (vendorId: number) => {
        when(
            () => this.isVendorTrustCenterEnabled,
            () => {
                this.#vendorTrustCenterCertificationsQuery.load({
                    path: { id: vendorId },
                });
            },
        );
    };

    loadDocuments = (vendorId: number) => {
        when(
            () => this.isVendorTrustCenterEnabled,
            () => {
                this.#vendorTrustCenterDocumentsQuery.load({
                    path: { id: vendorId },
                });
            },
        );
    };

    loadSubprocessors = (vendorId: number) => {
        when(
            () => this.isVendorTrustCenterEnabled,
            () => {
                this.#vendorTrustCenterSubprocessorsQuery.load({
                    path: { id: vendorId },
                });
            },
        );
    };

    loadItems = (vendorId: number) => {
        when(
            () => this.isVendorTrustCenterEnabled,
            () => {
                this.#vendorTrustCenterItemsQuery.load({
                    path: { id: vendorId },
                });
            },
        );
    };
}

export const sharedVendorTrustCenterController =
    new VendorTrustCenterController();
