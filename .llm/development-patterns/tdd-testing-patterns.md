# TDD Testing Patterns

## 🚨 CRITICAL: Test-Driven Development (TDD) Guidelines

This document provides patterns for writing tests in TDD style, particularly when implementation files don't exist yet.

## TDD Red Phase - Writing Tests Before Implementation

### Basic Principles

1. **Start Simple**: Tests should verify behavior, not implementation details
2. **Handle Non-Existent Files**: Gracefully handle imports that don't exist yet
3. **Follow Conventions**: Match the codebase's testing patterns exactly
4. **Fix All Errors**: Never submit tests with ESLint or TypeScript errors

### Handling Non-Existent Imports

When writing tests before implementation (TDD red phase), imports will fail. Handle this properly:

```typescript
// ❌ WRONG - This will throw an unhandled error
import { MyComponent } from './my-component';

// ❌ WRONG - Using async imports incorrectly
const { MyComponent } = await import('./my-component'); // await outside async

// ✅ CORRECT - For components that don't exist yet
describe('MyComponent', () => {
  it('should export MyComponent', async () => {
    try {
      const module = await import('./my-component');
      expect(module.MyComponent).toBeDefined();
    } catch (error) {
      // This is expected in TDD red phase
      expect(error).toBeDefined();
    }
  });
});

// ✅ CORRECT - Alternative approach with dynamic imports
describe('MyComponent', () => {
  let MyComponent: any;

  beforeEach(async () => {
    try {
      const module = await import('./my-component');
      MyComponent = module.MyComponent;
    } catch {
      // Expected in red phase
      MyComponent = undefined;
    }
  });

  it('should be defined', () => {
    // This will fail until implementation exists
    expect(MyComponent).toBeDefined();
  });
});
```

### Test File Naming

- Use `.spec.ts` or `.spec.tsx` (NOT `.test.ts`)
- Match the implementation file name: `my-component.tsx` → `my-component.spec.tsx`

### Test Structure

```typescript
// ✅ CORRECT - Proper test structure
describe('feature description', () => { // lowercase
  // blank line after describe
  it('should do something specific', () => {
    // test implementation
  });

  it('should handle edge case', () => {
    // test implementation
  });
});

// ❌ WRONG - Common mistakes
describe('Feature Description', () => { // uppercase - WRONG
  it('Should Do Something', () => { // uppercase - WRONG
    // missing blank lines
  });
});
```

### Component Testing Pattern

```typescript
import { render, screen } from '@testing-library/react';
import { describe, it, expect, beforeEach } from 'vitest';

describe('my component', () => {
  let MyComponent: any;

  beforeEach(async () => {
    try {
      const module = await import('./my-component');
      MyComponent = module.MyComponent;
    } catch {
      MyComponent = null;
    }
  });

  it('should render when component exists', () => {
    if (!MyComponent) {
      expect(MyComponent).toBeNull(); // Expected in red phase
      return;
    }

    render(<MyComponent />);
    expect(screen.getByTestId('my-component')).toBeInTheDocument();
  });
});
```

### Controller Testing Pattern

```typescript
import { describe, it, expect, beforeEach } from 'vitest';

describe('my controller', () => {
  let MyController: any;

  beforeEach(async () => {
    try {
      const module = await import('./my.controller');
      MyController = module.MyController;
    } catch {
      MyController = null;
    }
  });

  it('should have load method', () => {
    if (!MyController) {
      expect(MyController).toBeNull(); // Expected in red phase
      return;
    }

    const instance = new MyController();
    expect(instance.load).toBeDefined();
  });
});
```

### Model Testing Pattern

```typescript
import { describe, it, expect, beforeEach } from 'vitest';

describe('my model', () => {
  let MyModel: any;

  beforeEach(async () => {
    try {
      const module = await import('./my.model');
      MyModel = module.MyModel;
    } catch {
      MyModel = null;
    }
  });

  it('should have required properties', () => {
    if (!MyModel) {
      expect(MyModel).toBeNull(); // Expected in red phase
      return;
    }

    const instance = new MyModel();
    expect(instance.someProperty).toBeDefined();
  });
});
```

## Common TDD Mistakes to Avoid

### 1. Overly Complex Tests

```typescript
// ❌ WRONG - Too complex for initial TDD
it('should handle complex business logic with mocks', async () => {
  const mockApi = vi.fn().mockResolvedValue({ data: [...] });
  const mockRouter = { navigate: vi.fn() };
  // ... 20 more lines of setup
});

// ✅ CORRECT - Start simple
it('should initialize with default values', () => {
  const instance = new MyModel();
  expect(instance.isLoading).toBe(false);
});
```

### 2. Testing Implementation Details

```typescript
// ❌ WRONG - Testing internals
it('should call internal method X when Y happens', () => {
  // Testing private methods or internal state
});

// ✅ CORRECT - Test behavior
it('should display error message when save fails', () => {
  // Test what the user experiences
});
```

### 3. Incorrect Import Paths

```typescript
// ❌ WRONG - Guessing at module structure
import { sharedController } from '@controllers/my-feature';

// ✅ CORRECT - Use exact paths
import { sharedMyFeatureController } from '@controllers/my-feature/my-feature.controller';
```

### 4. Not Following Linting Rules

Always run and fix:
- `pnpm run lint` - Fix all ESLint errors
- `pnpm run typecheck` - Fix all TypeScript errors

Common linting issues:
- Missing blank lines after describe blocks
- Uppercase test descriptions
- Unused imports
- Type safety violations

### 5. Async/Await Mistakes

```typescript
// ❌ WRONG - await in non-async context
describe('test', () => {
  const module = await import('./file'); // ERROR
});

// ✅ CORRECT - Proper async handling
describe('test', () => {
  let module: any;

  beforeEach(async () => {
    module = await import('./file').catch(() => null);
  });
});
```

## TDD Workflow

1. **Write Failing Test**: Start with the simplest test that verifies the file/export exists
2. **Run Test**: Verify it fails for the right reason (file not found, not other errors)
3. **Fix Errors**: Ensure no ESLint or TypeScript errors
4. **Implement**: Create the minimal code to make the test pass
5. **Refactor**: Clean up while keeping tests green

## Testing Checklist

Before submitting any test file:

- [ ] All tests use `.spec.ts` or `.spec.tsx` extension
- [ ] No ESLint errors (`pnpm run lint`)
- [ ] No TypeScript errors (`pnpm run typecheck`)
- [ ] Proper error handling for non-existent imports
- [ ] Following existing test patterns in the codebase
- [ ] Test descriptions are lowercase
- [ ] Proper blank lines after describe blocks
- [ ] No unused imports or variables
- [ ] Tests focus on behavior, not implementation

## Example: Complete TDD Test File

```typescript
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen } from '@testing-library/react';

describe('policy builder edit component', () => {
  let PolicyBuilderEditComponent: any;

  beforeEach(async () => {
    try {
      const module = await import('./policy-builder-edit.component');
      PolicyBuilderEditComponent = module.PolicyBuilderEditComponent;
    } catch {
      PolicyBuilderEditComponent = null;
    }
  });

  it('should export PolicyBuilderEditComponent', () => {
    // This will fail until implementation exists
    expect(PolicyBuilderEditComponent).toBeDefined();
  });

  it('should render edit form when component exists', () => {
    if (!PolicyBuilderEditComponent) {
      expect(PolicyBuilderEditComponent).toBeNull();
      return;
    }

    render(<PolicyBuilderEditComponent />);
    expect(screen.getByTestId('policy-builder-edit-form')).toBeInTheDocument();
  });

  it('should handle save action when implemented', () => {
    if (!PolicyBuilderEditComponent) {
      expect(PolicyBuilderEditComponent).toBeNull();
      return;
    }

    // Add specific behavior tests here
  });
});
```

## References

- [Testing Library Documentation](https://testing-library.com/)
- [Vitest Documentation](https://vitest.dev/)
- See existing tests in the codebase for patterns