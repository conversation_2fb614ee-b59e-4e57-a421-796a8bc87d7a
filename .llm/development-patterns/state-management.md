# State Management

> **OFFICIAL STANDARD**: Use MobX controllers and models for state management instead of React's internal state (`useState`, `useReducer`) and context (`useContext`) for all but the **simplest** UI state.

> **CRITICAL**: For mutation operations, follow the official [MobX Mutation Patterns](./mobx-mutations.md) guide which establishes the required ObservedMutation + onSuccess + when() pattern.

═══════════════════════════════════════════════════════════════════════
## 🚨 BEFORE WRITING ANY CODE - MANDATORY CHECKLIST 🚨

**THIS SECTION WILL PREVENT 90% OF MOBX ERRORS**

□ **For ALL onClick/onChange handlers with MobX state changes:**
  ```tsx
  onClick={() => runInAction(() => {
    controller.someProperty = newValue;
  })}
  ```
  
□ **For controller methods that mutate state:**
  ```tsx
  updateStatus(status: string) {
    runInAction(() => {
      this.model.status = status;
    });
  }
  ```

□ **For models:** NO actions, only computed properties and data

□ **For business logic:** Put in controllers, NOT components

□ **For API calls:** Use ObservedQuery/ObservedMutation pattern

═══════════════════════════════════════════════════════════════════════

## Overview

State management is crucial for building scalable and maintainable applications. This guide establishes the official patterns for state management in the Multiverse project, emphasizing MobX controllers and models over React's built-in state solutions.

## Critical MobX Rules

> **CRITICAL**: When modifying MobX state from React event handlers (onClick, onChange, etc.), you MUST wrap the state changes in `runInAction`:

```typescript
// ❌ INCORRECT - Direct state modification in onClick
onClick: () => {
    sharedController.setIsEditMode(true);
}

// ✅ CORRECT - Wrapped in runInAction
onClick: () => {
    runInAction(() => {
        sharedController.setIsEditMode(true);
    });
}
```

This ensures MobX properly tracks the state changes and triggers re-renders correctly.

## ❌ COMMON MISTAKES THAT WILL FAIL CODE REVIEW

### Mistake #1: Missing runInAction in React Event Handlers
```typescript
// ❌ WRONG - THIS WILL BREAK
<Button onClick={() => controller.updateStatus('active')}>
  Activate
</Button>

// ✅ CORRECT
<Button onClick={() => runInAction(() => {
  controller.status = 'active';
})}>
  Activate
</Button>

// ✅ ALSO CORRECT - If controller method uses runInAction internally
<Button onClick={() => controller.updateStatus('active')}>
  Activate
</Button>
```

### Mistake #2: Direct State Mutation Without runInAction
```typescript
// ❌ WRONG - THIS WILL BREAK
class MyController {
  updateUser(name: string) {
    this.user.name = name; // Direct mutation without runInAction
  }
}

// ✅ CORRECT
class MyController {
  updateUser(name: string) {
    runInAction(() => {
      this.user.name = name;
    });
  }
}
```

### Mistake #3: Actions in Models
```typescript
// ❌ WRONG - Models should NEVER have actions
class UserModel {
  name: string;
  
  updateName(newName: string) {
    this.name = newName;
  }
}

// ✅ CORRECT - Only computed properties in models
class UserModel {
  name: string;
  
  get displayName() {
    return this.name || 'Anonymous';
  }
}
```

## Prefer MobX Controllers and Models Over React State and Context

> **OFFICIAL STANDARD**: Use MobX controllers and models for state management instead of React's internal state (`useState`, `useReducer`) and context (`useContext`) for all but the **simplest** UI state.

### Justification

- **Consistent Pattern**: Provides a consistent state management pattern across the application
- **Separation of Concerns**: Enables better separation (business logic in controllers, data in models)
- **Observability**: Makes state more observable and debuggable with MobX DevTools
- **State Sharing**: Allows for easier sharing of state between components
- **MVC Architecture**: Follows our established MVC architecture pattern
- **Performance**: Reduces prop drilling and unnecessary re-renders
- **Context Issues**: Avoids performance issues that occur with Context when state changes frequently
- **Testing**: Simplifies testing by making dependencies explicit rather than implicit through context

### ✅ CORRECT: MobX Controllers for Business Logic

```typescript
// ✅ CORRECT - Controller handles business logic and state
// file: policies.controller.ts
import { makeAutoObservable } from '@globals/mobx';
import { ObservedMutation } from '@globals/mobx';
import { when } from '@globals/mobx';

class PoliciesController {
    policies: PolicyModel[] = [];
    selectedPolicy: PolicyModel | null = null;
    isLoading = false;
    filters = {
        status: 'all' as PolicyStatus | 'all',
        search: '',
    };

    constructor() {
        makeAutoObservable(this);
    }

    // Computed values
    get filteredPolicies() {
        return this.policies.filter(policy => {
            const matchesStatus = this.filters.status === 'all' ||
                                policy.status === this.filters.status;
            const matchesSearch = policy.name.toLowerCase()
                                .includes(this.filters.search.toLowerCase());
            return matchesStatus && matchesSearch;
        });
    }

    get hasSelectedPolicy() {
        return this.selectedPolicy !== null;
    }

    // Actions
    setFilters(filters: Partial<typeof this.filters>) {
        Object.assign(this.filters, filters);
    }

    selectPolicy(policy: PolicyModel) {
        this.selectedPolicy = policy;
    }

    clearSelection() {
        this.selectedPolicy = null;
    }

    // ✅ CORRECT - ObservedMutation with official pattern
    loadPoliciesMutation = new ObservedMutation(
        async () => {
            const response = await fetch('/api/policies');
            return response.json();
        },
        {
            onSuccess: (data) => {
                this.policies = data.map(item => new PolicyModel(item));
            },
        }
    );

    createPolicyMutation = new ObservedMutation(
        async (policyData: CreatePolicyRequest) => {
            const response = await fetch('/api/policies', {
                method: 'POST',
                body: JSON.stringify(policyData),
            });
            return response.json();
        },
        {
            onSuccess: (newPolicy) => {
                this.policies.push(new PolicyModel(newPolicy));
                this.selectedPolicy = this.policies[this.policies.length - 1];
            },
        }
    );

    // ✅ CORRECT - Computed properties for reactive state
    get isLoadingPolicies(): boolean {
        return this.loadPoliciesMutation.isPending;
    }

    get isCreatingPolicy(): boolean {
        return this.createPolicyMutation.isPending;
    }

    get hasLoadError(): boolean {
        return this.loadPoliciesMutation.hasError;
    }

    // ✅ CORRECT - Action with when() pattern (see MobX Mutation Patterns guide)
    createPolicy = (policyData: CreatePolicyRequest, onSuccess?: () => void): void => {
        if (!policyData.title || !policyData.description) {
            return;
        }

        this.createPolicyMutation.mutate(policyData);

        when(
            () => !this.isCreatingPolicy,
            () => {
                if (this.createPolicyMutation.hasError) {
                    // Error handling with snackbar (see MobX Mutation Patterns)
                    return;
                }
                // Success handling and optional callback
                onSuccess?.();
            }
        );
    };

    // Simple data loading
    loadPolicies() {
        this.loadPoliciesMutation.mutate();
    }
}

export const sharedPoliciesController = new PoliciesController();
```

### ✅ CORRECT: Models for Data Structures

```typescript
// ✅ CORRECT - Model represents data structure only
// file: policy.model.ts
export class PolicyModel {
    id: string;
    name: string;
    description: string;
    status: PolicyStatus;
    createdAt: Date;
    updatedAt: Date;

    constructor(data: PolicyData) {
        this.id = data.id;
        this.name = data.name;
        this.description = data.description;
        this.status = data.status;
        this.createdAt = new Date(data.createdAt);
        this.updatedAt = new Date(data.updatedAt);
    }

    // ✅ CORRECT - Computed properties for data formatting
    get displayName(): string {
        return this.name || 'Untitled Policy';
    }

    get isActive(): boolean {
        return this.status === 'active';
    }

    get formattedCreatedDate(): string {
        return this.createdAt.toLocaleDateString();
    }

    get statusLabel(): string {
        const statusLabels = {
            active: 'Active',
            draft: 'Draft',
            archived: 'Archived',
        } as const satisfies Record<PolicyStatus, string>;

        return statusLabels[this.status];
    }

    // ✅ CORRECT - Data transformation methods
    toJSON(): PolicyData {
        return {
            id: this.id,
            name: this.name,
            description: this.description,
            status: this.status,
            createdAt: this.createdAt.toISOString(),
            updatedAt: this.updatedAt.toISOString(),
        };
    }

    // ❌ AVOID - No actions in models
    // save() { ... }  // This belongs in controller
    // delete() { ... } // This belongs in controller
}
```

### ✅ CORRECT: Component Integration

```typescript
// ✅ CORRECT - Component uses controller for state
// file: policies-view.component.tsx
import { observer } from '@globals/mobx';
import { sharedPoliciesController } from '@controllers/policies';

export const PoliciesView = observer(() => {
    const {
        filteredPolicies,
        selectedPolicy,
        filters,
        loadPoliciesMutation,
        createPolicyMutation
    } = sharedPoliciesController;

    useEffect(() => {
        sharedPoliciesController.loadPolicies();
    }, []);

    const handleCreatePolicy = (policyData: CreatePolicyRequest) => {
        createPolicyMutation.mutate(policyData);
    };

    const handleFilterChange = (newFilters: Partial<typeof filters>) => {
        sharedPoliciesController.setFilters(newFilters);
    };

    return (
        <div data-id="policies-view">
            <PolicyFilters
                filters={filters}
                onChange={handleFilterChange}
            />

            <PolicyList
                policies={filteredPolicies}
                selectedPolicy={selectedPolicy}
                onSelect={sharedPoliciesController.selectPolicy}
                isLoading={loadPoliciesMutation.isLoading}
            />

            <PolicyForm
                onSubmit={handleCreatePolicy}
                isSubmitting={createPolicyMutation.isLoading}
            />
        </div>
    );
});
```

### ❌ AVOID: React State for Business Logic

```typescript
// ❌ AVOID - Using React state for business logic
export const PoliciesView = () => {
    const [policies, setPolicies] = useState<PolicyData[]>([]);
    const [selectedPolicy, setSelectedPolicy] = useState<PolicyData | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [filters, setFilters] = useState({ status: 'all', search: '' });

    // ❌ Business logic in component
    const filteredPolicies = useMemo(() => {
        return policies.filter(policy => {
            const matchesStatus = filters.status === 'all' || policy.status === filters.status;
            const matchesSearch = policy.name.toLowerCase().includes(filters.search.toLowerCase());
            return matchesStatus && matchesSearch;
        });
    }, [policies, filters]);

    // ❌ API calls in component
    useEffect(() => {
        const loadPolicies = async () => {
            setIsLoading(true);
            try {
                const response = await fetch('/api/policies');
                const data = await response.json();
                setPolicies(data);
            } catch (error) {
                console.error('Failed to load policies:', error);
            } finally {
                setIsLoading(false);
            }
        };

        loadPolicies();
    }, []);

    // ❌ More business logic in component
    const handleCreatePolicy = async (policyData: CreatePolicyRequest) => {
        try {
            const response = await fetch('/api/policies', {
                method: 'POST',
                body: JSON.stringify(policyData),
            });
            const newPolicy = await response.json();
            setPolicies(prev => [...prev, newPolicy]);
        } catch (error) {
            console.error('Failed to create policy:', error);
        }
    };

    return (
        <div>
            {/* Component JSX */}
        </div>
    );
};
```

### ❌ AVOID: React Context for Business State

```typescript
// ❌ AVOID - Using Context for business state
const PoliciesContext = createContext<{
    policies: PolicyData[];
    selectedPolicy: PolicyData | null;
    loadPolicies: () => Promise<void>;
    createPolicy: (data: CreatePolicyRequest) => Promise<void>;
} | null>(null);

export const PoliciesProvider = ({ children }: { children: React.ReactNode }) => {
    const [policies, setPolicies] = useState<PolicyData[]>([]);
    const [selectedPolicy, setSelectedPolicy] = useState<PolicyData | null>(null);

    // ❌ Business logic in provider
    const loadPolicies = async () => {
        const response = await fetch('/api/policies');
        const data = await response.json();
        setPolicies(data);
    };

    const createPolicy = async (policyData: CreatePolicyRequest) => {
        const response = await fetch('/api/policies', {
            method: 'POST',
            body: JSON.stringify(policyData),
        });
        const newPolicy = await response.json();
        setPolicies(prev => [...prev, newPolicy]);
    };

    return (
        <PoliciesContext.Provider value={{
            policies,
            selectedPolicy,
            loadPolicies,
            createPolicy,
        }}>
            {children}
        </PoliciesContext.Provider>
    );
};

// ❌ AVOID - Hook for accessing context
export const usePolicies = () => {
    const context = useContext(PoliciesContext);
    if (!context) {
        throw new Error('usePolicies must be used within PoliciesProvider');
    }
    return context;
};
```

## When to Use React State or Context

> **Note**: There are legitimate use cases for React Context in our codebase, particularly for UI-specific concerns like forms, themes, or localization. The key is to avoid using Context for business logic and data that should be managed by controllers and models.

### ✅ CORRECT: React State for Simple UI State

```typescript
// ✅ CORRECT - React state for simple UI interactions
export const PolicyCard = ({ policy }: { policy: PolicyModel }) => {
    const [isExpanded, setIsExpanded] = useState(false);
    const [showTooltip, setShowTooltip] = useState(false);

    return (
        <Card data-id="policy-card">
            <div
                onMouseEnter={() => setShowTooltip(true)}
                onMouseLeave={() => setShowTooltip(false)}
            >
                <Text>{policy.displayName}</Text>
                {showTooltip && <Tooltip>Policy details</Tooltip>}
            </div>

            <Button
                onClick={() => setIsExpanded(!isExpanded)}
                data-id="expand-policy-button"
            >
                {isExpanded ? 'Collapse' : 'Expand'}
            </Button>

            {isExpanded && (
                <div>
                    <Text>{policy.description}</Text>
                </div>
            )}
        </Card>
    );
};
```

### ✅ CORRECT: React Context for UI Configuration

```typescript
// ✅ CORRECT - Context for theme/configuration that rarely changes
const ThemeContext = createContext<{
    theme: 'light' | 'dark';
    toggleTheme: () => void;
}>({
    theme: 'light',
    toggleTheme: () => {},
});

export const ThemeProvider = ({ children }: { children: React.ReactNode }) => {
    const [theme, setTheme] = useState<'light' | 'dark'>('light');

    const toggleTheme = () => {
        setTheme(prev => prev === 'light' ? 'dark' : 'light');
    };

    return (
        <ThemeContext.Provider value={{ theme, toggleTheme }}>
            {children}
        </ThemeContext.Provider>
    );
};

// ✅ CORRECT - Use @ui/forms for form state management
// See: Form System Guide (./form-system.md) for complete patterns
import { Form, type FormSchema } from '@ui/forms';
import { z } from 'zod';

const POLICY_FORM_SCHEMA = {
    name: { type: 'text', label: 'Policy Name' },
    description: { type: 'textarea', label: 'Description' },
    category: {
        type: 'select',
        label: 'Category',
        options: [
            { id: 'security', label: 'Security', value: 'security' },
            { id: 'compliance', label: 'Compliance', value: 'compliance' },
        ],
    },
} satisfies FormSchema;

export const PolicyFormComponent = () => {
    const handleSubmit = (values: PolicyFormData) => {
        // Handle form submission with MobX controller
        sharedPoliciesController.createPolicy(values);
    };

    return (
        <Form
            formId="policy-form"
            schema={POLICY_FORM_SCHEMA}
            onSubmit={handleSubmit}
            data-id="policy-form"
        />
    );
};
```

## Controller and Model Responsibilities

### Models: Data Structures Only

> **CRITICAL**: Models should just be adapters and data formatting. **No actions in models**.

Models represent business entities and should only contain:
- **Data Properties**: The actual data fields
- **Computed Properties**: Derived values from the data
- **Data Transformation**: Methods to convert data formats
- **Validation Helpers**: Pure functions that validate data

```typescript
// ✅ CORRECT - Model with proper responsibilities
export class UserModel {
    id: string;
    name: string;
    email: string;
    role: UserRole;
    lastLoginAt: Date | null;

    constructor(data: UserData) {
        this.id = data.id;
        this.name = data.name;
        this.email = data.email;
        this.role = data.role;
        this.lastLoginAt = data.lastLoginAt ? new Date(data.lastLoginAt) : null;
    }

    // ✅ CORRECT - Computed properties
    get displayName(): string {
        return this.name || this.email;
    }

    get isAdmin(): boolean {
        return this.role === 'admin';
    }

    get hasLoggedIn(): boolean {
        return this.lastLoginAt !== null;
    }

    get formattedLastLogin(): string {
        if (!this.lastLoginAt) return 'Never';
        return this.lastLoginAt.toLocaleDateString();
    }

    // ✅ CORRECT - Data transformation
    toJSON(): UserData {
        return {
            id: this.id,
            name: this.name,
            email: this.email,
            role: this.role,
            lastLoginAt: this.lastLoginAt?.toISOString() || null,
        };
    }

    // ✅ CORRECT - Validation helpers (pure functions)
    isValidEmail(): boolean {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.email);
    }

    // ❌ AVOID - No actions in models
    // async save() { ... }        // Belongs in controller
    // async delete() { ... }      // Belongs in controller
    // updateRole(role) { ... }    // Belongs in controller
}
```

### Controllers: Business Logic and State Management

Controllers handle all business logic, API calls, and state transformations:
- **State Management**: Observable properties and computed values
- **Business Logic**: Complex operations and workflows
- **API Integration**: Data fetching and mutations
- **State Coordination**: Managing relationships between different data

```typescript
// ✅ CORRECT - Controller with proper responsibilities
class UserManagementController {
    users: UserModel[] = [];
    selectedUser: UserModel | null = null;
    filters = {
        role: 'all' as UserRole | 'all',
        search: '',
        showInactive: false,
    };

    constructor() {
        makeAutoObservable(this);
    }

    // ✅ CORRECT - Computed values
    get filteredUsers() {
        return this.users.filter(user => {
            const matchesRole = this.filters.role === 'all' || user.role === this.filters.role;
            const matchesSearch = user.displayName.toLowerCase()
                                .includes(this.filters.search.toLowerCase());
            const matchesActive = this.filters.showInactive || user.hasLoggedIn;
            return matchesRole && matchesSearch && matchesActive;
        });
    }

    get adminUsers() {
        return this.users.filter(user => user.isAdmin);
    }

    get userStats() {
        return {
            total: this.users.length,
            active: this.users.filter(user => user.hasLoggedIn).length,
            admins: this.adminUsers.length,
        };
    }

    // ✅ CORRECT - State management actions
    setFilters(filters: Partial<typeof this.filters>) {
        Object.assign(this.filters, filters);
    }

    selectUser(user: UserModel) {
        this.selectedUser = user;
    }

    clearSelection() {
        this.selectedUser = null;
    }

    // ✅ CORRECT - Business logic
    promoteToAdmin(userId: string) {
        const user = this.users.find(u => u.id === userId);
        if (user && !user.isAdmin) {
            this.updateUserRoleMutation.mutate({ userId, role: 'admin' });
        }
    }

    // ✅ CORRECT - API mutations
    loadUsersMutation = new ObservedMutation({
        mutationFn: async () => {
            const response = await fetch('/api/users');
            return response.json();
        },
        onSuccess: (data) => {
            this.users = data.map(userData => new UserModel(userData));
        },
    });

    updateUserRoleMutation = new ObservedMutation({
        mutationFn: async ({ userId, role }: { userId: string; role: UserRole }) => {
            const response = await fetch(`/api/users/${userId}/role`, {
                method: 'PUT',
                body: JSON.stringify({ role }),
            });
            return response.json();
        },
        onSuccess: (updatedUser) => {
            const index = this.users.findIndex(u => u.id === updatedUser.id);
            if (index !== -1) {
                this.users[index] = new UserModel(updatedUser);
            }
        },
    });
}

export const sharedUserManagementController = new UserManagementController();
```

## Advanced State Management Patterns

### Shared State Between Controllers

```typescript
// ✅ CORRECT - Controllers can reference each other for shared state
class PolicyBuilderController {
    currentPolicy: PolicyModel | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    get currentUser() {
        // Reference shared controller for user data
        return sharedUserManagementController.selectedUser;
    }

    get canEditPolicy() {
        return this.currentUser?.isAdmin || this.currentPolicy?.createdBy === this.currentUser?.id;
    }

    savePolicy() {
        if (!this.canEditPolicy) {
            throw new Error('User does not have permission to edit this policy');
        }
        // Save logic
    }
}
```

### Reactive State Updates

```typescript
// ✅ CORRECT - Use when() for reactive programming
class NotificationController {
    notifications: NotificationModel[] = [];

    constructor() {
        makeAutoObservable(this);

        // ✅ CORRECT - React to changes in other controllers
        when(
            () => sharedUserManagementController.selectedUser !== null,
            () => {
                this.loadUserNotifications();
            }
        );
    }

    async loadUserNotifications() {
        const user = sharedUserManagementController.selectedUser;
        if (user) {
            this.loadNotificationsMutation.mutate({ userId: user.id });
        }
    }

    loadNotificationsMutation = new ObservedMutation({
        mutationFn: async ({ userId }: { userId: string }) => {
            const response = await fetch(`/api/users/${userId}/notifications`);
            return response.json();
        },
        onSuccess: (data) => {
            this.notifications = data.map(item => new NotificationModel(item));
        },
    });
}
```

### State Persistence

```typescript
// ✅ CORRECT - Controller with localStorage persistence
class UserPreferencesController {
    theme: 'light' | 'dark' = 'light';
    sidebarCollapsed = false;
    language = 'en';

    constructor() {
        makeAutoObservable(this);
        this.loadFromStorage();
    }

    setTheme(theme: 'light' | 'dark') {
        this.theme = theme;
        this.saveToStorage();
    }

    toggleSidebar() {
        this.sidebarCollapsed = !this.sidebarCollapsed;
        this.saveToStorage();
    }

    setLanguage(language: string) {
        this.language = language;
        this.saveToStorage();
    }

    private loadFromStorage() {
        const stored = localStorage.getItem('userPreferences');
        if (stored) {
            const preferences = JSON.parse(stored);
            Object.assign(this, preferences);
        }
    }

    private saveToStorage() {
        const preferences = {
            theme: this.theme,
            sidebarCollapsed: this.sidebarCollapsed,
            language: this.language,
        };
        localStorage.setItem('userPreferences', JSON.stringify(preferences));
    }
}

export const sharedUserPreferencesController = new UserPreferencesController();
```

## Common Anti-Patterns

### 1. Actions in Models

```typescript
// ❌ AVOID - Actions in models
export class PolicyModel {
    id: string;
    name: string;

    // ❌ AVOID - Models should not have actions
    async save() {
        await fetch(`/api/policies/${this.id}`, {
            method: 'PUT',
            body: JSON.stringify(this),
        });
    }

    // ❌ AVOID - Models should not mutate themselves
    updateName(name: string) {
        this.name = name;
    }
}

// ✅ CORRECT - Actions belong in controllers
class PolicyController {
    async savePolicy(policy: PolicyModel) {
        await fetch(`/api/policies/${policy.id}`, {
            method: 'PUT',
            body: JSON.stringify(policy.toJSON()),
        });
    }

    updatePolicyName(policy: PolicyModel, name: string) {
        // Create new model instance or update through controller state
        const updatedData = { ...policy.toJSON(), name };
        return new PolicyModel(updatedData);
    }
}
```

### 2. Business Logic in Components

```typescript
// ❌ AVOID - Business logic in components
export const UserList = () => {
    const [users, setUsers] = useState<UserModel[]>([]);

    // ❌ Business logic in component
    const promoteUser = async (userId: string) => {
        const response = await fetch(`/api/users/${userId}/promote`, {
            method: 'POST',
        });
        const updatedUser = await response.json();
        setUsers(prev => prev.map(u => u.id === userId ? new UserModel(updatedUser) : u));
    };

    return (
        <div>
            {users.map(user => (
                <UserCard
                    key={user.id}
                    user={user}
                    onPromote={() => promoteUser(user.id)}
                />
            ))}
        </div>
    );
};

// ✅ CORRECT - Use controller for business logic
export const UserList = observer(() => {
    const { filteredUsers } = sharedUserManagementController;

    return (
        <div>
            {filteredUsers.map(user => (
                <UserCard
                    key={user.id}
                    user={user}
                    onPromote={() => sharedUserManagementController.promoteToAdmin(user.id)}
                />
            ))}
        </div>
    );
});
```

### 3. Overusing React Context

```typescript
// ❌ AVOID - Context for frequently changing business state
const PolicyContext = createContext<{
    policies: PolicyModel[];
    selectedPolicy: PolicyModel | null;
    // This will cause performance issues
}>({} as any);

// ✅ CORRECT - Use MobX controller instead
export const PoliciesView = observer(() => {
    const { policies, selectedPolicy } = sharedPoliciesController;
    // MobX handles efficient re-renders
});
```

## 🎯 QUICK REFERENCE - COPY/PASTE PATTERNS

### React Event Handler with MobX State Change
```tsx
// Always wrap MobX mutations in runInAction for event handlers
onClick={() => runInAction(() => {
  controller.someProperty = newValue;
  controller.someOtherProperty = anotherValue;
})}
```

### Controller Method Pattern
```tsx
class MyController {
  model: MyModel;
  
  constructor() {
    makeAutoObservable(this);
  }
  
  // Always wrap mutations in runInAction
  updateSomething(value: string) {
    runInAction(() => {
      this.model.property = value;
    });
  }
}
```

### API Call Pattern
```tsx
myMutation = new ObservedMutation(
  async (data: RequestType) => {
    const response = await fetch('/api/endpoint', {
      method: 'POST',
      body: JSON.stringify(data),
    });
    return response.json();
  },
  {
    onSuccess: (result) => {
      runInAction(() => {
        this.someProperty = result.data;
      });
    },
  }
);
```

## 📋 CODE REVIEW CHECKLIST

Before submitting your code, verify:

- [ ] All onClick/onChange handlers use `runInAction` when modifying MobX state
- [ ] All controller methods wrap state mutations in `runInAction`
- [ ] Models contain NO actions (only computed properties)
- [ ] No business logic in React components
- [ ] All API calls use ObservedQuery or ObservedMutation
- [ ] No useState/useReducer for business state (only UI state)
- [ ] Controllers are exported as `sharedXxxController` singletons
- [ ] Components are wrapped with `observer` when using MobX state

## 🤖 AUTOMATED CHECKS (Recommended)

Consider adding these ESLint rules to catch MobX mistakes automatically:

```json
// .eslintrc.json
{
  "plugins": ["mobx"],
  "rules": {
    "mobx/missing-observer": "error",
    "mobx/missing-make-observable": "error",
    "mobx/unconditional-make-observable": "error",
    "mobx/no-anonymous-observer": "warn"
  }
}
```

Also consider a custom ESLint rule to detect state mutations without `runInAction` in event handlers.

## Best Practices Summary

1. **Use MobX Controllers** - For all business logic and state management
2. **Keep Models Pure** - Data structures and computed properties only
3. **Separate Concerns** - Controllers handle logic, models handle data, components handle presentation
4. **Shared Singletons** - Use `sharedXxxController` pattern for global state
5. **React State for UI Only** - Simple, local UI state like dropdowns, modals
6. **Context for Configuration** - Theme, localization, form state scoped to specific forms
7. **ObservedMutation Pattern** - Use for all API calls and side effects
8. **Reactive Programming** - Use `when()` for cross-controller reactivity
9. **Explicit Dependencies** - Import controllers directly, avoid implicit context dependencies
10. **Performance First** - MobX provides better performance than Context for frequently changing state

---

*Following these state management patterns ensures consistent, maintainable, and performant state handling across the Multiverse project.*
