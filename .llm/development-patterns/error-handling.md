# Error Handling Pat<PERSON>s

## Overview

When API errors occur in the application, follow this two-step process to ensure both engineering visibility and good user experience.

## The Two-Step Process

### 1. Log the Error for Engineering Visibility

Use `logger.error(...)` to capture the full technical details so engineers can debug and fix issues.

**Include in logs:**
- The original error message and stack trace
- HTTP status codes
- Request context (what the user was trying to do)
- Any relevant data that might help with debugging

```typescript
import { logger } from '@globals/logger';

try {
  await someApiCall();
} catch (error) {
  // Option 1: Simple string message
  logger.error('Failed to update company settings');

  // Option 2: Complex object with additional context
  logger.error({
    message: 'Failed to update company settings',
    additionalInfo: {
      userId: user.id,
      companyId: company.id,
      action: 'updateCompanySettings'
    },
    errorObject: {
      message: error.message,
      statusCode: error.status || error.statusCode || 'unknown'
    }
  });

  // Step 2: Help the customer recover (see below)
}
```

### 2. Help the Customer Recover

Generate user-friendly messages based on error codes or types and send them to the UI using `snackbarController.addSnackbar(...)`.

```typescript
import { snackbarController } from '@controllers/snackbar';
import { t } from '@globals/i18n/macro';

// After logging the error above...
snackbarController.addSnackbar({
  id: 'update-company-settings-error',
  props: {
    title: t`We couldn't save your changes right now. Please try again in a few moments.`,
    severity: 'critical',
    closeButtonAriaLabel: t`Close`,
  },
});
```

## Error Message Guidelines

### ✅ DO:

- **Be specific** about what went wrong
- **Use plain language** without technical jargon
- **Provide clear next steps** to resolve the issue
- **Maintain a helpful tone** without blame
- **Use internationalization** with the `t` macro
- **Keep internal system details private**

### ❌ DON'T:

- Show raw API error messages
- Use technical terms or stack traces
- Expose database constraints, table names, or internal IDs
- Reveal file paths or server configurations
- Blame the user
- Leave users hanging without next steps

## Example Patterns

### Direct Error Message Exposure

```typescript
// ❌ BAD: Directly passing error.message to user
try {
  await someApiCall();
} catch (error) {
  snackbarController.addSnackbar({
    id: 'api-error',
    props: {
      title: error.message, // Could be "Database connection timeout" or "Invalid JWT token signature"
      severity: 'critical',
      closeButtonAriaLabel: t`Close`,
    },
  });
}

// ❌ BAD: Only logging, no user feedback
try {
  await someApiCall();
} catch (error) {
  logger.error({
    message: 'API call failed',
    errorObject: {
      message: error.message,
      statusCode: error.status || error.statusCode || 'unknown'
    }
  });
  // Missing step 2: No user-friendly message shown to the customer!
  // User has no idea something went wrong
}

// ✅ GOOD: User-friendly message with proper logging
try {
  await someApiCall();
} catch (error) {
  // Step 1: Log for engineering
  logger.error({
    message: 'API call failed',
    additionalInfo: {
      action: 'someApiCall',
      userId: currentUser?.id
    },
    errorObject: {
      message: error.message,
      statusCode: error.status || error.statusCode || 'unknown'
    }
  });

  // Step 2: Help the customer
  snackbarController.addSnackbar({
    id: 'api-call-error',
    props: {
      title: t`Something went wrong. Please try again.`,
      severity: 'critical',
      closeButtonAriaLabel: t`Close`,
    },
  });
}
```

### Network/Connection Errors

```typescript
// ❌ BAD: Raw error message
snackbarController.addSnackbar({
  id: 'network-error',
  props: {
    title: 'NetworkError: fetch failed at line 42',
    severity: 'critical',
    closeButtonAriaLabel: t`Close`,
  },
});

// ✅ GOOD: User-friendly message
snackbarController.addSnackbar({
  id: 'network-connection-error',
  props: {
    title: t`We're having trouble connecting to our servers. Please check your internet connection and try again.`,
    severity: 'critical',
    closeButtonAriaLabel: t`Close`,
  },
});
```

### Validation Errors

```typescript
// ❌ BAD: Technical validation message
snackbarController.addSnackbar({
  id: 'validation-error',
  props: {
    title: 'Constraint violation: company_name_unique_idx',
    severity: 'critical',
    closeButtonAriaLabel: t`Close`,
  },
});

// ✅ GOOD: Clear, actionable message
snackbarController.addSnackbar({
  id: 'company-name-exists-error',
  props: {
    title: t`A company with this name already exists. Please choose a different name.`,
    severity: 'critical',
    closeButtonAriaLabel: t`Close`,
  },
});
```

### Permission Errors

```typescript
// ❌ BAD: Exposing internal permission structure
snackbarController.addSnackbar({
  id: 'permission-error',
  props: {
    title: 'User lacks COMPANY_ADMIN role for resource ID 12345',
    severity: 'critical',
    closeButtonAriaLabel: t`Close`,
  },
});

// ✅ GOOD: Respectful, clear message
snackbarController.addSnackbar({
  id: 'access-denied-error',
  props: {
    title: t`You don't have permission to make this change. Contact your administrator if you need access.`,
    severity: 'critical',
    closeButtonAriaLabel: t`Close`,
  },
});
```

### Server Errors

```typescript
// ❌ BAD: Internal server details
snackbarController.addSnackbar({
  id: 'server-error',
  props: {
    title: 'Internal Server Error: Database connection pool exhausted',
    severity: 'critical',
    closeButtonAriaLabel: t`Close`,
  },
});

// ✅ GOOD: Professional, helpful message
snackbarController.addSnackbar({
  id: 'internal-server-error',
  props: {
    title: t`Something went wrong on our end. We've been notified and are working to fix it. Please try again in a few minutes.`,
    severity: 'critical',
    closeButtonAriaLabel: t`Close`,
  },
});
```


## Why This Approach Works

### User Experience
Users get helpful, actionable messages instead of confusing technical errors that don't help them understand what to do next.

### Security & Professionalism
API errors can include sensitive information we don't want users seeing:
- Internal system details and architecture info
- Database table/column names and constraints
- File paths and server configurations
- Third-party service details
- Internal error codes that could reveal system vulnerabilities

This isn't necessarily "secret" information, but it makes our system look unprofessional and could potentially be misused.

### Engineering & Support
- **Debugging**: Engineers get full error details in logs for faster incident resolution
- **Support**: Customer support can better help users when they understand what went wrong
- **Trust**: Professional error handling builds user confidence in our product

## Related Patterns

- **Logging**: See logging patterns for structured error logging
- **Snackbar Controller**: See controller patterns for snackbar usage
- **Internationalization**: See i18n patterns for proper message translation
- **API SDK**: See API SDK patterns for handling API responses
