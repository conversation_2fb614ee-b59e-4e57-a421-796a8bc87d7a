# Common Testing Pitfalls - Learn from Mistakes

## 🚨 CRITICAL: Avoid These Testing Mistakes

This document catalogs common testing mistakes and how to avoid them. These patterns have caused real issues in the codebase.

## 1. Overly Complex Initial Tests

### ❌ WRONG - Starting Too Complex

```typescript
// Trying to test everything at once with complex mocks
describe('policy builder controller', () => {
  it('should handle complex business logic with API calls', async () => {
    const mockApi = vi.fn().mockResolvedValue({ data: [...] });
    const mockRouter = { navigate: vi.fn() };
    const mockToast = { show: vi.fn() };
    const mockFeatureAccess = { canEdit: vi.fn().mockReturnValue(true) };
    
    // 20 more lines of setup...
    // Complex assertions...
  });
});
```

### ✅ CORRECT - Start Simple

```typescript
describe('policy builder controller', () => {
  it('should be constructable', () => {
    const controller = new PolicyBuilderController();
    expect(controller).toBeDefined();
  });

  it('should have required methods', () => {
    const controller = new PolicyBuilderController();
    expect(controller.load).toBeDefined();
    expect(controller.save).toBeDefined();
  });
});
```

## 2. Incorrect Import Patterns

### ❌ WRONG - Guessing Module Structure

```typescript
// These imports assume a structure that doesn't exist
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import { PolicyModel, UserModel, SettingsModel } from '@models/policy';
import { Button, Form, Input } from '@ui';
```

### ✅ CORRECT - Use Exact File Imports

```typescript
// Each module has its own file with specific naming
import { sharedPolicyBuilderController } from '@controllers/policy-builder/policy-builder.controller';
import { PolicyModel } from '@models/policy/policy.model';
import { AppButton } from '@ui/app-button';
import { FormField } from '@components/form-field';
```

## 3. ESLint and TypeScript Violations

### ❌ WRONG - Ignoring Linting Errors

```typescript
describe('My Component', () => { // Uppercase - WRONG
it('Should Do Something', () => { // Uppercase - WRONG
  const result: any = someFunction(); // Using any without proper typing
  // @ts-ignore
  expect(result.foo).toBe('bar'); // Ignoring TS errors
});
});
```

### ✅ CORRECT - Following All Conventions

```typescript
describe('my component', () => { // lowercase
  // blank line required after describe
  
  it('should do something', () => { // lowercase
    const result = someFunction() as SomeType;
    expect(result.foo).toBe('bar');
  });
});
```

## 4. Async/Await Context Errors

### ❌ WRONG - Await Outside Async

```typescript
describe('test', () => {
  // This causes syntax error
  const module = await import('./my-module');
  
  it('should work', () => {
    // This also fails
    const { Component } = await import('./component');
  });
});
```

### ✅ CORRECT - Proper Async Handling

```typescript
describe('test', () => {
  let module: any;

  beforeEach(async () => {
    module = await import('./my-module').catch(() => null);
  });

  it('should work', async () => {
    const { Component } = await import('./component').catch(() => ({}));
    expect(Component).toBeDefined();
  });
});
```

## 5. Testing Non-Existent Functionality

### ❌ WRONG - Assuming Implementation Details

```typescript
it('should call internal method when button clicked', () => {
  // Testing private methods or internals
  expect(component._handleClick).toHaveBeenCalled();
  expect(component.state.internalFlag).toBe(true);
});
```

### ✅ CORRECT - Test Observable Behavior

```typescript
it('should display success message when save completes', async () => {
  // Test what the user sees/experiences
  await userEvent.click(screen.getByText('Save'));
  expect(await screen.findByText('Saved successfully')).toBeInTheDocument();
});
```

## 6. Copy-Paste Without Adaptation

### ❌ WRONG - Blindly Copying Patterns

```typescript
// Copied from UserController test but forgot to update
describe('policy controller', () => {
  it('should load user data', () => { // Still says "user"!
    const controller = new PolicyController();
    expect(controller.userData).toBeDefined(); // Wrong property
  });
});
```

### ✅ CORRECT - Adapt to Context

```typescript
describe('policy controller', () => {
  it('should load policy data', () => {
    const controller = new PolicyController();
    expect(controller.policyData).toBeDefined();
  });
});
```

## 7. Missing Test Infrastructure

### ❌ WRONG - Using Unavailable Matchers

```typescript
it('should be visible', () => {
  render(<Component />);
  // This fails if jest-dom isn't set up
  expect(element).toBeInTheDocument();
  expect(element).toHaveClass('active');
});
```

### ✅ CORRECT - Check Available Matchers

```typescript
// If jest-dom isn't available, use basic matchers
it('should be visible', () => {
  const { container } = render(<Component />);
  const element = container.querySelector('[data-testid="my-element"]');
  expect(element).not.toBeNull();
  expect(element?.className).toContain('active');
});
```

## 8. Not Running Verification Commands

### ❌ WRONG - Submitting Without Checking

```typescript
// Writing tests and submitting without running:
// - pnpm run lint
// - pnpm run typecheck
// - pnpm run test
```

### ✅ CORRECT - Always Verify Before Submitting

```bash
# Run these commands before considering task complete:
pnpm run lint        # Fix all ESLint errors
pnpm run typecheck   # Fix all TypeScript errors
pnpm run test        # Ensure tests run (even if failing in TDD)
```

## Pre-Submission Checklist

Before submitting any test file:

- [ ] Ran `pnpm run lint` and fixed all errors
- [ ] Ran `pnpm run typecheck` and fixed all errors
- [ ] Test file uses `.spec.ts` or `.spec.tsx` extension
- [ ] All describe blocks use lowercase
- [ ] Blank lines after describe blocks
- [ ] No `any` types without proper handling
- [ ] Imports use try-catch for non-existent files
- [ ] No await outside async functions
- [ ] Following exact import paths from codebase
- [ ] Tests focus on behavior, not implementation

## Key Lessons

1. **Start Simple**: Begin with basic "does it exist" tests
2. **Follow Patterns**: Study existing tests and match their style exactly
3. **Handle Errors**: Gracefully handle non-existent imports in TDD
4. **Fix Linting**: Never submit code with ESLint/TS errors
5. **Test Behavior**: Focus on what users experience, not how code works
6. **Verify First**: Always run lint/typecheck before considering done

## Remember

> "The best test is one that fails for the right reason in TDD red phase, then passes when the feature is implemented correctly."

Tests should be:
- Simple enough to understand at a glance
- Complex enough to catch real issues
- Focused on user-facing behavior
- Free of all linting errors