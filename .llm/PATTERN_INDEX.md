# Pattern Index - Your Starting Point

**For LLM Agents**: This is your main reference. When coding and you need to know:
- How to import something → Check "Import Violations" below
- How to name a file → Check "Creating a New File" below
- Any other pattern → Search this file for keywords

You do NOT need to read other files unless this index points you there.

## 🚨 CRITICAL: Found Incorrect Documentation?

**If you discover any inaccuracy in .llm files:**
1. **NOTIFY THE USER IMMEDIATELY** - Don't silently work around it
2. **Create a todo** to fix the documentation
3. **Provide specific details**: file path, line number, what's wrong, suggested fix
4. **Continue with the correct approach** but ensure the docs get updated

Example: "I found that the import path in api-sdk-patterns.md line 45 is outdated. The correct import is X not Y. I'll add a todo to fix this."

## 🚨 IMPORTANT: Adding New Documentation

**DO NOT EDIT THIS INDEX FILE** to add detailed information. This is only a quick reference!

📖 **See [DOCUMENTATION_GUIDELINES.md](./DOCUMENTATION_GUIDELINES.md) for where to add new content**

When adding new patterns or documentation:
1. Check if a relevant pattern file already exists in `.llm/development-patterns/`, `.llm/critical-rules/`, or `.llm/build-time-rules/`
2. Add your detailed documentation to the appropriate existing file
3. Only update this index with a brief pointer to that documentation
4. If no appropriate file exists, create a new one in the correct directory

Example:
- ❌ WRONG: Adding 10 lines about API workflow to this index
- ✅ RIGHT: Adding details to `api-sdk-patterns.md` and a one-line pointer here

## 🎯 QUICK START PATTERNS

### Project Structure
- Controllers live in: `controllers/[feature]/src/lib/[feature].controller.ts`
- API SDK is in: `globals/api-sdk/src/lib/generated/`
- Components live in: `components/[feature]/src/lib/`

### Need to use the API?
1. Check if endpoint exists: Look in `globals/api-sdk/src/lib/generated/sdk.gen.ts`
2. If not found: The endpoint doesn't exist yet - coordinate with backend team
3. Update SDK: Run `pnpm run update-api-sdk` after backend adds the endpoint

### Creating a Controller?
Jump to: [Creating Controllers](#creating-controllers) section below for complete example

### Creating a Component?
Jump to: [Building Components](#building-components) section below

## 🚨 CRITICAL BUILD-BREAKING RULES

These violations will cause ESLint errors or runtime failures:

### Import Violations
**Keywords**: `import`, `mobx`, `lingui`, `@tanstack`, `react-table`
- **Rule**: Use global modules for restricted imports
- **File**: [pitfalls.md#import-restrictions](./pitfalls.md)
- **Quick Fix**:
  - `mobx-react-lite` → `@globals/mobx`
  - `@lingui/macro` → `@globals/i18n/macro`
  - `@tanstack/react-table` → `@cosmos/components/datatable`

### Navigation Components
**Keywords**: `Link`, `navigate`, `href`, `routing`, `button`, `redirect`
- **Rule**: Use AppLink/AppButton for declarative navigation, useNavigate for programmatic
- **Files**:
  - [development-patterns/navigation-patterns.md](./development-patterns/navigation-patterns.md) - Complete navigation guide
  - [critical-rules/applink-usage.md](./critical-rules/applink-usage.md) - AppLink details
  - [development-patterns/appbutton-href-usage.md](./development-patterns/appbutton-href-usage.md) - AppButton navigation
- **Quick Fix**:
  - Links: `@ui/app-link`
  - Buttons with navigation: `@ui/app-button` with `href` prop
  - Programmatic: `useNavigate` from `@remix-run/react`

### Data Tables
**Keywords**: `table`, `datatable`, `grid`, `rows`
- **Rule**: Always use AppDatatable, never legacy Datatable
- **Files**:
  - [critical-rules/appdatatable-usage.md](./critical-rules/appdatatable-usage.md)
  - [critical-rules/appdatatable-cells.md](./critical-rules/appdatatable-cells.md)
- **Quick Fix**: Use `@components/app-datatable`

### Export Rules
**Keywords**: `export`, `default`, `module`
- **Rule**: Named exports only (except routes)
- **File**: [critical-rules/export-patterns.md](./critical-rules/export-patterns.md)
- **Quick Fix**: `export const MyComponent` not `export default`

### Test Import Handling
**Keywords**: `test import`, `module not found`, `await outside async`, `TDD imports`
- **Rule**: Handle non-existent imports gracefully in tests
- **File**: [critical-rules/test-import-handling.md](./critical-rules/test-import-handling.md)
- **Quick Fix**: Use try-catch or dynamic imports in beforeEach

## 📋 PATTERN LOOKUP BY TASK

### Creating a New File
**Keywords**: `new file`, `create`, `naming`, `test`, `spec`
1. **File Naming**: [build-time-rules/file-naming.md](./build-time-rules/file-naming.md)
   - Format: `export-name.modifier.extension`
   - Use kebab-case only
   - Test files use `.spec.ts` or `.spec.tsx` (NOT `.test.ts`)
2. **Single Export**: [build-time-rules/single-export-files.md](./build-time-rules/single-export-files.md)
   - One export per file
   - Filename matches export name

### Testing & TDD
**Keywords**: `test`, `tdd`, `spec`, `vitest`, `testing-library`, `red phase`, `failing test`
1. **TDD Patterns**: [development-patterns/tdd-testing-patterns.md](./development-patterns/tdd-testing-patterns.md)
   - Writing tests before implementation
   - Handling non-existent imports
   - Proper test structure
   - Common TDD mistakes to avoid
2. **Testing Pitfalls**: [development-patterns/testing-pitfalls.md](./development-patterns/testing-pitfalls.md)
   - Common mistakes and how to avoid them
   - Pre-submission checklist
   - Real examples from the codebase
3. **Test Naming**: Use `.spec.ts` or `.spec.tsx` extension
4. **Test Structure**: Lowercase describe blocks, proper spacing

### Building Components
**Keywords**: `component`, `react`, `UI`
1. **Design Pattern**: [development-patterns/component-design.md](./development-patterns/component-design.md)
   - Separation of concerns
   - Composition over configuration
2. **Styling**: [development-patterns/styling.md](./development-patterns/styling.md)
   - CSS modules + design tokens
   - No child selectors
3. **Data Attributes**: [development-patterns/conventions.md#component-requirements](./development-patterns/conventions.md)
   - Add `data-testid` and `data-id`

### State Management
**Keywords**: `state`, `mobx`, `controller`, `model`, `useState`
1. **MobX Pattern**: [development-patterns/state-management.md](./development-patterns/state-management.md)
   - Use Controllers + Models
   - Avoid React state for business logic
2. **Mutations**: [critical-rules/mobx-mutations.md](./critical-rules/mobx-mutations.md)
   - ObservedMutation + onSuccess + when()
3. **Data Fetching**: [development-patterns/data-fetching.md](./development-patterns/data-fetching.md)
   - ObservedQuery for fetching
   - `.load()` and `.invalidate()`

### API SDK (Auto-Generated)
**Keywords**: `api`, `sdk`, `openapi`, `generated`, `fetch`, `endpoint`
1. **Complete Guide**: [development-patterns/api-sdk-patterns.md](./development-patterns/api-sdk-patterns.md)
2. **Quick Check**: API files are in `globals/api-sdk/src/lib/`
   - Generated files: `globals/api-sdk/src/lib/generated/`
   - **Endpoint Index**: `globals/api-sdk/src/lib/generated/api-endpoints.json` (auto-generated)
3. **Quick Usage**: Import from `@globals/api-sdk`
   - **NEVER** edit files in `@globals/api-sdk` - they're auto-generated!
   - Use `pnpm run update-api-sdk` to sync with backend
4. **Finding Endpoints**:
   - Check the auto-generated index: `globals/api-sdk/src/lib/generated/api-endpoints.json`
   - This file contains all controllers, queries, and mutations
   - Updated automatically when you run `pnpm run update-api-sdk`
   - See [Finding Available Endpoints](./development-patterns/api-sdk-patterns.md#finding-available-endpoints) for the complete workflow
5. **Can't Find an Endpoint?**
   - If endpoint doesn't exist in `api-endpoints.json`, it hasn't been added to backend yet
   - Check with backend team or create a ticket for the new endpoint
   - Once backend adds it, run `pnpm run update-api-sdk`
6. **Query Pattern**: See [Data Fetching with ObservedQuery](./development-patterns/api-sdk-patterns.md#data-fetching-with-observedquery)
7. **Mutation Pattern**: See [Data Mutations with ObservedMutation](./development-patterns/api-sdk-patterns.md#data-mutations-with-observedmutation)

### Creating Controllers
**Keywords**: `controller`, `new controller`, `create controller`, `mobx controller`
- **Complete Guide**: [Controller Patterns](./development-patterns/controller-patterns.md)
- **Quick Info**: Controllers live in `controllers/[feature-name]/src/lib/[feature-name].controller.ts`
- **Key Concepts**: ObservedQuery for fetching, ObservedMutation for actions
- **State Management**: [State Management Patterns](./development-patterns/state-management.md)

### Forms
**Keywords**: `form`, `input`, `validation`, `zod`
1. **Form System**: [development-patterns/form-system.md](./development-patterns/form-system.md)
   - Schema-based with Zod
2. **Form Fields**: [development-patterns/form-fields.md](./development-patterns/form-fields.md)
   - Field types reference
3. **Validation**: [development-patterns/form-validation.md](./development-patterns/form-validation.md)
   - Zod schemas and patterns

### TypeScript
**Keywords**: `type`, `interface`, `satisfies`, `as`
1. **TypeScript Patterns**: [build-time-rules/typescript-patterns.md](./build-time-rules/typescript-patterns.md)
   - Use `satisfies` operator
   - Avoid type assertions
2. **Data Mapping**: [development-patterns/data-mapping-patterns.md](./development-patterns/data-mapping-patterns.md)
   - Switch statements over objects

### Internationalization
**Keywords**: `i18n`, `translate`, `t`, `Trans`, `locale`
1. **i18n Patterns**: [development-patterns/internationalization.md](./development-patterns/internationalization.md)
   - Function-based translations
   - Avoid constants with t``
   - Import from `@globals/i18n/macro`

### Error Handling
**Keywords**: `error`, `exception`, `api error`, `logging`, `snackbar`, `user message`
1. **Error Handling**: [development-patterns/error-handling.md](./development-patterns/error-handling.md)
   - Two-step process: log for engineering, help the customer
   - User-friendly messages with internationalization
   - Never expose internal system details

### Permissions & Access
**Keywords**: `permission`, `access`, `auth`, `feature flag`
1. **Access Control**: [development-patterns/permissions-and-access.md](./development-patterns/permissions-and-access.md)
   - Always use FeatureAccessModel
   - Never direct permission checks

### Package/Module Creation
**Keywords**: `package`, `barrel`, `export`, `public API`
1. **Package Exports**: [build-time-rules/package-exports.md](./build-time-rules/package-exports.md)
   - Explicit barrel exports
   - No `export *`

### File Organization
**Keywords**: `structure`, `folder`, `organize`, `directory`
1. **Organization**: [development-patterns/file-organization.md](./development-patterns/file-organization.md)
   - Domain-driven structure
   - Co-location principles

## 🔍 QUICK VIOLATION CHECKER

Before committing code, check for these common violations:

```typescript
// ❌ VIOLATIONS THAT WILL FAIL
import { observer } from 'mobx-react-lite'; // → @globals/mobx
import { Link } from '@remix-run/react'; // → @ui/app-link
export default MyComponent; // → export const MyComponent
const label = t`Hello`; // → const getLabel = () => t`Hello`
if (user.permissions.includes('admin')) // → featureAccessModel.canAccess()

// ✅ CORRECT PATTERNS
import { observer } from '@globals/mobx';
import { AppLink } from '@ui/app-link';
export const MyComponent = observer(() => {
  const getLabel = () => t`Hello`;
  if (sharedFeatureAccessModel.canAccess('admin')) {
    // ...
  }
});
```

## 🎯 CONTEXT TRIGGERS

These keywords should trigger loading specific documentation:

- **"import"** → Check import restrictions
- **"export"** → Check export patterns
- **"state"** → Load state management patterns
- **"form"** → Load form system docs
- **"table"** → Load AppDatatable patterns
- **"link"/"navigate"/"button"/"redirect"** → Load navigation patterns
- **"permission"/"access"** → Load FeatureAccessModel docs
- **"style"/"css"** → Load styling patterns
- **"controller"/"model"** → Load MobX patterns & Creating Controllers
- **"translate"/"i18n"** → Load internationalization
- **"api"/"sdk"/"fetch"** → Load API SDK patterns
- **"error"/"exception"/"logging"/"snackbar"** → Load error handling patterns
- **"create controller"/"new controller"** → Load Creating Controllers section
- **"endpoint"/"openapi"** → Load API SDK patterns
- **"test"/"tdd"/"spec"/"vitest"** → Load TDD testing patterns
- **"red phase"/"failing test"** → Load TDD patterns for tests before implementation
- **"testing mistake"/"test error"/"lint error"** → Load testing pitfalls

## 📍 NAVIGATION SHORTCUTS

- **Project Setup**: [README.md](./README.md) → [project-docs/overview.md](./project-docs/overview.md)
- **Code Style**: [development-patterns/conventions.md](./development-patterns/conventions.md) → [project-docs/tooling.md](./project-docs/tooling.md)
- **Common Issues**: [development-patterns/pitfalls.md](./development-patterns/pitfalls.md)
- **Contributing**: [project-docs/contribution.md](./project-docs/contribution.md)
- **Development Patterns**: [development-patterns/](./development-patterns/)
- **Critical Rules**: [critical-rules/](./critical-rules/)
- **Build-Time Rules**: [build-time-rules/](./build-time-rules/)
