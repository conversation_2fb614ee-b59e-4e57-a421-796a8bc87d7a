# File Naming Conventions

> **OFFICIAL STANDARD**: Use kebab-case for all file names with appropriate suffixes to indicate file purpose.

## Overview

Consistent file naming is fundamental to maintaining a clean, searchable, and organized codebase. This guide establishes the official naming patterns used throughout the Multiverse project.

## Justification

- **Consistent Naming**: Provides uniform naming across the entire codebase
- **Clear Purpose**: Makes file purpose immediately clear from the filename
- **Easy Search**: Simplifies file search and organization
- **Tool Compatibility**: Works well with all development tools and file systems
- **Team Efficiency**: Reduces cognitive load when navigating the codebase

## Core Pattern

### Single-Responsibility Files

For files with a single export, use the pattern: `export-name.modifier.extension`

```
// ✅ CORRECT - Single export files with appropriate modifiers
user-profile.component.tsx // Component file
foo.view.tsx               // View file
format-date.helper.ts      // Helper function
user.model.ts              // Model definition
user-details.controller.ts // Controller
api-endpoints.constant.ts  // Single group of constants
button-props.type.ts       // Type definitions
```

### Multiple-Export Files

For files with multiple related exports, use the plural form: `scope.modifiers.extension`

```
// ✅ CORRECT - Multiple export files with plural modifiers
user.constants.ts          // Multiple constants related to users
date.helpers.ts            // Multiple date-related helper functions
form.types.ts              // Multiple form-related type definitions
```

## File Type Modifiers

### Controllers
```
// ✅ CORRECT
evidence-mutation.controller.tsx
policies.controller.tsx
user-profile.controller.tsx

// ❌ INCORRECT
evidence-mutation-controller.tsx    // Missing dot separator
evidenceMutationController.tsx      // camelCase
EvidenceMutationController.tsx      // PascalCase
```

### Models
```
// ✅ CORRECT
user-profile.model.tsx
evidence-details.model.tsx
policy-builder-page-header.model.tsx

// ❌ INCORRECT
user-profile-model.tsx              // Missing dot separator
userProfileModel.tsx                // camelCase
UserProfileModel.tsx                // PascalCase
```

### Components
```
// ✅ CORRECT
policy-builder-header.component.tsx
evidence-upload-form.component.tsx
user-avatar-detail.component.tsx

// ❌ INCORRECT
PolicyBuilderHeader.tsx             // PascalCase without modifier
policy-builder-header.tsx           // Missing modifier
policyBuilderHeader.component.tsx   // camelCase
```

### Views
```
// ✅ CORRECT
policies.view.tsx
evidence-library.view.tsx
user-profile-settings.view.tsx

// ❌ INCORRECT
PoliciesView.tsx                    // PascalCase without modifier
policies-view.tsx                   // Missing dot separator
policies.tsx                        // Missing modifier
```

### Helpers
```
// ✅ CORRECT
format-currency.helper.ts
build-evidence-request-dto.helper.ts
validate-email.helper.ts

// ❌ INCORRECT
formatCurrency.helper.ts            // camelCase
format-currency-helper.ts           // Missing dot separator
FormatCurrencyHelper.ts             // PascalCase
```

### Constants
```
// ✅ CORRECT - Single group of constants
columns.constant.tsx
table-actions.constant.tsx
validation-rules.constant.tsx

// ✅ CORRECT - Multiple groups of constants
user.constants.ts
api.constants.ts
form.constants.ts

// ❌ INCORRECT
columns.constants.tsx               // Wrong plural for single export
COLUMNS.constant.tsx                // UPPER_CASE
columnsConstant.tsx                 // camelCase
```

### Types
```
// ✅ CORRECT - Single type definition
user-profile.type.ts
evidence-upload.type.ts
policy-builder.type.ts

// ✅ CORRECT - Multiple type definitions
user.types.ts
api.types.ts
form.types.ts

// ❌ INCORRECT
userProfile.types.ts                // camelCase
user-profile.types.ts               // Wrong plural for single export
UserProfileTypes.ts                 // PascalCase
```

### Test Files
```
// ✅ CORRECT - Test files use .spec extension
user-profile.model.spec.ts
policy-builder.controller.spec.ts
format-date.helper.spec.ts
evidence-upload.component.spec.tsx

// ❌ INCORRECT
user-profile.model.test.ts          // Use .spec not .test
userProfile.model.spec.ts           // camelCase
user-profile-model.spec.ts          // Missing dot separator
UserProfileModel.spec.ts            // PascalCase
```

## Special Cases

### Route Files (Remix)
```
// ✅ CORRECT - Remix route files follow framework conventions
app/routes/workspaces.$workspaceId.policies.tsx
app/routes/evidence-library.tsx
app/routes/_index.tsx

// Note: Route files use Remix naming conventions, not our standard pattern
```

### Index Files
```
// ✅ CORRECT - Barrel exports
index.ts                            // Standard barrel file
index.tsx                           // When JSX is needed

// ❌ INCORRECT
Index.ts                            // PascalCase
main.ts                             // Non-standard name
```

### Configuration Files
```
// ✅ CORRECT - Configuration files
vite.config.ts
eslint.config.js
package.json

// Note: Config files follow their respective tool conventions
```

## Directory Naming

### General Directories
```
// ✅ CORRECT - kebab-case for directories
src/
├── components/
├── controllers/
├── evidence-library/
├── user-profile/
└── policy-builder/

// ❌ INCORRECT
src/
├── Components/                     // PascalCase
├── evidence_library/               // snake_case
├── userProfile/                    // camelCase
└── PolicyBuilder/                  // PascalCase
```

### View Directories
```
// ✅ CORRECT - View directory structure
views/
├── policies/
├── evidence-library/
├── user-profile-settings/
└── risk-register-mitigation-controls/
```

## Common Patterns

### Cell Components (AppDatatable)
```
// ✅ CORRECT
policy-name-cell.component.tsx
policy-status-cell.component.tsx
policy-actions-cell.component.tsx

// ❌ INCORRECT
PolicyNameCell.tsx                  // Missing modifier
policy-name-cell.tsx                // Missing modifier
policyNameCell.component.tsx        // camelCase
```

### Page Header Models
```
// ✅ CORRECT
policy-builder-page-header.model.tsx
evidence-library-page-header.model.tsx
user-profile-page-header.model.tsx
```

### Mutation Controllers
```
// ✅ CORRECT
evidence-mutation.controller.tsx
policy-mutation.controller.tsx
user-mutation.controller.tsx
```

## Anti-Patterns to Avoid

### Case Violations
```
// ❌ AVOID - Wrong case patterns
PolicyController.tsx                // PascalCase files
policy_controller.tsx               // snake_case
policyController.tsx                // camelCase
POLICY_CONTROLLER.tsx               // UPPER_CASE
```

### Missing Modifiers
```
// ❌ AVOID - Missing purpose indicators
policy-builder.tsx                  // Is this a component? view? helper?
user-profile.ts                     // What type of file is this?
evidence-upload.tsx                 // Unclear purpose
```

### Inconsistent Separators
```
// ❌ AVOID - Wrong separators
policy-builder-component.tsx        // Missing dot separator
policy_builder.component.tsx        // Wrong underscore
policyBuilder.component.tsx         // camelCase
```

### Redundant Descriptors
```
// ❌ AVOID - Extra unnecessary words
policy-builder-header-component-file.tsx    // Too verbose
user-profile-model-class.tsx                // Redundant descriptors
evidence-upload-form-component.tsx          // "form" + "component" redundant
```

## Best Practices

### 1. Be Descriptive but Concise
```
// ✅ GOOD
user-profile-settings.view.tsx
evidence-upload-validation.helper.ts

// ❌ TOO VERBOSE
user-profile-settings-page-view-component.tsx
evidence-upload-form-validation-helper-function.ts

// ❌ TOO VAGUE
settings.view.tsx
validation.helper.ts
```

### 2. Use Domain Language
```
// ✅ GOOD - Uses business domain terms
policy-builder.view.tsx
evidence-library.controller.tsx
risk-assessment.model.tsx

// ❌ GENERIC - Too technical/generic
form-builder.view.tsx
data-manager.controller.tsx
object.model.tsx
```

### 3. Group Related Files
```
// ✅ GOOD - Related files have similar prefixes
policy-builder.view.tsx
policy-builder-header.component.tsx
policy-builder-tabs.component.tsx
policy-builder.controller.tsx
policy-builder.model.tsx
```

### 4. Match Export Names
```
// ✅ GOOD - File name matches export
// file: user-profile.component.tsx
export const UserProfile = () => { /* ... */ };

// file: format-currency.helper.ts
export const formatCurrency = (amount: number) => { /* ... */ };
```

> **Note**: For comprehensive guidance on single export patterns, see [Single Export Files](./single-export-files.md).

## Generator Compliance

When using `pnpm run generate`, the generated files automatically follow these naming conventions:

```bash
# Generator creates files with correct naming
pnpm run generate
# Creates: my-feature.controller.tsx
# Creates: my-feature.model.tsx
# Creates: my-feature.component.tsx
```

## Validation

### ESLint Integration
The project's ESLint configuration can be extended to validate file naming patterns:

```javascript
// Future ESLint rule (example)
"filename-rules/match": [
  "error",
  {
    ".tsx": "^[a-z-]+\\.(component|view|controller|model)\\.tsx$",
    ".ts": "^[a-z-]+\\.(helper|constant|type|controller|model)\\.ts$"
  }
]
```

---

*Following these naming conventions ensures consistency, maintainability, and a great developer experience across the Multiverse project.*
