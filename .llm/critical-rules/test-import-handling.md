# Test Import Handling - Critical Rules

## 🚨 CRITICAL: Handling Imports in Tests

When writing tests, especially in TDD where implementation doesn't exist yet, imports MUST be handled properly to avoid runtime errors.

## The Problem

```typescript
// ❌ THIS WILL CRASH YOUR TESTS
import { MyComponent } from './my-component'; // File doesn't exist yet
import { MyController } from '@controllers/my-feature'; // Module doesn't exist

// ❌ THIS CAUSES SYNTAX ERRORS
describe('test', () => {
  const { MyComponent } = await import('./my-component'); // await outside async
});
```

## The Solution

### Pattern 1: Try-Catch in beforeEach

```typescript
describe('my feature', () => {
  let MyComponent: any;
  let MyController: any;

  beforeEach(async () => {
    // Handle component imports
    try {
      const componentModule = await import('./my-component');
      MyComponent = componentModule.MyComponent;
    } catch {
      MyComponent = null;
    }

    // Handle controller imports
    try {
      const controllerModule = await import('@controllers/my-feature/my-feature.controller');
      MyController = controllerModule.MyController;
    } catch {
      MyController = null;
    }
  });

  it('should handle missing imports gracefully', () => {
    if (!MyComponent) {
      expect(MyComponent).toBeNull();
      return;
    }
    // Test implementation
  });
});
```

### Pattern 2: Import Existence Test

```typescript
describe('module exports', () => {
  it('should export MyComponent', async () => {
    try {
      const module = await import('./my-component');
      expect(module.MyComponent).toBeDefined();
    } catch (error) {
      // This is expected in TDD red phase
      expect(error).toBeDefined();
    }
  });
});
```

### Pattern 3: Mock Fallbacks

```typescript
describe('component with dependencies', () => {
  let Component: any;
  let mockController: any;

  beforeEach(async () => {
    // Create mock if real implementation doesn't exist
    mockController = {
      load: vi.fn(),
      save: vi.fn(),
      isLoading: false,
    };

    try {
      const module = await import('./my-component');
      Component = module.Component;
    } catch {
      // Component doesn't exist yet
      Component = null;
    }
  });
});
```

## Import Path Rules

### NEVER Guess Module Paths

```typescript
// ❌ WRONG - Guessing at module structure
import { sharedController } from '@controllers/my-feature';
import { MyModel } from '@models/my-feature';

// ✅ CORRECT - Use exact file paths
import { sharedMyFeatureController } from '@controllers/my-feature/my-feature.controller';
import { MyFeatureModel } from '@models/my-feature/my-feature.model';
```

### Controller Imports

```typescript
// ❌ WRONG
import { PolicyBuilderController } from '@controllers/policy-builder';

// ✅ CORRECT - Controllers are in their own files
import { sharedPolicyBuilderController } from '@controllers/policy-builder/policy-builder.controller';
```

### Model Imports

```typescript
// ❌ WRONG
import { PolicyModel } from '@models/policy';

// ✅ CORRECT - Models follow naming convention
import { PolicyModel } from '@models/policy/policy.model';
```

## Common Import Errors and Fixes

### Error: Cannot use 'await' outside async function

```typescript
// ❌ WRONG
describe('test', () => {
  const module = await import('./file');
});

// ✅ CORRECT
describe('test', () => {
  let module: any;
  
  beforeEach(async () => {
    module = await import('./file').catch(() => null);
  });
});
```

### Error: Module not found

```typescript
// ❌ WRONG - Let it crash
import { Component } from './non-existent-file';

// ✅ CORRECT - Handle gracefully
let Component: any;
beforeEach(async () => {
  try {
    const mod = await import('./non-existent-file');
    Component = mod.Component;
  } catch {
    Component = null;
  }
});
```

### Error: Cannot read property of undefined

```typescript
// ❌ WRONG - Accessing without checking
const { MyComponent } = await import('./file');
render(<MyComponent />); // Crashes if MyComponent is undefined

// ✅ CORRECT - Check before use
if (MyComponent) {
  render(<MyComponent />);
} else {
  expect(MyComponent).toBeNull();
}
```

## Testing Strategy for Non-Existent Code

1. **Start with Export Tests**: Verify the module exports what you expect
2. **Guard All Usage**: Always check if imports succeeded before using them
3. **Explicit Expectations**: Make it clear when something should be null (red phase)
4. **Progressive Enhancement**: Add more specific tests as implementation progresses

## Checklist

Before submitting test files:

- [ ] All imports are wrapped in try-catch or use dynamic imports
- [ ] No `await` outside of async functions
- [ ] All imported values are checked before use
- [ ] Import paths follow exact project conventions
- [ ] Tests handle both success and failure cases for imports
- [ ] No assumptions about module structure