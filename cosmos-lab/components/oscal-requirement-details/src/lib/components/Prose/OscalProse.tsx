import domPurify from 'dompurify';
import { useMemo } from 'react';
import { styled } from 'styled-components';
import { dimension4x } from '@cosmos/constants/tokens';
import { removeMarkdownLinksCallback } from '../../helpers/remove-markdown-links-callback.helper';
import { splitProseByPlaceholders } from '../../helpers/split-prose-by-placeholders.helper';
import { OscalParameter } from '../Parameter/OscalParameter';

const StyledSpan = styled.span`
    font-size: ${dimension4x};
    line-height: 2;

    & * {
        color: inherit;
        line-height: inherit;
    }
`;

interface Props {
    children: string;
}

export const OscalProse = ({ children }: Props): JSX.Element => {
    const childrenSplit = useMemo(
        () =>
            splitProseByPlaceholders(children)
                // remove [text](#link)
                .map(removeMarkdownLinksCallback)
                // unescape \" => "
                .map((text: string) =>
                    domPurify.sanitize(text).replaceAll('\\"', '"'),
                ),
        [children],
    );

    return (
        <StyledSpan data-testid="OscalProse" data-id="9Eptn__B">
            {childrenSplit.map((segment, i) => (
                // eslint-disable-next-line react/no-array-index-key --  the index is good enough for this case
                <OscalParameter key={i} data-id="Is4sBbED">
                    {segment}
                </OscalParameter>
            ))}
        </StyledSpan>
    );
};
