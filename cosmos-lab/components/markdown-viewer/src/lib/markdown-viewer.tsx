import Markdown from 'markdown-to-jsx';
import { Stack } from '@cosmos/components/stack';
import { generateMarkdownOptions } from './constants/generate-markdown-options';

export interface MarkdownViewerProps {
    children: string;
    allowParsingRawHTML?: boolean;
    'data-id': string;
}

/**
 * The MarkdownViewer component renders markdown content with custom styling and interactive elements like links and lists.
 *
 * 🚧 Currently no Figma link.
 */
export const MarkdownViewer = ({
    children,
    allowParsingRawHTML = false,
    'data-id': dataId,
}: MarkdownViewerProps): React.JSX.Element => {
    // This is used to be able to have line breaks in the markdown
    const formatted = children.replaceAll('\n', '  \n');

    return (
        <Stack
            direction="column"
            gap="4x"
            data-testid="MarkdownViewer"
            data-id={dataId}
        >
            <Markdown
                options={generateMarkdownOptions({ allowParsingRawHTML })}
            >
                {formatted}
            </Markdown>
        </Stack>
    );
};
