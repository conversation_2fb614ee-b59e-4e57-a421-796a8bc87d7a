import type { MarkdownToJSX } from 'markdown-to-jsx';
import React from 'react';
import { Text } from '@cosmos/components/text';
import { ListItem, ListRoot } from '@cosmos-lab/components/list';
import { MarkdownViewerLink } from '../components/markdown-viewer-link.component';

export const generateMarkdownOptions = ({
    allowParsingRawHTML = false,
}: {
    allowParsingRawHTML?: boolean;
}): MarkdownToJSX.Options => {
    return {
        wrapper: React.Fragment,
        disableParsingRawHTML: !allowParsingRawHTML,
        overrides: {
            h1: {
                component: Text,
                props: {
                    as: 'h1',
                    type: 'headline',
                    size: '600',
                },
            },
            h2: {
                component: Text,
                props: {
                    as: 'h2',
                    type: 'headline',
                    size: '500',
                },
            },
            h3: {
                component: Text,
                props: {
                    as: 'h3',
                    type: 'headline',
                    size: '400',
                },
            },
            h4: {
                component: Text,
                props: {
                    as: 'h4',
                    type: 'headline',
                    size: '300',
                },
            },
            h5: {
                component: Text,
                props: {
                    as: 'h5',
                    type: 'headline',
                    size: '200',
                },
            },
            h6: {
                component: Text,
                props: {
                    as: 'h6',
                    type: 'headline',
                    size: '100',
                },
            },
            p: {
                component: Text,
                props: {
                    as: 'p',
                    allowBold: true,
                },
            },
            span: {
                component: Text,
                props: {
                    as: 'span',
                    allowBold: true,
                },
            },
            a: {
                component: MarkdownViewerLink,
            },
            ul: {
                component: ListRoot,
                props: {
                    type: 'unordered',
                },
            },
            ol: {
                component: ListRoot,
                props: {
                    type: 'ordered',
                },
            },
            li: {
                component: ListItem,
            },
        },
    };
};
