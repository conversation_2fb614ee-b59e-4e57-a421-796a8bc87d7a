import type { ButtonProps } from '@cosmos/components/button';
import { Modal } from '@cosmos/components/modal';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
/**
 * The ConfirmationModal component provides a consistent modal for confirming user actions.
 *
 * [ConfirmationModal in Figma](https://www.figma.com/design/YBybPS6EnNlfNl88DfUGCS/Cosmos--Stickers?node-id=7110-19778&m=dev).
 */
export const Confirmation = observer(
    ({
        title,
        body,
        confirmText = 'Yes',
        onConfirm,
        cancelText = 'No, go back',
        onCancel,
        type = 'primary',
        isLoading,
    }: {
        confirmText: string;
        onConfirm: () => void;
        cancelText?: string;
        onCancel?: () => void;
        title: string;
        body: string;
        type: 'primary' | 'danger';
        isLoading?: () => boolean;
        isDisabled?: () => boolean;
    }): React.JSX.Element => {
        const loading = isLoading?.() ?? false;

        const rightActionStack: ButtonProps[] = [];

        if (onCancel) {
            rightActionStack.push({
                label: cancelText,
                level: 'secondary',
                cosmosUseWithCaution_isDisabled: loading,
                onClick: () => {
                    onCancel();
                },
            });
        }

        rightActionStack.push({
            label: confirmText,
            level: 'primary',
            colorScheme: type,
            isLoading: loading,
            onClick: () => {
                onConfirm();
            },
        });

        return (
            <>
                <Modal.Header
                    closeButtonAriaLabel={t`Close modal`}
                    title={title}
                    onClose={
                        onCancel
                            ? () => {
                                  onCancel();
                              }
                            : undefined
                    }
                />
                <Modal.Body>
                    <Text colorScheme="neutral">{body}</Text>
                </Modal.Body>
                <Modal.Footer rightActionStack={rightActionStack} />
            </>
        );
    },
);
